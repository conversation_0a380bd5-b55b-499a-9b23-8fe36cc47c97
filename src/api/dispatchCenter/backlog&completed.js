import { getRequest, postRequestBody3, getRequestByValue, putRequest, postRequestBody } from '@/utils/request'
import getBaseUrl from "@/utils/baseUrl";

const baseURL = getBaseUrl();
const url = {
    todoList: `${baseURL}workflow/process/todoList`,
    dispatchTodoList: `${baseURL}workflow/dispatch/todoList`,
    finishedList: `${baseURL}workflow/process/finishedList`,
    dispatchFinishedList: `${baseURL}workflow/dispatch/finishedList`,
    totalList: `${baseURL}workflow/workStation/pre/totalList`,
    statistics: `${baseURL}workflow/workStation/statistics`,
    category: `${baseURL}workflow/category/list`,
    todoExport: `${baseURL}workflow/dispatch/todoList/export`,
    finishedExport: `${baseURL}workflow/dispatch/finishedList/export`,
    totalExport: `${baseURL}workflow/workStation/pre/totalList/export`,
    getOwnerUserList: `${baseURL}workflow/workStation/getOwnerUserList`
}

// 获取售前待办流程表格数据接口
export const getBacklogData = (data) => getRequest(url.todoList, data)
// 获取售前多模块待办流程表格数据
export const getDispatchTodoList = (data) => getRequest(url.dispatchTodoList, data)
// 获取售前已办流程表格数据接口
export const getCompletedData = (data) => getRequest(url.finishedList, data)
// 获取售前多模块已办流程表格数据
export const getDispatchCompletedData = (data) => getRequest(url.dispatchFinishedList, data)

// 获取售前表格全部数据接口
export const getTotalData = (data) => getRequest(url.totalList, data)

// 获取统计数据接口
export const getStatisticsData = (data) => getRequest(url.statistics, data)
// 获取流程分类接口
export const getClassificationData = (data) => getRequest(url.category, data)

// 调度审批待办导出
export const processDispatchTodoListExport = (params) => getRequest(url.todoExport, params)

// 调度审批已办导出
export const processDispatchFinishedListExport = (params) => getRequest(url.finishedExport, params)

// 售前全部导出
export const processPreTotalListExport = (params) => getRequest(url.totalExport, params)

// 获取自有能力方列表
export const getOwnerUserList = (params) => getRequest(url.getOwnerUserList, params)