<template>
  <div class="">
    <div class="searchInfo flex" :class="switchOnOff ? 'AIBg' : 'commonBg'">
      <div class="vocationPull" style="flex: 1; height: 56px">
        <div class="switch">
          <div class="AIlogo"></div>
          <a-switch
            checked-children="on"
            un-checked-children="off"
            v-model:checked="switchOnOff"
          />
        </div>
        <a-config-provider
          :locale="zhCN"
          :getPopupContainer="(triggerNode) => triggerNode.parentNode"
        >
          <div class="line"></div>

          <div class="lines"></div>
          <a-input
            v-model:value="name"
            class="inputClass"
            allow-clear
            height="56px"
            @keyup.enter="seekContent"
            placeholder="请输入方案名称、标签等关键词进行检索"
          />
          <voiceRecorder
            v-if="switchOnOff"
            :isTranslating="isTranslating"
            :canBtnUse="canBtnUse"
            @audioReady="handleAudio"
          />
          <div
            class="seekInfo"
            :class="switchOnOff ? 'AIbtn' : 'commonBtn'"
            @click="seekContent"
          >
            <img src="@/assets/images/home/<USER>" />
            <div>搜索</div>
          </div>
          <!-- <div class="AISearchLogo pointer" @click="getAIList()">
            <img src="@/assets/images/home/<USER>" alt="">
          </div> -->
        </a-config-provider>
      </div>
    </div>
    <div class="selectData" v-if="!switchOnOff">
      <!--<div :class="['selcet_box', { showMore: showScense == 'provider' }]">
        <div class="left_select">方案分类：</div>
        <div class="right_select" id="getWidth">
          <span
            v-for="(item, key) in tabList"
            :class="{ activeBtn: activeKey === item.value }"
            :key="key"
            @click="providerBtn(item)"
          >
            {{ item.label }}
          </span>
        </div>
        <span
          class="more flex"
          v-if="tabList.length > 8 && morePro"
          @click="showMore('provider')"
          >更多 <img src="@/assets/images/solution/home/<USER>" alt=""
        /></span>
        <span
          class="more flex"
          v-if="tabList.length > 8 && !morePro"
          @click="showless('provider_less')"
          >收起 <img src="@/assets/images/solution/home/<USER>" alt=""
        /></span>
      </div>-->
      <!--<div class="second_line">
        <span @click="toggleShowLabel">展开</span>
        <div class="img_box">
          <img
            src="@/assets/images/solution/home/<USER>"
            alt=""
            class="first"
          />
          <img src="@/assets/images/solution/home/<USER>" alt="" class="sec" />
        </div>
      </div>-->
      <div :class="[ 'selcet_box', { showMore: showScense == 'label' && showIndex === index }, ]" v-for="(val, index) in vocationList" :key="index">
        <div class="left_select">{{ val.label }}：</div>
        <div :class="[ 'right_select', { showHidden: showScense == 'label' && showIndex === index }]">
          <span v-if="val.label == '行业'" v-for="(value, key1) in val.children" :key="key1" :class="{ activeBtn: activeKey === value.value }" :style="{ height:(showLast && (showId == value.value) ? '89px':'49px') }">
            <span @click="providerBtn(value, 'default', index)">
            	{{ value.label }}
            </span>
            <div class="last_data" v-if="showLast && showId == value.value">
              <span v-for="(e, i) in value.children" @click="providerBtn(e, 'last', i)" :class="{ activeBtn: providerSelect.indexOf(e.label) > -1 }" :key="i">
              	{{ e.label }}
              </span>
            </div>
          </span>
          <span v-else v-for="(value, key2) in val.children" :key="key2" :class="{ activeBtn: selectList.indexOf(value.label) > -1 }" :style="{ height:(showLast && (showId == value.value) ? '89px':'49px') }">
            <span @click="labelSelect(value, 'default', index)">
            	{{ value.label }}
            </span>
            <div class="last_data" v-if="showLast && showId == value.value">
              <span v-for="(e, i) in value.children" @click="labelSelect(e, 'last')" :class="{ activeBtn: selectList.indexOf(e.label) > -1 }" :key="i">
              	{{ e.label }}
              </span>
            </div>
          </span>
        </div>
        <span
          class="more flex"
          v-if="val.children && val.children.length > 8 && showIndex !== index"
          @click="showMore('label', index)"
          >更多<img src="@/assets/images/solution/home/<USER>" alt=""
        /></span>
        <span
          class="more flex"
          v-if="val.children &&val.children.length > 8 && showIndex === index"
          @click="showless('label_less', index)"
          >收起<img src="@/assets/images/solution/home/<USER>" alt=""
        /></span>
      </div>
      <div v-if="!showLabel" class="second_line">
        <span @click="toggleShowLabel">收起</span>
        <div class="img_box">
          <img
            src="@/assets/images/solution/home/<USER>"
            class="first"
            alt=""
          />
          <img src="@/assets/images/solution/home/<USER>" class="sec" alt="" />
        </div>
      </div>
      <div class="select_boot flex">
        <div>
          已选条件：
          <span v-if="providerSelect.length>0">行业：</span>
          <span v-for="(val, index) in providerSelect" :key="index">
          	<span style="margin-left: 8px">
              {{ val }}
            </span>
            <img
              src="@/assets/images/solution/home/<USER>"
              alt=""
              style="width: 16px; height: 16px; cursor: pointer"
              @click="deleteSelect(val, index, 'pro')"
            />
          </span>
          <span class="label" v-if="selectList.length > 0">标签：</span>
          <span v-for="(item, index) in selectList" :key="key">
            <span style="margin-left: 8px">
              {{ item }}
            </span>
            <img
              src="@/assets/images/solution/home/<USER>"
              alt=""
              style="width: 16px; height: 16px; cursor: pointer"
              @click="deleteSelect(item, index)"
            />
          </span>
        </div>
        <div class="right_con">
          共找到 <span>{{ totalItemCount }}</span> 条结果
        </div>
      </div>
    </div>
    <div class="tabContent">
      <div v-if="tableList && tableList.length > 0" style="width: 100%">
        <div class="AITips flex align-center" v-if="!showPagination">
          <img
            style="width: 40px; height: 40px; margin-right: 10px"
            src="../../../../../assets/images/AI/ai.png"
            alt=""
          />
          <div class="words">以下是AI助手为您找到的相关结果</div>
        </div>
        <div class="cardContent">
          <div class="card_total flex-1">
            <template v-for="(item, index) in tableList" :key="index">
              <div
                :class="[
                  'card_content',
                  {
                    cardActive: cardActive == index,
                    rightActive: index % 2 != 0,
                    cardObvious: index < 2 && tableList.length < 3,
                    bottomLine:
                      (index == tableList.length - 1 ||
                        index == tableList.length - 2) &&
                      index > 1,
                  },
                ]"
                @mouseenter="contentColor(index)"
                @mouseleave="contentLeave"
                @click="proDetail(item)"
              >
                <div style="display: flex; margin: 24px">
                  <div>
                    <a-image
                      :width="168"
                      :height="105"
                      :preview="false"
                      v-if="item.logo"
                      :src="`${item.logo}`"
                      style="width: 168px; height: 105px"
                    />
                    <div
                      v-else
                      style="
                        width: 168px;
                        height: 105px;
                        text-align: center;
                        position: relative;
                      "
                      :style="backgroundStyles()"
                    >
                      <p
                        style="
                          font-weight: 700;
                          display: block;
                          color: #0a7aee;
                          position: absolute;
                          left: 50%;
                          top: 50%;
                          transform: translate(-50%, -50%);
                        "
                      >
                        {{ item.name }}
                      </p>
                    </div>
                  </div>
                  <div class="card_center">
                    <div class="card_text">
                      <div class="card_tag">
                        <!--<a-tag color="#D7E6FF">{{ item.categoryName }}</a-tag>-->
                        <div class="card_title">{{ item.name }}</div>
                      </div>
                      <a-tag :bordered="false" class="cityStyle">{{
                        item.provider
                      }}</a-tag>
                    </div>
                    <div class="card_des">
                      {{ item.description }}
                    </div>
                    <div class="flex" style="justify-content: space-between">
                      <div class="flex">
                        <a-tag
                          color="#D7E6FF"
                          v-if="item.labelName && item.labelName[0]"
                          style="
                            display: block;
                            color: rgba(0, 0, 0, 0.45);
                            background-color: transparent;
                            border: 1px solid #d9d9d9;
                            line-height: 17px;
                          "
                          >{{ item.labelName[0] }}</a-tag
                        >
                        <a-tag
                          color="#D7E6FF"
                          v-if="item.labelName && item.labelName[1]"
                          style="
                            display: block;
                            color: rgba(0, 0, 0, 0.45);
                            background-color: transparent;
                            border: 1px solid #d9d9d9;
                            line-height: 17px;
                          "
                          >{{ item.labelName[1] }}</a-tag
                        >
                      </div>
                    </div>
                    <div
                      style="
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                      "
                    >
                      <div
                        style="
                          display: flex;
                          align-items: center;
                          justify-content: space-between;
                          margin-right: 5px;
                        "
                      >
                        <div
                          style="
                            display: flex;
                            align-items: center;
                            margin-right: 18px;
                          "
                        >
                          <img
                            src="@/assets/images/home/<USER>"
                            style="width: 16px; height: 16px"
                          />
                          <span
                            style="font-size: 12px; color: rgba(0, 0, 0, 0.45)"
                            v-if="item.viewCount"
                            >{{ item.viewCount }}</span
                          >
                          <span v-else>-</span>
                        </div>
                        <div style="display: flex; align-items: center">
                          <img
                            src="@/assets/images/home/<USER>"
                            style="width: 16px; height: 16px"
                          />
                          <span
                            style="font-size: 12px; color: rgba(0, 0, 0, 0.45)"
                            v-if="item.downloadCount"
                            >{{ item.downloadCount }}</span
                          >
                          <span v-else>-</span>
                        </div>
                      </div>
                      <div>
                        <button
                          class="cart-button"
                          disabled
                          v-if="item.addCart"
                        >
                          <span class="add" style="color: rgba(0, 0, 0, 0.9)">
                            &nbsp;已加入</span
                          >
                        </button>
                        <button
                          class="cart-button pointer"
                          v-else
                          @click.stop="add(item.id)"
                        >
                          <img
                            v-if="!item.addCart"
                            class="add-icon"
                            src=" @/assets/images/AI/isadded.png"
                          /><span class="add"> &nbsp;加入预选</span>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>
        <div class="layPage" v-if="showPagination">
          <a-pagination
            v-model:pageSize="pageItemSize"
            v-model:current="currentPage"
            :pageSizeOptions="pageSizeOptions"
            show-quick-jumper
            show-size-changer
            :total="totalItemCount"
            @change="pageChange"
            @showSizeChange="sizeChange"
            class="mypage"
          />
        </div>
      </div>
      <div v-if="tableList.length == 0 && !loadingShow" class="emptyPhoto">
        <img src="@/assets/images/home/<USER>" />
      </div>
      <div class="loading" v-show="loadingShow">
        <a-spin />
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, ref, watch } from "vue";
import { useRouter } from "vue-router";
import {
  getTradeList,
  getsolutionList,
  getLabelTreeList,
} from "@/api/solutionNew/home";
import { useHomeStore } from "@/store";
import bac from "@/assets/images/noDataBac.png";
import { addShoppingCart } from "@/api/combine/shoppingCart.js";
import voiceRecorder from "@/components/voiceRecorder/voiceRecorder.vue";
import zhCN from "ant-design-vue/es/locale/zh_CN";
import { getMakeUrl } from "@/utils/getUrl";
import eventBus from "@/utils/eventBus";
import { getSceneSchemeList } from "@/api/moduleList/home";
import { AISearch, AIvoice } from "@/api/AI/ai.js";
export default defineComponent({
  components: {
    voiceRecorder,
  },
  props: {
    sourceType: {
      type: String,
      default: "1",
    },
  },
  setup(props, { emit }) {
    const baseURL = getMakeUrl();
    const vocation = ref("");
    const data = reactive({
      name: "",
      moment: "",
      backgroundImage: bac,
      sourceType: props.sourceType,
      loadingShow: true,
      showLabel: true,
      activeKey: "",
      cardActive: "-1",
      pageSizeOptions: ["10", "20", "30", "50"],
      totalItemCount: 0,
      tabList: [],
      vocationList: [],
      tableList: [],
      totalNum: 0,
      totalItemCount1: 0,
      currentPage: 1,
      pageItemSize: 10,
      labelIdlist: [],
      showLast: false,
      showId: undefined,
      selectList: [],
      selectListNew: [],
      selectListOld: [],
      showMore: false,
      showScense: "",
      morePro: true,
      providerSelect: [],
      showIndex: "",
      showPagination: true,
      switchOnOff: true,
      isTranslating: false,
      canBtnUse: false,
      AISearchType: 2,
    });
    watch(
      () => props.sourceType,
      (val) => {
        if (val == "1") {
          data.AISearchType = 2;
        } else if (val == "2") {
          data.AISearchType = 7;
        }
        data.sourceType = val;
        data.activeKey = "";
        data.name = "";
        data.labelIdlist = [];
        data.providerSelect = [];
        data.selectList = [];
        data.selectListNew = [];
        data.selectListOld = [];
        data.currentPage = "1";
        data.pageItemSize = "10";
        getList();
      }
    );
    const getList = () => {
      if (data.sourceType === "2") {
        let pageParams = {
          pageNo: data.currentPage,
          pageSize: data.pageItemSize,
          keyword: data.name,
          //industryIdList: data.activeKey,
          labelIds: data.labelIdlist,
          type: 5,
        };
        data.loadingShow = true;
        getSceneSchemeList(pageParams)
          .then((res) => {
            data.showPagination = true;
            data.loadingShow = false;
            data.tableList = [];
            data.tableList = res.data.rows;
            data.totalItemCount = res.data.totalRows;
            data.tableList.map((item) => {
              //item.label = item.label.split(",");
              //item.categoryName = item.name;
              item.description = item.summary;
              item.logo = item.image;
            });

            if (data.activeKey == "") {
              data.totalItemCount1 = res.data.totalRows;
            }
          })
          .catch((error) => {
            data.loadingShow = false;
          });
      } else {
        let pageParams = {
          pageNo: data.currentPage,
          pageSize: data.pageItemSize,
          keyword: data.name,
          //industryIdList: data.activeKey,
          labelIds: data.labelIdlist,
        };
        data.loadingShow = true;
        getsolutionList(pageParams)
          .then((res) => {
            data.showPagination = true;
            data.loadingShow = false;
            data.tableList = [];
            data.tableList = res.data.rows;
            data.totalItemCount = res.data.totalRows;
            data.tableList.forEach((item) => {
              item.labelName = item.labelName.split(",");
            });
            if (data.activeKey == "") {
              data.totalItemCount1 = res.data.totalRows;
            }
          })
          .catch((error) => {
            data.loadingShow = false;
          });
      }
    };
    getList();
    const providerList = () => {
      getLabelTreeList().then((res) => {
        data.vocationList = res.data.map((item) => ({
          label: item.name,
          value: item.id,
          length: item.children ? item.children.length : 0,
          children: item.children
            ? item.children.map((child) => ({
                label: child.name,
                value: child.id,
                children: child.children
                  ? child.children.map((ele) => ({
                      label: ele.name,
                      value: ele.id,
                    }))
                  : undefined,
              }))
            : undefined,
        }));
        console.log(data.vocationList);
        //      data.vocationList = data.vocationList.slice(1);
      });
    };
    providerList();
    const seekContent = () => {
      data.currentPage = 1;
      if (data.switchOnOff) {
        getAIList();
      } else {
        getList();
      }
    };

    const add = (id) => {
      let addParams = {
        schemeId: id,
        type: "1",
      };
      if (data.sourceType == "1") {
        addParams.type = "1";
      } else {
        addParams.type = "3";
      }
      addShoppingCart(addParams).then((res) => {
        if (data.showPagination) {
          getList();
        } else {
          getAIList();
        }
        eventBus.emit("cartRefresh");
      });
    };
    const tabChange = (val) => {
      if (val.value !== data.activeKey) {
        data.activeKey = vocation.value = val.value;
        data.currentPage = 1;
        getList();
      }
    };
    const contentColor = (index) => {
      data.cardActive = index;
    };
    const router = useRouter();
    const proDetail = (val) => {
      if (data.sourceType == "1") {
        router.push({
          query: {
            id: val.id,
          },
          name: "solveDetailNew",
        });
      } else {
        router.push({
          query: {
            id: val.id,
            activeBtn: 2,
          },
          name: "applyNew",
        });
      }
    };
    const contentLeave = () => {
      data.cardActive = "-1";
    };
    const pageChange = (page, pageSize) => {
      data.currentPage = page;
      getList();
    };
    const sizeChange = (current, size) => {
      data.pageItemSize = size;
      getList();
    };
    const labelChange = (val) => {
      data.labelIdlist = val.join(",");
    };
    const getTarde = () => {
      let tradeParams = {};
      getTradeList(tradeParams).then((result) => {
        result.data.map((item) => {
          data.tabList.push({
            label: item.name,
            value: item.id,
          });
        });
      });
    };
    const labelSelect = (value, type = "default", index) => {
      if (value.children && type !== "last") {
      	if (data.selectList.includes(value.label)) {
      		data.showLast = false;
      	} else {
      		data.showLast = true;
      	}
      	data.showId = value.value;
	      data.showScense = "label";
	      data.showIndex = index;
      }
        if (data.selectList.includes(value.label)) {
          const index = data.selectList.findIndex(
            (item) => item === value.label
          );
          if (index !== -1) {
            data.selectList.splice(index, 1);
          }
          const index1 = data.selectListNew.findIndex(
            (item) => item === value.value
          );
          if (index1 !== -1) {
            data.selectListNew.splice(index, 1);
          }
        } else {
          data.selectList.push(value.label);
          data.selectListNew.push(value.value);
        }
        data.selectList = data.selectList.filter((value, index, self) => {
          return self.indexOf(value) === index;
        });
        data.selectListNew = data.selectListNew.filter((value, index, self) => {
          return self.indexOf(value) === index;
        });
        if(data.selectListOld.length>0){
        	data.labelIdlist = [...data.selectListNew, data.selectListOld[data.selectListOld.length-1]]
        } else {
        	data.labelIdlist = data.selectListNew;
        }
        data.labelIdlist = data.labelIdlist.join(",");
        getList();
//    }
    };
    const providerBtn = (value, type = "default", index) => {
    	if (value.children && type !== "last") {
      	data.showId = value.value;
	      data.showScense = "label";
	      data.showIndex = index;
    	}
    	if(type != 'last'){
    		data.activeKey = vocation.value = value.value;
	      if (data.providerSelect.includes(value.label)) {
	        data.providerSelect = [];
	        data.selectListOld = [];
	        data.activeKey = "";
	        data.showLast = false;
	      } else {
	      	if(value.children) {
	      		data.showLast = true;
	      	} else {
	      		data.showLast = false;
	      	}
	      	data.providerSelect = [];
	      	data.selectListOld = [];
	        data.providerSelect.push(value.label);
	        data.selectListOld.push(value.value);
	        data.providerSelect = data.providerSelect.filter(
	          (value, index, self) => {
	            return self.indexOf(value) === index;
	          }
	        );
	        data.selectListOld = data.selectListOld.filter(
	          (value, index, self) => {
	            return self.indexOf(value) === index;
	          }
	        );
	      }
    	} else {
    		if (data.providerSelect.includes(value.label)) {
	        data.providerSelect = [data.providerSelect[0]];
	        data.selectListOld = [data.selectListOld[0]];
	      } else {
	      	data.providerSelect = [data.providerSelect[0]];
	        data.providerSelect.push(value.label);
	        data.providerSelect = data.providerSelect.filter(
	          (value, index, self) => {
	            return self.indexOf(value) === index;
	          }
	        );
	        data.selectListOld = [data.selectListOld[0]];
	        data.selectListOld.push(value.value);
	        data.selectListOld = data.selectListOld.filter(
	          (value, index, self) => {
	            return self.indexOf(value) === index;
	          }
	        );
	      }
    	}
    	if(data.selectListOld.length>0){
        data.labelIdlist = [...data.selectListNew, data.selectListOld[data.selectListOld.length-1]]
      } else {
       	data.labelIdlist = data.selectListNew;
      }
      data.labelIdlist = data.labelIdlist.join(",");
      data.currentPage = 1;
      getList();
    };
    const deleteSelect = (val, index, type) => {
      if (type == "pro") {
      	if(index == 1){
      		data.providerSelect = [data.providerSelect[0]];
      		data.selectListOld = [data.selectListOld[0]];
      	} else if(index == 0){
      		data.providerSelect = [];
      		data.selectListOld = [];
        	data.activeKey = "";
        	data.showLast = false;
      	}
      }
      data.selectList.splice(index, 1);
      data.selectListNew.splice(index, 1);
      if(data.selectListOld.length>0){
        data.labelIdlist = [...data.selectListNew, data.selectListOld[data.selectListOld.length-1]]
      } else {
       	data.labelIdlist = data.selectListNew;
      }
      data.labelIdlist = data.labelIdlist.join(",");
      getList();
    };
    const showMore = (type, index) => {
      if (type == "provider") {
        data.showScense = type;
        data.morePro = false;
      } else {
        data.showIndex = index;
        data.showScense = type;
      }
    };
    const showless = (type, index) => {
      if (type == "provider_less") {
        data.showScense = type;
        data.morePro = true;
      } else {
        data.showIndex = "";
        data.showScense = type;
        console.log(index, `index`, data.showIndex);
      }
    };
    getTarde();
    const getAIList = () => {
      console.log("456");
      data.loadingShow = true;
      AISearch({
        question: data.name,
        type: data.AISearchType,
      }).then((res) => {
        data.loadingShow = false;
        if (res.code == 200) {
          data.showPagination = false;
          data.tableList = [];
          data.tableList = res.data;
          data.totalItemCount = res.data ? res.data.length : 0;
          data.tableList.map((item) => {
            item.labelName = item.labelName.split(",");
          });
        }
      });
    };
    // eventBus.on("solutionAIRefresh", getAIList);
    const refreshList = () => {
      if (data.showPagination) {
        getList();
      } else {
        getAIList();
      }
    };
    eventBus.on("solutionAllRefresh", refreshList);
    const toggleShowLabel = () => {
      data.showLabel = !data.showLabel;
    };
    // 语音输入
    const handleAudio = (audioBlob) => {
      const formData = new FormData();
      formData.append("file", audioBlob, "recording.wav"); // 上传文件
      console.log("ssssssss", formData);
      // 调用 AIvoice 函数并传递音频数据
      data.isTranslating = true;
      data.canBtnUse = true;
      AIvoice(formData).then((res) => {
        data.isTranslating = false;
        data.canBtnUse = false;
        console.log(res);
        if (res.code == 200) {
          data.name = res.msg;
          // seekContent()
        }
      });
    };
    const backgroundStyles = () => {
      return {
        backgroundImage: `url(${data.backgroundImage})`, // 使用模板字符串来插入变量
        backgroundRepeat: "no-repeat",
        backgroundSize: "cover",
      };
    };
    const counterStore = useHomeStore();
    return {
      ...toRefs(data),
      vocation,
      labelSelect,
      backgroundStyles,
      counterStore,
      toggleShowLabel,
      providerBtn,
      showMore,
      showless,
      deleteSelect,
      tabChange,
      contentColor,
      sizeChange,
      contentLeave,
      add,
      proDetail,
      router,
      pageChange,
      zhCN,
      seekContent,
      baseURL,
      labelChange,
      getAIList,
      refreshList,
      handleAudio,
    };
  },
});
</script>

<style lang="scss" scoped src="./tableList.scss"></style>

<style lang="scss">
.ant-select:not(.ant-select-customize-input) .ant-select-selector {
  border: none;
}

:deep(.ant-cascader-input.ant-input) {
  border: none !important;
}

.mypage {
  .ant-pagination {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-select-selector {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-pagination-item-active {
    background: #007eff;
  }

  .ant-pagination-item-active a {
    color: #ffffff;
  }

  .ant-pagination-item-active:focus a,
  .ant-pagination-item-active:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-active a:focus,
  .ant-pagination-item-active a:hover {
    color: #ffffff;
  }

  .ant-pagination-item:focus,
  .ant-pagination-item:hover {
    background: #007eff;
  }

  .ant-pagination-item:focus a,
  .ant-pagination-item:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-disabled:hover,
  .ant-pagination-item-disabled:focus {
    background-color: #ffffff;
    border-color: #d9d9d9;

    a {
      color: rgba(0, 0, 0, 0.25);
    }
  }

  .ant-pagination-options-size-changer.ant-select {
    margin-right: 0;
  }

  .ant-pagination-options-quick-jumper {
    margin-left: 8px;
  }
}

.AISearchLogo {
  width: 190px;
  height: 80px;

  img {
    width: 100%;
    height: 100%;
  }
}
</style>
