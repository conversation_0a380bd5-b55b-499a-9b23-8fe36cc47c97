<template>
  <div class="box">
    <div class="loading-overlay" v-if="loadShow">
      <a-spin :spinning="loadShow" tip="附件加载中"></a-spin>
    </div>
    <div class="top_nav">
      <div class="left_nav">
        <span class="title" @click="back">案例库</span>
        <span class="title"> / </span>
        <span class="current">{{
          detailData.caseName || detailData.name
        }}</span>
      </div>
      <div class="right_nav">
        <div @click="backLast" style="margin-right: 20px">返回</div>
      </div>
    </div>
    <div style="margin-top: 50px">
      <div class="banner">
        <div class="text">{{ detailData.caseName || detailData.name }}</div>
        <div class="contentCase">
          <div class="flex align-center" style="margin-right: 60px">
            <img
              src="@/assets/images/prefecture/scale.png"
              width="12px"
              height="12px"
              style="margin-right: 6px; margin-bottom: 0"
            />
            <div class="store">
              规模：
              <span style="font-weight: bold; font-size: 12px; color: #236cff">
                {{ dataDeal(detailData.projectAmount) }}
              </span>
            </div>
          </div>

          <div class="flex align-center">
            <img
              src="@/assets/images/prefecture/time.png"
              width="12px"
              height="12px"
              style="margin-right: 6px; margin-bottom: 0"
            />
            <div class="time">
              时间：
              <span style="font-weight: bold; font-size: 12px; color: #236cff">
                {{ timeDeal(detailData.projectTime) }}
              </span>
            </div>
          </div>
        </div>

        <div class="lineHead"></div>

        <div class="des">{{ detailData.constructionContent }}</div>
        <div class="imgaeBac">
          <img v-if="detailData.image" :src="`${detailData.image}`" alt="" />
          <div class="detail_bg" v-else></div>
        </div>
        <div
          class="card"
          style="width: 1200px; margin-top: 40px"
          v-if="detailData.citySolution && detailData.citySolution.length > 0"
        >
          <div class="tab_content">
            <img
              src="@/assets/images/solution/detail/leftIcon.png"
              style="width: 33px; height: 22px"
              alt=""
            />
            <div class="tit">所属方案</div>
            <img
              src="@/assets/images/solution/detail/rightIcon.png"
              style="width: 33px; height: 22px"
              alt=""
            />
          </div>
          <div
            class="content flex"
            style="width: 960px"
            v-for="item in detailData.citySolution"
          >
            <div
              style="
                width: 370px;
                height: 255px;
                text-align: center;
                position: relative;
              "
              :style="backgroundStyles()"
            >
              <p
                style="
                  font-weight: 700;
                  display: block;
                  color: #1f82c8;
                  position: absolute;
                  left: 50%;
                  top: 50%;
                  transform: translate(-50%, -50%);
                  font-size: 18px;
                "
              >
                {{ item.name }}
              </p>
            </div>
            <div
              class="right"
              @click="toSolution(item)"
              style="height: 255px; cursor: pointer; text-align: left"
            >
              <p
                class="title"
                style="
                  font-weight: bold;
                  font-size: 20px;
                  color: #2e3852;
                  line-height: 23px;
                  margin-bottom: 16px;
                "
              >
                {{ item.name }}
              </p>
              <p
                style="
                  font-weight: 400;
                  font-size: 16px;
                  color: #2e3852;
                  line-height: 28px;
                  display: -webkit-box;
                  -webkit-line-clamp: 5;
                  -webkit-box-orient: vertical;
                  overflow: hidden;
                  text-overflow: ellipsis;
                "
              >
                <a-tooltip overlayClassName="tooltip_class">
                  <template v-if="isShowToolTip(item.description, 165)" #title>
                    {{ item.description }}
                  </template>
                  <span class="right_name">{{ item.description }}</span>
                </a-tooltip>
              </p>
            </div>
          </div>
        </div>
        <div class="containFile">
          <div class="fileContent">
            <div class="list" v-if="detailData.pptPath != null">
              <div class="li">
                <div class="li_box">
                  <div class="left_box">
                    <img
                      src="@/assets/images/prefecture/pptx.png"
                      alt=""
                      style="width: 16px; height: 16px; margin-bottom: 0"
                    />
                    <div class="fileText">{{ detailData.pptName }}</div>
                  </div>
                  <div class="flex">
                    <img
                      src="@/assets/images/prefecture/view.png"
                      @click="fileShow(detailData.pptPath, detailData.pptUrl)"
                      style="width: 22px; height: 22px"
                      class="pointer"
                    />
                    <!-- 下载超限 -->
                    <img
                      src="@/assets/images/prefecture/load.png"
                      @click.stop="
                        downloadBtn(
                          detailData.pptUrl,
                          detailData.pptName,
                          detailData
                        )
                      "
                      style="width: 22px; height: 22px; margin-left: 24px"
                      class="pointer"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <img
        class="top"
        src="../../../assets/images/solution/detail/toTap.png"
        alt=""
        @click="scrollUp"
      />
    </div>
  </div>
  <a-modal
    v-model:visible="showDownloadModal"
    title="提示"
    :mask-closable="false"
    :footer="null"
    :destroyOnClose="true"
    width="550px"
  >
    <promptBox
      @downloadModalCancel="downloadModalCancel"
      @downloadModalConfirm="downloadModalConfirm"
    />
  </a-modal>
  <a-modal
    v-model:visible="showDownloadForm"
    title="新增工单"
    :mask-closable="false"
    :footer="null"
    :destroyOnClose="true"
    width="600px"
  >
    <reviewForm
      @downloadFormCancel="downloadFormCancel"
      @downloadFormConfirm="downloadFormConfirm"
    />
  </a-modal>
</template>
<script lang="ts" setup>
import { isShowToolTip } from "../../../utils/index.js";
import { onMounted, ref } from "vue";
import { getTradeList } from "../../../api/solutionNew/home";
import { useRouter, useRoute } from "vue-router";
import { addShoppingCart } from "../../../api/combine/shoppingCart.js";
import eventBus from "../../../utils/eventBus";
import bac from "@/assets/images/noDataBac.png";
import {
  cancelCollect,
  collect,
  getReviewList,
  getSolveDetail,
  getCaseInfoDetail,
  getCityDetail,
} from "../../../api/moduleList/detail";
import { pptTopdf } from "../../../api/fileUpload/uploadFile.js";
import { message } from "ant-design-vue";
import { getDownCount, getNewDownCount } from "../../../api/solutionNew/detail";
import promptBox from "@/components/promptBox/index.vue";
import reviewForm from "@/components/reviewForm/index.vue";
interface FormState {
  name: string;
  code: string;
  categoryId: number | undefined;
  estimatedAmount: string;
  schemeId: number;
  status: string;
}

const route = useRoute();
onMounted(() => {
  getData();
});
const speakList = ref();
const showDownloadModal = ref(false);
const showDownloadForm = ref(false);
const pageItemSize = ref("3");
const currentPage = ref("1");
const totalItemCount = ref(0);
const viewType = ref("5");
const loadShow = ref(false);
const backgroundImage = ref(bac);
const reviewList = () => {
  let params = {
    sourceId: route.query.id,
    pageSize: pageItemSize.value,
    pageNo: currentPage.value,
    sourceType: "2",
    delStatus: 1,
  };
  getReviewList(params).then((res) => {
    if (res.code == 200) {
      totalItemCount.value = res.data.totalRows;
      speakList.value = res.data.rows.map((item) => ({
        ...item,
        show: item.creatorName === userInfo.realName,
      }));
    }
  });
};
reviewList();
const userInfo = JSON.parse(localStorage.getItem("userInfo"));
const functionKey = ref(1);
const isActive = ref(0);
const collectActive = ref(false);

function change(v) {
  isActive.value = v;
}
const downloadBtn = (e, n, detail) => {
  getNewDownCount({
    businessId: detailData.value.id,
    businessType: 13,
  }).then((res) => {
    if (res.code == 200) {
      if (res.data) {
        const href = e;
        let windowOrigin = window.location.origin;
				let token = localStorage.getItem("token");
				let newHref = href;
        if(href.includes(windowOrigin)){
         	newHref = "/portal" + href.split(windowOrigin)[1]
        }
			  window.open(windowOrigin + newHref + "?token=" + token);
      } else {
        showDownloadModal.value = true;
      }
    }
  });
};
const fileShow = (val, valu) => {
  loadShow.value = true;
  // 转PDF的下载
  pptTopdf({
    filePath: val,
    fileUrl: valu,
  }).then((res) => {
    loadShow.value = false;
    if (res.code == 200) {
      let windowOrigin = window.location.origin;
      let token = localStorage.getItem("token");
      let newHref = res.data;
      if(res.data.includes(windowOrigin)){
        newHref = "/portal" + res.data.split(windowOrigin)[1]
      }
      const newpage = Router.resolve({
        name: "lookPdf",
        query: {
          urlMsg: encodeURIComponent(
          	windowOrigin + newHref + "?token=" + token
          ),
          urlName: val.name,
        },
      });
      window.open(newpage.href, "_blank");
    }
  });
};
const formRef = ref();

const getCurrentAnchor = () => {
  return currentAnchor.value;
};
const currentAnchor = ref("#desc");
const viewId = ref(route.query.id);
const back = () => {
  Router.back();
};
const backLast = () => {
  Router.back();
  return false;
};
// 滚动函数
const scrollUp = () => {
  currentAnchor.value = "#desc";
  getCurrentAnchor();
  isActive.value = 0;
  document.getElementById("layout_content").scrollTo({
    top: 0,
    behavior: "smooth",
  });
};
const nowSolutionId = ref(null);
const detailData = ref({});
const getData = () => {
  if (route.query.id) {
    getCaseInfoDetail(route.query.id).then((res) => {
      // nowSolutionId.value = res.data.solutionId
      detailData.value = [];
      anchorList.value = [];
      res.data.image = res.data.imageUrl.split(",")[0];
      let date = new Date(res.data.createTime);
      let Y = date.getFullYear();
      let M =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      let D =
        (date.getDate() < 10 ? "0" + date.getDate() : date.getDate()) + " ";
      let GMT = Y + "-" + M + "-" + D;
      res.data.createTime = GMT;
      if (res.data.caseList) {
        res.data.caseList.forEach((element) => {
          let text = element.description;
          let caseInfo = {};
          let keywords = {
            name: "项目名称[：:](.*?)\\n",
            size: "规模[：:](.*?)\\n",
            time: "时间[：:](.*?)\\n",
            people: "联系人[：:](.*?)\\n",
            intro: "(案例介绍|项目介绍)[：:](.*)",
          };
          for (let key in keywords) {
            let regex = new RegExp(keywords[key], "s");
            let match = text.match(regex);
            if (match) {
              caseInfo[key] =
                key === "intro"
                  ? (match[2] || "").trim()
                  : (match[1] || "").trim();
            } else {
              caseInfo[key] = "";
              // 如果您不想在找不到匹配时保留该属性，可以取消注释下一行
              // delete caseInfo[key];
            }
          }
          element.caseInfo = caseInfo;
        });
      }

      if (res.data.functionList && res.data.functionList.length != 0) {
        res.data.functionListShow = true;
        anchorList.value.push({
          key: "functionList",
          href: "#functionList",
          title: "核心功能",
        });
      }
      if (res.data.caseList && res.data.caseList.length != 0) {
        res.data.caseListShow = true;
        anchorList.value.push({
          key: "caseList",
          href: "#caseList",
          title: "项目案例",
        });
      }
      if (res.data.pptPath != null && res.data.pptPath != "") {
        anchorList.value.push({
          key: "download",
          href: "#download",
          title: "场景附件",
        });
      }
      detailData.value = res.data;
      detailData.value.citySolution = [];
      let citySolutionList = res.data.citySolutionList || [];
      let solutionListPro = res.data.solutionList || [];
      detailData.value.citySolution = citySolutionList.concat(solutionListPro);
      // console.log(res.data,'===')
      return;
      let solutionIdCity = res.data.solutionCaseList.filter(
        (item) => item.type == 2
      );
      let solutionIdPro = res.data.solutionCaseList.filter(
        (item) => item.type == 1
      );
      solutionIdCity.forEach((item) => {
        getCityDetail(item.solutionId).then((result) => {
          if (result.code == 200) {
            detailData.value.citySolution.push({
              solutionName: result.data.name,
              solutionDesc: result.data.description,
              solutionImg: result.data.logo,
              id: result.data.id,
            });
          }
        });
      });
      solutionIdPro.forEach((item) => {
        getSolveDetail(item.solutionId).then((result) => {
          if (result.code == 200) {
            detailData.value.citySolution.push({
              solutionName: result.data.name,
              solutionDesc: result.data.description,
              solutionImg: result.data.logo,
              id: result.data.id,
            });
          }
        });
      });
    });
  }
};
const backgroundStyles = () => {
  return {
    backgroundImage: `url(${backgroundImage.value})`, // 使用模板字符串来插入变量
    backgroundRepeat: "no-repeat",
    backgroundSize: "cover",
  };
};
const dataDeal = (value: any) => {
  if (value) return value + "（万元）";
  return "-";
};
const timeDeal = (value: any) => {
  if (value) return value;
  return "-";
};
const add = () => {
  addShoppingCart({
    schemeId: route.query.id,
    type: "3",
  }).then(() => {
    getData();
    eventBus.emit("cartRefresh");
  });
};
const tabList = ref([]);
const getTarde = () => {
  let tradeParams = {};
  getTradeList(tradeParams).then((result) => {
    result.data.map((item) => {
      tabList.value.push({
        name: item.name,
        id: item.id.toString(),
      });
    });
  });
};
getTarde();
const isShow = ref("desc");
const anchorList = ref([]);

const collectById = () => {
  if (detailData.value.collect == 1) {
    cancelCollect(route.query.id)
      .then(() => {
        message.success("取消收藏成功");
        getData();
      })
      .catch(() => {
      });
  } else {
    collect(route.query.id)
      .then(() => {
        message.success("收藏成功");
        getData();
      })
      .catch(() => {
      });
  }
  collectActive.value = !collectActive.value;
};
const Router = useRouter();
const handleClick = (e, link) => {
  const href = link.href.replace("#", "");
  e.preventDefault();
  currentAnchor.value = "#" + href;
  let srcolls = document.getElementById(link.href);
  srcolls &&
    srcolls.scrollIntoView({
      block: "center",
      behavior: "smooth",
    });
  isShow.value = href;
};
const toSolution = (e) => {
  //let id = detailData.value.solutionId;
  let arr = detailData.value.solutionCaseList;
  let type = "";
  arr.forEach((item) => {
    if (item.solutionId == e.id) {
      type = item.type;
    }
  });
  let id = e.id;
  if (type == 1) {
    Router.push({
      path: "/solveNew/detailNew",
      query: { id: id },
    });
  } else {
    Router.push({
      name: "cityAreaDetailNew",
      query: { id: id },
    });
  }
  return;
  Router.push({
    path: "/solveNew/detailNew",
    query: { id: id },
  });
};
eventBus.on("applyDetailRefresh", getData);
// 下载超限提示弹窗取消按钮
const downloadModalCancel = () => {
  showDownloadModal.value = false;
};
// 下载超限提示弹窗确认按钮
const downloadModalConfirm = () => {
  showDownloadModal.value = false;
  showDownloadForm.value = true;
};
const downloadFormCancel = () => {
  showDownloadForm.value = false;
};
const downloadFormConfirm = () => {
  showDownloadForm.value = false;
};
</script>
<style lang="scss" scoped>
@import "./index.scss";
</style>

<style lang="scss">
.dialogModal {
  .dia_box {
    background-image: url("@/assets/images/solution/detail/downBgc.png");
    height: 150px;
    padding: 20px 24px;
  }

  .ant-modal .ant-modal-title {
    font-weight: bold;
    font-size: 24px;
    color: #122c6c;
    line-height: 28px;
    text-align: center;
  }

  .ant-modal-content {
    height: 395px;
    padding: 0;
  }

  .ant-form {
    width: 100%;
  }

  .title {
    font-weight: bold;
    font-size: 24px;
    color: #122c6c;
    line-height: 28px;
    margin-bottom: 8px;
  }

  .ant-tabs-tab-active {
    background: #1a66fb;

    .ant-tabs-tab-btn {
      color: #ffffff !important;
    }
  }

  .ant-tabs-nav-wrap {
    margin-top: 16px;
    width: 236px;
    height: 48px;
    background: #ffffff;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #ffffff;
  }

  .ant-input {
    background: linear-gradient(
      196deg,
      #eaeff7 0%,
      rgba(234, 239, 247, 0.41) 100%
    );
  }

  .ant-input-affix-wrapper {
    background: linear-gradient(
      196deg,
      #eaeff7 0%,
      rgba(234, 239, 247, 0.41) 100%
    );
    box-shadow: 0px -8px 32px 0px #ffffff, inset 0px 8px 24px 0px #dfe4ed;
    border-radius: 4px 4px 4px 4px;

    button {
      font-weight: 500;
      font-size: 16px;
      color: #1a66fb;
      line-height: 28px;
    }
  }

  .ant-tabs-nav::before {
    display: none;
  }

  .ant-tabs-tabpane {
    background-color: #ffffff !important;
    font-weight: 500;
    font-size: 16px;
    color: #2e3852;
    line-height: 28px;
    height: 150px;
  }

  .ant-tabs-ink-bar {
    display: none;
  }

  .ant-tabs-content {
    padding-left: 10px;
  }

  .ant-tabs-tab {
    width: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .key {
    font-weight: 400;
    font-size: 16px;
    color: #2b3f66;
    line-height: 28px;
  }
}

.left_tit {
  .title {
    font-weight: 700;
    font-size: 24px;
    color: #2e3852;
    line-height: 28px;
    text-align: left;
    display: inline-block;
    margin-right: 8px;
  }
}

.code {
  display: inline-block;
  padding: 5px 8px;
  border: none;
  font-size: 16px;
  margin-left: 12px;
  color: #0c70eb;
  background: transparent;
}

.case_box {
  .left_img {
    width: 420px;
    height: 280px;

    img {
      height: 280px;
    }
  }

  .right_con {
    height: 280px;
    flex: 1;
    padding: 24px;
    background: linear-gradient(163deg, #f1f3f6 0%, #f6f7f9 38%, #ffffff 100%),
      #ffffff;
    box-shadow: 8px 8px 12px 0px rgba(0, 0, 0, 0.04),
      -8px -8px 12px 0px rgba(255, 255, 255, 0.5);
    border: 1px solid #ffffff;
    font-weight: 400;
    font-size: 16px;
    color: #2e3852;
    line-height: 28px;

    .right_name {
      margin-bottom: 0;
    }
  }
}

.caseinfo {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  /* 限制为3行 */
  -webkit-box-orient: vertical;
  overflow: hidden;
  /* 隐藏超出部分 */
  text-overflow: ellipsis;
}

.cards {
  display: flex;
  justify-content: start;
  margin-top: 24px;
  flex-wrap: wrap;

  .item_card {
    width: 374px;
    padding-bottom: 12px;
    margin-right: 39px;
    margin-bottom: 24px;
    background: linear-gradient(163deg, #f1f3f6 0%, #f6f7f9 38%, #ffffff 100%);
    box-shadow: 8px 8px 12px 0px rgba(0, 0, 0, 0.04),
      -8px -8px 12px 0px rgba(255, 255, 255, 0.5);
    border-radius: 0px 0px 0px 0px;
    border: 2px solid #ffffff;

    p {
      margin-bottom: 6px;
      padding: 0 12px;
    }

    img {
      width: 100%;
      height: 176px;
    }

    .title {
      margin-top: 12px;
      font-weight: bold;
      font-size: 20px;
      color: #2e3852;
    }

    .desc {
      font-weight: 400;
      font-size: 16px;
      color: rgba(46, 56, 82, 0.85);
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      /* 控制显示的行数 */
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      height: 75px;
    }
  }
}

.item_card:nth-child(3n) {
  margin-right: 0;
}

// .item_card:nth-child(4n) {
//   margin: 0;
// }
.info_bottom {
  display: flex;

  p {
    display: flex !important;
    align-items: center;
  }
}

:v-deep(.an_title) {
  background: #ffffff !important;
}

tooltip-style.el-tooltip__popper {
  max-width: 220px;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.7);
  /* 半透明遮罩 */
  z-index: 9999;
}
</style>
