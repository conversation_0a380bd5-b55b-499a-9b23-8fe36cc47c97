<template>
  <div id="zonePage">
    <div class="loading-overlay" v-if="viewLoading">
      <a-spin :spinning="viewLoading" tip="附件加载中"></a-spin>
    </div>
    <div class="top_nav">
      <div class="left_nav">
        <span class="title" @click="back">专区</span>
        <span class="title"> / </span>
        <span class="current">{{ policyData.name }}</span>
      </div>
      <div @click="back" style="cursor: pointer; color: #2e7fff">返回</div>
    </div>

    <div class="contentZone">
      <div class="text">{{ policyData.name }}</div>
      <div style="margin: 0 80px">
        <div class="classify">
          <div class="flex">
            <div style="padding: 14px 0">
              <span class="title">政策分类：</span>
              <span class="word">{{ policyData.classifyName }}</span>
            </div>
            <div style="padding: 14px 0; margin: 0 40px; flex: 1">
              <span class="title">所属专区：</span>
              <span class="word">{{ zoneDeal(policyData.zoneList) }}</span>
            </div>
          </div>
          <div class="time">{{ createTime }}</div>
        </div>

        <img
          :src="policyData.image"
          alt=""
          width="1040px"
          height="480px"
          style="margin-top: 40px"
        />

        <div class="description" v-html="policyData.details"></div>
      </div>

      <div class="fileInfo">
        <div
          v-for="(item, index) in policyData.fileList"
          :key="index"
          style="margin-bottom: 10px"
        >
          <img
            src="@/assets/images/solution/detail/text.png"
            alt=""
            style="width: 40px; height: 40px"
          />
          <a @click="previewFile(item)">{{ item.name }}</a>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs } from "vue";
import { policyDetail } from "@/api/prefecture/home";
import { useRouter, useRoute } from "vue-router";
import { pptTopdf } from "@/api/fileUpload/uploadFile.js";

export default defineComponent({
  setup(props) {
    const Route = useRoute();
    const Router = useRouter();
    const data = reactive({
      policyData: {},
      createTime: "",
      viewLoading: false,
    });
    const getDetail = () => {
      policyDetail(Route.query.id).then((res) => {
        data.policyData = res.data;
        data.createTime = res.data.createTime.slice(0, 10);
      });
    };
    getDetail();
    const zoneDeal = (list) => {
      let name = "";
      if (list && list.length > 0) {
        list.forEach((el, index) => {
          if (index < list.length - 1) name = name + el.zoneName + "、";
          if (index == list.length - 1) name = name + el.zoneName;
        });
        return name;
      }
      return "-";
    };
    const previewFile = (val) => {
      data.viewLoading = true;
      pptTopdf({
        filePath: val.path,
        fileUrl: val.url,
      }).then((res) => {
        data.viewLoading = false;
        if (res.code == 200) {
        	let windowOrigin = window.location.origin;
		      let token = localStorage.getItem("token");
		      let newHref = res.data;
					if(res.data.includes(windowOrigin)){
					  newHref = "/portal" + res.data.split(windowOrigin)[1]
					}
		      const newpage = Router.resolve({
		        name: "lookPdf",
		        query: {
		          urlMsg: encodeURIComponent(
		          	windowOrigin + newHref + "?token=" + token
		          ),
		          urlName: val.name,
		        },
		      });
          window.open(newpage.href, "_blank");
        }
      });
    };
    const back = () => {
      Router.back(-1);
    };
    return {
      ...toRefs(data),
      Route,
      previewFile,
      zoneDeal,
      back,
    };
  },
});
</script>

<style lang="scss" scoped>
.top_nav {
  padding-left: 120px;
  height: 60px;
  width: 100%;
  margin-top: 8px;
  padding-right: 120px;
  display: flex;
  justify-content: space-between;
  position: fixed;
  top: 50px;
  z-index: 21;
  padding-top: 20px;

  div {
    display: inline-block;
  }

  .left_nav {
    padding-bottom: 8px;

    .title {
      font-weight: 400;
      font-size: 12px;
      color: #84899a;
      line-height: 20px;
      margin-right: 8px;
      cursor: pointer;
    }

    .current {
      font-weight: 400;
      font-size: 12px;
      color: #2e3852;
      line-height: 20px;
    }
  }
}

.contentZone {
  margin: 50px auto 20px;
  width: 1200px;
  height: calc(100vh - 130px);
  overflow-y: auto;

  background-color: #fff;
  .text {
    font-weight: 500;
    font-size: 24px;
    color: rgba(0, 0, 0, 0.85);
    text-align: center;
    padding-top: 56px;
    padding-bottom: 24px;
  }

  .classify {
    border-top: 1px solid #dae2f5;
    border-bottom: 1px solid #dae2f5;
    display: flex;
    justify-content: space-between;
    .title {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.45);
    }
    .time {
      color: rgba(0, 0, 0, 0.45);
      padding: 14px 0;
      flex: none;
    }
  }
  .description {
    padding: 40px 0 10px;
  }
  .fileInfo {
    margin: 0 80px;
    margin-bottom: 10px;
  }
}
.contentZone::-webkit-scrollbar {
  display: none;
}
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.7);
  /* 半透明遮罩 */
  z-index: 9999;
}
</style>