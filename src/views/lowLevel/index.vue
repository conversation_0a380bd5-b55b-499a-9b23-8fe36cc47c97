<template>
  <div class="topImage">
    <div class="topImage_content">
      <div class="topImage_title">{{ formData.name }}</div>
      <div class="topImage_details">
        <a-tooltip overlayClassName="tooltip_class">
          <template v-if="isShowToolTip(formData.introduce, 127)" #title>
            {{ formData.introduce }}
          </template>
          {{ formData.introduce }}
        </a-tooltip>
      </div>
    </div>

    <div class="flex just-sb align-center">
      <template v-for="(item, index) in projectList" :key="index">
        <div
          class="cardBac"
          :style="{ backgroundImage: `url('${item.backgroundImageUrl}')` }"
        >
          <div class="card_dec">
            <div style="display: flex">
              <p style="margin: 0 0 0 0">
                {{ item.num }}
              </p>
              <p class="tips">个</p>
            </div>
            <div
              style="
                font-weight: 400;
                font-size: 14px;
                color: rgba(0, 0, 0, 0.45);
                display: flex;
                align-items: center;
                margin-top: 11px;
              "
            >
              <p class="second">{{ item.title }}</p>
            </div>
          </div>
          <div style="margin-right: 24px">
            <img :src="item.image" style="width: 88px; height: 88px" />
            <div v-show="!item.rate" style="margin-top: 8px">&nbsp</div>
          </div>
        </div>
      </template>
    </div>
  </div>

  <div class="totaoText"></div>
  <table-list :policyInsightsList="policyInsightsList" />
</template>

<script>
import { defineComponent, reactive, toRefs } from "vue";
import tableList from "./components/tableList.vue";
import { getDetailList } from "@/api/prefecture/home";
import backgroundImageUrl1 from "@/assets/images/home/<USER>";
import backgroundImageUrl2 from "@/assets/images/home/<USER>";
import backgroundImageUrl3 from "@/assets/images/home/<USER>";
import image5 from "@/assets/images/product/home.png";
import { isShowToolTip } from "@/utils/index.js";
import { useRoute } from "vue-router";

export default defineComponent({
  name: "lowLevel",
  components: {
    tableList,
  },
  setup() {
    const route = useRoute();
    const data = reactive({
      projectList: [
        // {
        //   num: "0",
        //   title: "政策",
        //   backgroundImageUrl: backgroundImageUrl1,
        //   image: image5,
        // },
        {
          num: "0",
          title: "方案",
          backgroundImageUrl: backgroundImageUrl1,
          image: image5,
        },
        {
          num: "0",
          title: "能力",
          backgroundImageUrl: backgroundImageUrl2,
          image: image5,
        },
        {
          num: "0",
          title: "产品",
          backgroundImageUrl: backgroundImageUrl3,
          image: image5,
        },
      ],
      formData: {},
      policyInsightsList: [],
    });
    const getCountDate = () => {
      getDetailList(route.query.zoneId).then((res) => {
        data.policyInsightsList = res.data.policyInsightsList;
        // data.projectList[0].num = res.data.policyList
        //   ? res.data.policyList.length
        //   : 0;
        data.projectList[0].num = res.data.solutionList
          ? res.data.solutionList.length
          : 0;
        data.projectList[1].num = res.data.schemeModuleList
          ? res.data.schemeModuleList.length
          : 0;
        data.projectList[2].num = res.data.productList
          ? res.data.productList.length
          : 0;
        data.formData = res.data;
      });
    };
    getCountDate();
    return {
      ...toRefs(data),
      isShowToolTip,
    };
  },
});
</script>

<style lang="scss" scoped>
.topImage {
  width: 1200px;
  margin: 0 auto;
  height: 320px;
  background-image: url("@/assets/images/prefecture/levelBac.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;

  .topImage_content {
    padding: 63px 0 40px 0;

    .topImage_title {
      font-weight: bold;
      font-size: 42px;
      color: #122c6c;
    }

    .topImage_details {
      width: 1020px;
      font-size: 16px;
      color: #2b3f66;
      line-height: 32px;
      margin-top: 16px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .cardBac:nth-child(1) {
    margin-left: -23px;
  }

  .cardBac:nth-child(3) {
    margin-right: -23px;
  }

  .cardBac {
    flex: 1;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: space-around;

    .card_num {
      display: flex;
      align-items: flex-start;
    }

    .card_dec {
      margin: 24px 0 24px 24px;
      p {
        font-weight: bold;
        font-size: 40px;
        color: #2e7fff;
        line-height: 47px;
        text-shadow: 0px 4px 6px rgba(46, 127, 255, 0.25);
      }
      .second {
        font-weight: bold;
        font-size: 18px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 21px;
      }
      .tips {
        width: 18px;
        height: 18px;
        text-align: center;
        font-weight: 500;
        font-size: 12px;
        color: #ffffff;
        line-height: 12px;
        background: #ff5b00;
        border-radius: 50%;
        margin-top: 5px;
        padding-top: 2px;
      }
      .dec_num {
        font-size: 40px;
        font-weight: bold;
        font-family: DIN, DIN;
        color: #2e7fff;
        text-shadow: 0px 4px 6px rgba(46, 127, 255, 0.25);
      }

      .dec_box {
        background-color: #ff5b00;
        border-radius: 50%;
        width: 18px;
        height: 18px;
        font-weight: 500;
        font-size: 12px;
        color: #ffffff;
        text-align: center;
        line-height: 18px;
      }
    }
  }
}

.totaoText {
  font-weight: bold;
  font-size: 28px;
  color: rgba(0, 0, 0, 0.85);
  text-align: center;
  margin-top: 80px;
  margin-bottom: 32px;
}
</style>

<style lang="scss">
.tooltip_class {
  max-width: 50%;
}
</style>