.AIBg {
    background-image: url("@/assets/images/AI/newAIbg.png");
}

.commonBg {
    background-image: url("@/assets/images/home/<USER>");
}

.readContent {
    width: 1200px;
    margin: 0 auto;
    background-image: url("@/assets/images/product/board.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    border-radius: 10px 10px 10px 10px;
    padding: 0 40px;
    overflow: hidden;

    .text {
        font-weight: 500;
        font-size: 24px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 28px;
        padding-top: 40px;
    }

    .date {
        color: rgba(0, 0, 0, 0.45);
        font-size: 14px;
        margin-top: 16px;
        line-height: 20px;
    }

    .desc {
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 34px;
        margin-top: 24px;
        overflow: hidden;
        height: 160px;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 5;
    }

    .file {
        font-size: 14px;
        color: #0C70EB;
        line-height: 22px;
        margin-top: 16px;
    }

    .operation {
        padding: 32px 0;
        display: flex;
        justify-content: center;
    }

}


.searchInfo {
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 1200px;
    margin: 0 auto;
    padding: 24px 40px 24px 40px;
    transition: 1s;

    .switch {
        margin-right: 16px;
        transition: 1s;

        .AIlogo {
            width: 48px;
            height: 18px;
            background-image: url(@/assets/images/AI/AILogo.png);
            background-size: 100% 100%;
            margin-bottom: 5px;
        }
    }

    .vocationPull {
        // background: #FFFFFF;
        display: flex;
        align-items: center;
        border-top-left-radius: 5px;
        border-bottom-left-radius: 5px;
        border-top-right-radius: 5px;
        border-bottom-right-radius: 5px;
    }

    .inputClass {
        border: none;
        width: 100%;
        height: 100%;
        box-shadow: none !important;
    }

    .line {
        width: 1px;
        height: 26px;
        background: #EFF0F4;
    }

    .lines {
        width: 1px;
        height: 56px;
        background: #EFF0F4;
    }

    .AIbtn {
        background: linear-gradient(108deg, #4446FF 0%, #4173FF 50%, #3EB8FF 100%);
    }

    .commonBtn {
        background-color: #0C70EB;
    }

    .seekInfo {
        font-weight: bold;
        font-size: 20px;
        color: #FFFFFF;
        display: flex;
        align-items: center;
        width: 160px;
        height: 56px;
        cursor: pointer;
        border-top-right-radius: 5px;
        border-bottom-right-radius: 5px;

        img {
            width: 24px;
            height: 24px;
            margin-left: 20px;
            margin-right: 8px;
        }
    }

    .stage {
        margin-left: 44px;
        margin-top: 16px;
    }
}

.emptyPhoto {
    text-align: center;
    margin-bottom: 20px;

    img {
        width: 240px;
        height: 248px;
    }
}

.textWord {
    font-weight: bold;
    font-size: 32px;
    color: rgba(0, 0, 0, 0.85);
    width: 1200px;
    margin: 40px auto 24px;
}