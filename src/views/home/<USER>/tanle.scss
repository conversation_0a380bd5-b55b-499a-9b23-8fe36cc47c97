.tabList {
    padding: 66px 120px 60px 120px;

    .ant-tabs {
        min-height: 200px;
        max-height: 460px;
    }

    .top {
        display: flex;
        margin-bottom: 50px;
    }

    .my_work {
        background-color: #ffffff;

        :deep(.ant-tabs-bar) {
            margin-bottom: 0 !important;
        }

        :deep(.ant-tabs-content) {
            margin-top: 10px;
        }

        :deep(.ant-tabs-nav-container) {

            .ant-tabs-nav-wrap {
                height: 52px;
            }

            .ant-tabs-nav-wrap>div {
                height: 65px;
            }
        }

        .header {
            padding-left: 20px;
            padding-top: 15px;
            padding-bottom: 12px;
            border-bottom: 3px solid #f5f9fd;

            .label {
                width: 14px;
                height: 8px;
                margin-right: 10px;
            }

            .tit {
                width: 78px;
                height: 19px;
            }
        }

        .content {
            padding: 0 20px 20px 20px;
            position: relative;

            .more {
                font-weight: 400;
                font-size: 12px;
                color: #1E63FF;
                line-height: 32px;
                cursor: pointer;
            }

            :deep(.ant-tabs-tab) {
                font-weight: 400;
                font-size: 14px;
                color: #24456a;
                line-height: 16px;
            }

            :deep(.ant-tabs-tab-active) {
                font-weight: 500;
                font-size: 14px;
                color: #236cff;
                line-height: 16px;
            }

            .ant-tabs-tab-active {
                position: relative;
            }

            :deep(.ant-tabs-tab-active::after) {
                content: "";
                position: absolute;
                bottom: -3px;
                /* 调整三角形位置 */
                left: 50%;
                transform: translateX(-50%);
                width: 0;
                height: 0;
                z-index: 100;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 4px solid #236cff;
            }


            :deep(.ant-table-thead) {
                tr {
                    th {
                        background-color: #F4F7FF !important;
                        padding-left: 25px;
                        font-weight: 400;
                        font-size: 14px;
                        color: #0C213A;
                        line-height: 16px;
                    }

                }
            }

            :deep(.ant-table-tbody) {
                tr {
                    td {
                        background-color: #FFFFFF !important;
                        padding-left: 29px;
                        font-weight: 400;
                        font-size: 14px;
                        color: #0C213A;
                        line-height: 16px;
                        padding-bottom: 16px;
                    }

                }
            }

            :deep(.ant-table-tbody>tr>td) {
                border-bottom: 1px dashed #f0f0ff !important;
                height: 56px;
            }

            :deep(.ant-table-tbody) {
                tr {
                    td:last-child {
                        color: #829AB6 !important;
                    }
                }


            }

            :deep(.ant-pagination) {
                display: none !important;
            }
        }
    }

    .line {
        height: 1px;
        width: 96%;
        background-color: #E9E9E9;
        position: absolute;
        top: 37px;
    }



    .boot {
        display: flex;

        .notice {
            height: 367px;

            .content {
                .ulList {
                    list-style: none;
                    padding-left: 0;

                    li {
                        border-bottom: 1px dashed #f0f0ff;
                        padding: 16px 30px 16px 28px;
                        display: flex;
                        justify-content: space-between;

                        .con {
                            margin-left: 0;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap
                        }

                        span {
                            display: inline-block;
                        }
                    }
                }


            }
        }
    }

    .workList {
        width: 65%;
        position: relative;

        .content {
            // background-image: url('../../../assets/images/home/<USER>');
            background-size: 100% 100%;
            background-repeat: no-repeat;
            position: relative;
            // background-size: cover;
            z-index: 1;
        }
    }

    .image {
        position: absolute;
        height: 350px;
        width: 100%;
        z-index: 100;
        bottom: 0;

    }

    // .table-with-bg{
    //     background-image: url('../../../assets/images/home/<USER>');
    //     background-size: 100% 100%;
    //     background-repeat: no-repeat;
    // }




    .tabsList {
        display: inline-block;
        width: 100%;
        position: relative;

        .content {
            padding-top: 15px;

            .second {
                :deep(.ant-tabs-tab-active::after) {
                    display: none;
                }

                :deep(.ant-tabs-ink-bar) {
                    display: none !important;
                }
            }

            :deep(.ant-tabs-tab) {
                margin-bottom: 9px !important;
            }

            :deep(.ant-table-wrapper) {
                margin-top: 15px;
            }

            :deep(.ant-tabs-tab-active::after) {
                bottom: -11px;
            }

            .ant-btn {
                font-weight: 400;
                font-size: 14px;
                color: #24456A;
                line-height: 16px;
                border: 1px solid #E9E9E9;
                border-radius: 2px 2px 2px 2px;
                margin-right: 0;
            }

            .active {
                background: #E6EFFF;
                font-weight: 500;
                color: #236CFF;
            }
        }

        .line {
            height: 3px;
            width: 100%;
            background-color: #F4F7FD;
            position: absolute;
            top: 64px;
        }
    }

    .notice {
        margin-left: 3%;
        width: 32%;
        background-image: url('../../../assets/images/home/<USER>');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        position: relative;

        .line {
            height: 3px;
            width: 100%;
            background-color: #F4F7FD;
            position: absolute;
            top: 50px;
        }

        .content {
            padding-top: 13px;

            .more {
                text-align: center;
                color: #5387FF;
                position: relative;
                height: 40px;
                cursor: pointer;
                font-size: 14px;

                .line {
                    position: absolute;
                    top: 22px;
                    left: 49%;
                    right: 183px;
                    width: 8px;
                    height: 8px;
                    border-top: 1px solid #236CFF;
                    border-right: 1px solid #236CFF;
                    transform: rotate(-227deg);
                }

                .lines {
                    position: absolute;
                    top: 26px;
                    left: 48.7%;
                    right: 183px;
                    width: 10px;
                    height: 10px;
                    border-top: 1px solid #236CFF;
                    ;
                    border-right: 1px solid #236CFF;
                    ;
                    transform: rotate(-227deg);
                }
            }

            .ulList {
                list-style: none;
                font-weight: bold;
                font-size: 14px;
                color: #325A88;
                line-height: 16px;
                padding-left: 0;

                li {
                    border-bottom: 1px dashed #f0f0ff;
                    padding: 16px 0 16px 28px;

                    .con {
                        font-weight: 400;
                        color: #0C213A;
                        margin-left: 24px;
                        width: 260px;

                    }
                }

                li:last-child {
                    border-bottom: none
                }
            }

            .centerList {
                margin-bottom: 7px;

                li {
                    padding-left: 12px;
                    display: flex;
                    justify-content: space-between;
                    position: relative;
                }

                .con {
                    margin-left: 0 !important;
                }

                .left {
                    .tit_img {
                        img {
                            width: 21px;
                            height: 17px;
                        }

                        margin-right: 3px;
                    }

                    .tit {
                        margin-right: 5px;
                    }

                    .tag {
                        position: absolute;
                        top: 2px;

                        img {
                            width: 30px;
                            height: 15px;
                        }
                    }
                }

            }

        }
    }
}

:deep(.ant-table-title) {
    padding: 0;
}

:deep(.ant-table-header) {
    overflow-y: hidden !important;
}

.more {
    cursor: pointer;
}

:deep(.ant-table-body) {
    ::-webkit-scrollbar {
        display: block !important;
    }
}

:deep(.ant-table-header) {
}

.my_center {
    padding: 60px 120px;

    .top {
        text-align: center;
        width: 100%;
    }

    ul {
        list-style: none;
    }

    .banner {
        width: 498px;
    }

    .centerList {
        background-color: #FFFFFF;
        margin-top: 53px;
        padding-bottom: 15px;

        .header {
            padding-left: 20px;
            padding-top: 15px;
            padding-bottom: 12px;
            border-bottom: 3px solid #f5f9fd;

            .label {
                width: 14px;
                height: 8px;
                margin-right: 10px;
            }

            .tit {
                width: 78px;
                height: 19px;
            }
        }


        .ulList {
            font-weight: 400;
            font-size: 14px;
            color: #0C213A;
            line-height: 16px;
            padding-left: 30px;
            padding-right: 30px;

            li {
                border-bottom: 1px dashed #f0f0ff;
                padding: 16px 0 16px 17px;
                display: flex;
                justify-content: space-between;
                position: relative;

                .con {
                    font-weight: 400;
                    color: #0C213A;
                    margin-left: 14px;
                    margin-right: 5px;

                }
            }

            .left {
                .tit {
                    font-weight: 400;
                    font-size: 16px;
                    color: #325A88;
                    line-height: 16px;
                    margin-left: 5px;
                    margin-right: 5px;
                }

                .tit_img {
                    img {
                        width: 31px;
                        height: 26px;
                    }
                }
            }

            .time {
                color: #8BA1BC;
            }

            .tag {
                position: absolute;
                bottom: 28px;

                img {

                    width: 30px;
                    height: 15px;
                }
            }

            li:last-child {
                border-bottom: none
            }
        }

        .more {
            text-align: center;
            color: #5387FF;
            position: relative;
            height: 40px;
            cursor: pointer;

            .tit {
                font-weight: 400;
                font-size: 12px;
                color: #1E63FF;
                line-height: 32px;
            }

            .line {
                position: absolute;
                top: 22px;
                left: 49.8%;
                right: 183px;
                width: 9px;
                height: 9px;
                border-top: 1px solid #236CFF;
                ;
                border-right: 1px solid #236CFF;
                ;
                transform: rotate(-227deg);

            }

            .lines {
                position: absolute;
                top: 28px;
                left: 49.759%;
                right: 183px;
                width: 11px;
                height: 11px;
                border-top: 1px solid #236CFF;
                ;
                border-right: 1px solid #236CFF;
                transform: rotate(-227deg);
            }
        }
    }
}

:deep(.ant-table) {
    height: 256px !important;
}

.content_span {
    padding: 2px 6px;
    text-align: center;
    display: inline-block;
    background-color: #E8F8EC;
    font-weight: 400;
    font-size: 14px;
    color: #3DC55D;
    line-height: 22px;
    border-radius: 4px 4px 4px 4px;
}