<template>
  <!-- <div class="topImage">
        <div class="topImage_content">
          <div class="topImage_title" >能力图谱</div>
          <a-tag class="top_tag">个性化</a-tag>
          <a-tag class="top_tag">高性能</a-tag>
          <div class="topImage_details">
            为满足不同行业客户的特定需求，我们将网络、云、平台、应用和终端等多种技术融合在一起，贯穿整个销售、交付和服务等全流程。通过能力汇聚管理重点支撑能力的生产使用和属地化运营，通过汇聚全网能力、属地能力、生态能力，支撑集中调用、客户侧部署等多类能力的使用和计量，同步建立属地运营管理流程。可独立提供全流程服务，并支持快速组合调用，为客户提供更加个性化、高效的能力支持服务。
          </div>
        </div>
      </div> -->
  <!-- <div class="card_content flex">
        <div
          class="card_dec"
          style="
             {
              backgroundimage: `url('@/assets/images/ability/mask.png');
            }
          "
        >
          <img class="count_img" src="@/assets/images/ability/card1.png" alt="" />
          <div class="line1">
            <span>能力累计</span><span class="simplified_text">上架数 </span>
            <div>
              <span class="main_count"> {{ countList[0].number }}</span
              ><span class="unit"> 个 </span>
            </div>
          </div>
          <p class="line2">
            <span class="sub_text">近七天发能力数</span>
            <span class="sub_count"> {{ countList[0].num }}</span> <span> 个</span>
          </p>
        </div>
    
        <div class="card_dec">
          <img class="count_img" src="@/assets/images/ability/card2.png" />
          <div class="line1">
            <span class="simplified_text">能力查阅</span><span>总次数 </span>
            <div>
              <span class="main_count">{{ countList[1].number }}</span
              ><span class="unit"> 次 </span>
            </div>
          </div>
          <div class="line2">
            <span class="sub_text">近七天查阅数量</span>
            <span class="sub_count"> {{ countList[1].num }}</span> <span> 次</span>
          </div>
        </div>
    
        <div class="card_dec">
          <img class="count_img" src="@/assets/images/ability/card3.png" />
          <div class="line1">
            <span>能力累计</span><span class="simplified_text">支撑项目数 </span>
            <div>
              <span class="main_count"> {{ countList[2].number }}</span
              ><span class="unit"> 个 </span>
            </div>
          </div>
          <p class="line2">
            <span class="sub_text">近七天支撑项目数 </span>
            <span class="sub_count"> {{ countList[2].num }} </span>
            <span> 个</span>
          </p>
        </div>
    
        <div class="card_dec" @click="countAdd">
          <img class="count_img" src="@/assets/images/ability/card4.png" />
          <div class="line1">
            <span>能力应用项目累计</span
            ><span class="simplified_text">签约额 </span>
            <div>
              <span class="main_count">{{ countList[3].number }}</span
              ><span class="unit"> 万 </span>
            </div>
          </div>
          <p class="line2">
            <span class="sub_text">近七天签约额</span>
            <span class="sub_count"> {{ countList[3].num }} </span> <span> 万</span>
          </p>
        </div>
      </div> -->
  <div class="home-Ftop">
    <div class="tip">
      <p class="num">15</p>
      <span class="red">个</span>
      <p class="blod">二开产品上架数</p>
      <p class="light">近期上架产品数 <span class="num1">3</span>个</p>
    </div>
    <div class="tip">
      <p class="num">{{ countList[1].number }}</p>
      <span class="red1">次</span>
      <p class="blod">产品累计下载次数</p>
      <p class="light">近期产品下载次数 <span class="num1">79</span>次</p>
    </div>
    <div class="tip">
      <p class="num">35</p>
      <span class="red">个</span>
      <p class="blod">产品累计支撑项目数</p>
      <p class="light">近期产品支撑项目数 <span class="num1">3</span>个</p>
    </div>
    <div class="tip" style="border: none">
      <p class="num">9357.78</p>
      <span class="red2">万</span>
      <p class="blod">累计支撑项目签约额</p>
      <p class="light">
        近期新增支撑项目签约额 <span class="num1">506.2</span>万
      </p>
    </div>
  </div>
  <a-modal
    :visible="previewVisible"
    @cancel="closeModal2"
    :width="600"
    @ok="handleOk"
    :destroyOnClose="true"
    :maskClosable="false"
  >
    <div ref="add">
      累计上架数量：<a-input v-model:value="virtually.listingNum" />
      7天发布数量：
      <a-input v-model:value="virtually.abilityNum" /> 支撑项目数：
      <a-input v-model:value="virtually.projectNum" /> 7天支撑项目数：<a-input
        v-model:value="virtually.projectFrequency"
      />
      签约额：<a-input v-model:value="virtually.contractVolume" /> 7天签约额：
      <a-input v-model:value="virtually.recentSignAmount" />
    </div>
  </a-modal>
  <div class="chanpin-list box flex">
    <div class="list-wapper1">
      <div class="top-title flex">
        <span>
          <span class="chanpin">热门产品</span>
        </span>
        <span class="more" @click="more">更多</span>
      </div>
      <div class="chanpin-list flex">
        <div class="list-wapper">
          <div class="list-li" v-for="item in hotData" :key="item.id">
            <div class="li-img">
              <img :src="`${item.img}`" alt="" />
            </div>
            <div class="li-info">
              <div class="top-title-li">
                <div>{{ item.name }}</div>
                <div class="company">{{ item.pro }}</div>
              </div>
              <div class="bottom">
                <div class="left">
                  <p class="tag">
                    {{ item.type }}
                  </p>
                </div>
                <div class="last">
                  <!-- <img src="../../img/小下载.png" alt="" />
                      <p class="download">111</p> -->
                  <p class="time">{{ item.date }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="list-wapper2">
      <div class="top-title flex">
        <span>
          <span class="chanpin">近期发布</span>
        </span>
        <span class="more" @click="more">更多</span>
      </div>
      <div class="chanpin-list flex">
        <div class="list-wapper">
          <div class="list-li" v-for="item in recentData" :key="item.id">
            <div class="li-img">
              <img :src="`${item.img}`" alt="" />
            </div>
            <div class="li-info">
              <div class="top-title-li">
                <div>{{ item.name }}</div>
                <div class="company">{{ item.pro }}</div>
              </div>
              <div class="bottom">
                <div class="left">
                  <p class="tag">
                    {{ item.type }}
                  </p>
                  <!-- <p class="time">{{ item.date }}</p> -->
                </div>
                <div class="last">
                  <p class="time">{{ item.date }}</p>
                  <!-- <img src="../../img/小下载.png" alt="" />
                      <p class="download">{{ item.downloadCount }}</p> -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="list-wapper3">
      <div class="top-title flex">
        <span>
          <span class="chanpin">近期产品复用案例</span>
        </span>
        <span class="more" @click="more">更多</span>
      </div>
      <div class="chanpin-list flex">
        <div class="list-wapper">
          <div class="list-li" v-for="item in reData" :key="item.id">
            <div class="li-img">
              <img :src="`${item.img}`" alt="" />
            </div>
            <div class="li-info">
              <div class="top-title-li">
                <div>{{ item.name }}</div>
                <div class="company">
                  {{ item.pro }}
                </div>
              </div>
              <div class="bottom">
                <div class="left">
                  <p class="tag" v-for="(value, key) in item.type">
                    {{ value }}
                  </p>
                </div>
                <!-- <div class="last">
                      <img src="../../img/小下载.png" alt="" />
                      <p class="download">{{ item.downloadCount }}</p>
                    </div> -->
              </div>
            </div>
            <!-- <div :class="['tag-sort', `sort-${index}`]">No.{{ index + 1 }}</div> -->
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="bottom-left flex">
    <div class="left">
      <div class="top-title flex">
        <span>
          <span class="chanpin">近期二开产品引入计划</span>
        </span>
        <!-- <span class="more" @click="more">更多</span> -->
      </div>
      <div class="middle-table">
        <a-table
          :scroll="{
            y: 214,
          }"
          rowKey="id"
          :columns="columns"
          :dataSource="computedDataSet"
          :pagination="false"
        >
        </a-table>
      </div>
    </div>
    <div class="right">
      <div class="top-title flex">
        <span>
          <span class="chanpin">近期地市软研开发进展</span>
        </span>
        <!-- <span class="more" @click="more1">更多</span> -->
      </div>
      <div class="middle-table">
        <a-table
          :scroll="{
            y: 214,
          }"
          rowKey="id"
          :columns="columns1"
          :dataSource="computedDataSet1"
          :pagination="false"
        >
          <template #percent="{ text }">
            <a-progress :percent="text" size="small" />
          </template>
        </a-table>
      </div>
    </div>
  </div>

  <div class="contents">
    <div class="left">
      <div class="title">
        <img src="@/assets/images/echarts/Group <EMAIL>" alt="" />
        <p>二开产品江苏省应用情况</p>
      </div>
      <div class="bac">
        <div class="echarts">
          <div class="first">
            <p class="begin">24年江苏省落地项目<span class="num">35</span>个</p>
            <p>项目金额<span class="money">9357.78</span>万元</p>
            <div id="outside" style="width: 850px; height: 300px"></div>
          </div>
          <div class="line"></div>
          <div class="first bottom">
            <p class="begin">排名前3落地产品：智慧社区、智慧运维、乡村振兴</p>
            <!-- <p>项目金额<span class="money">10</span>万元</p> -->
            <div id="Province" style="width: 850px; height: 300px"></div>
          </div>
        </div>
      </div>
    </div>
    <div class="right">
      <div class="title">
        <img src="@/assets/images/echarts/Group <EMAIL>" alt="" />
        <p>二开产品项目应用案例</p>
      </div>
      <ul class="list">
        <li class="card" v-for="(item, key) in rightData" :key="key">
          <div class="header">
            <div class="left">
              <span>{{ key + 1 }}. {{ item.province }}</span>
            </div>
          </div>
          <div
            class="content"
            v-for="(value, index) in item.solutionList"
            :key="index"
          >
            <div class="footer">
              <div class="data" v-for="(v, k) in value.projectList" :key="k">
                <p class="solution">
                  {{ v.productsName }}
                  <a-tag
                    class="tag"
                    v-if="v.label === '标杆案例'"
                    style="
                      color: #2e7fff;
                      border: 1px solid rgba(46, 127, 255, 0.65);
                      margin-left: 4px;
                    "
                    >{{ v.label }}</a-tag
                  >
                </p>
                <div class="data1">
                  <div class="left1">
                    <p class="tit">项目名称</p>
                    <p class="cont">
                      {{ v.name }}
                    </p>
                  </div>
                  <div class="detail">
                    <p class="con" style="margin-bottom: 0">项目金额（万元）</p>
                    <div class="red box">
                      <p>{{ v.money }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="line"></div>
          </div>
        </li>
      </ul>
      <!-- <div class="layPage">
            <pagination
              :totalItemCount="totalData"
              @size-change="sizeChange"
              @page-change="pageChange"
            />
          </div> -->
    </div>
  </div>
</template>
    
    <script>
import {
  defineComponent,
  reactive,
  toRefs,
  ref,
  onMounted,
  onBeforeUnmount,
} from "vue";
import { useRouter, useRoute } from "vue-router";
import { getCount, countUpdate } from "@/api/ability/home";
import * as echarts from "echarts";
export default defineComponent({
  name: "topContent",
  components: {
  },
  setup() {
    const tableListRef = ref();
    const data = reactive({
      previewVisible: false,
      virtually: {},
      countsList: [],
      hotData: [
        {
          name: "统一门户",
          img: '',
          type: "入口型",
          date: "2024-04-16",
          pro: " ICT",
        },
        {
          name: "智慧社区",
          img: '',
          type: "业务型",
          date: "2023-09-15",
          pro: " ICT",
        },
        {
          name: "数字乡村",
          img:'',
          type: "业务型",
          date: "2024-04-16",
          pro: " ICT",
        },
        {
          name: "玄武运维",
          img: '',
          type: "功能型",
          date: "2024-04-16",
          pro: " ICT",
        },
      ],
      recentData: [
        {
          name: "智能外呼",
          img: '',
          date: "2024-10-22",
          type: "功能型",
          pro: "ICT",
        },
        {
          name: "AI智接",
          img: '',
          date: "2024-10-22",
          type: "功能型",
          pro: "ICT",
        },
        {
          name: "数据治理",
          img: '',
          date: "2024-10-22",
          type: "功能型",
          pro: "紫金院",
        },
        {
          name: "智能坐席",
          img: '',
          date: "2024-10-18",
          type: "功能型",
          pro: "ICT",
        },
      ],
      reData: [
        {
          name: "苏州太仓数智集成港产业园智能化项目",
          type: ["流程引擎", "文档管理"],
          img: '',
          pro: "盐城",
        },
        {
          name: "镇江官塘桥街道社区治理项目",
          type: ["智慧社区"],
          img: '',
          pro: "镇江",
        },
        {
          name: "南京市梅园新村街道办事处智慧社区升级改造项目",
          type: ["智慧社区"],
          img: '',
          pro: "南京",
        },
        {
          name: "沭阳县钱集镇张圩社区为民服务云平台项目",
          type: ["智慧社区"],
          img: '',
          pro: "宿迁",
        },
      ],
      visible: false,
      countClick: 0,
      countList: [
        {
          number: "",
          num: "",
          title: "能力累计上架数",
          textDec: "近七天发布能力数",
        },
        {
          number: "",
          num: "",
          title: "能力查阅总次数",
          textDec: "近七天查阅数",
        },
        {
          number: "",
          num: "",
          title: "能力累计支撑项目数",
          textDec: "近七天支撑项目数",
        },
        {
          number: "",
          num: "",
          title: "能力应用项目累计签约额",
          textDec: "近七天签约额",
        },
      ],
      columns: [
        { title: "产品名称", dataIndex: "name", algin: "center" },
        { title: "提供方", dataIndex: "pro", algin: "center" },
        { title: "预计上架时间", dataIndex: "date", algin: "center" },
      ],
      columns1: [
        { title: "产品名称", dataIndex: "name", algin: "center" },
        { title: "研发单位", dataIndex: "pro", algin: "center" },
        { title: "总工时", dataIndex: "time", algin: "center" },
        {
          title: "进度",
          slots: { customRender: "percent" },
          align: "center",
          dataIndex: "percent",
          key: "percent",
        },
      ],
      computedDataSet: [
        {
          name: "视频AI",
          pro: "ICT",
          date: "2024-10-25",
        },
        {
          name: "舆情监测",
          pro: "ICT",
          date: "2024-10-25",
        },
        {
          name: "数字人",
          pro: "ICT",
          date: "2024-10-25",
        },
        {
          name: "视频客服",
          pro: "ICT",
          date: "2024-10-30",
        },
        {
          name: "智慧研训",
          pro: "ICT",
          date: "2024-10-30",
        },
      ],
      computedDataSet1: [
        {
          name: "小区物业管理系统",
          pro: "盐城",
          time: "120",
          percent: 80,
        },
        {
          name: "跨境商城系统",
          pro: "连云港",
          time: "500",
          percent: 30,
        },
        {
          name: "智能感知设备一张图 ",
          pro: "盐城",
          time: "100",
          percent: 90,
        },
        {
          name: "公交公司学习考试小程序",
          pro: "扬州",
          time: "50",
          percent: 85,
        },
        {
          name: "物流管理",
          pro: "盐城",
          time: "150",
          percent: 55,
        },
      ],
      projectList: [
        {
          name: "苏州",
          value1: "5",
          value2: "4961.7",
        },
        {
          name: "南京",
          value1: "4",
          value2: "2749.44",
        },
        {
          name: "无锡",
          value1: "0",
          value2: "0",
        },
        {
          name: "南通",
          value1: "4",
          value2: "23.87",
        },
        {
          name: "徐州",
          value1: "3",
          value2: "455.05",
        },
        {
          name: "常州",
          value1: "1",
          value2: "424.9",
        },
        {
          name: "盐城",
          value1: "2",
          value2: "19.9",
        },
        {
          name: "宿迁",
          value1: "3",
          value2: "182.57",
        },
        {
          name: "泰州",
          value1: "3",
          value2: "42.7",
        },
        {
          name: "扬州",
          value1: "1",
          value2: "0",
        },
        {
          name: "连云港",
          value1: "5",
          value2: "440.4",
        },
        {
          name: "淮安",
          value1: "1",
          value2: "0",
        },
        {
          name: "镇江",
          value1: "3",
          value2: "57.25",
        },
      ],
      projectList1: [
        {
          name: "智慧社区",
          value1: "11",
          value2: "1159.56",
        },
        {
          name: "智慧运维",
          value1: "10",
          value2: "3715.33",
        },
        {
          name: "数字乡村",
          value1: "5",
          value2: "488.25",
        },
        {
          name: "智慧园区",
          value1: "4",
          value2: "3629.14",
        },
        {
          name: "统一门户",
          value1: "1",
          value2: "148.8",
        },
        {
          name: "流程引擎",
          value1: "1",
          value2: "156",
        },
        {
          name: "统一门户",
          value1: "3",
          value2: "18.4",
        },
      ],
      rightData: [
        {
          province: "南京",
          solutionList: [
            {
              id: 1,
              province: "南京",
              projectList: [
                {
                  id: 1,
                  productsName: "智慧社区",
                  name: "梅园新村街道办事处智慧社区项目",
                  money: "29.9",
                  label: "",
                },
                {
                  id: 2,
                  productsName: "智慧园区",
                  name: "锦创数字产业园智慧园区项目",
                  money: "2719.5",
                  label: "",
                },
              ],
            },
          ],
        },
        {
          province: "苏州",
          solutionList: [
            {
              id: 2,
              province: "苏州",
              projectList: [
                {
                  id: 3,
                  productsName: "统一门户",
                  name: "数字检察一期",
                  money: "148.8",
                  label: "标杆案例",
                },
                {
                  id: 4,
                  productsName: "数字乡村",
                  name: "石公村樟坞生态农业示范基地项目",
                  money: "294",
                  label: "标杆案例",
                },
                {
                  id: 5,
                  productsName: "智慧园区",
                  name: "金峰智能制造产业园智慧园区项目",
                  money: "247",
                  label: "",
                },
                {
                  id: 6,
                  productsName: "智慧社区",
                  name: "悦年华智慧社区信息化项目",
                  money: "247",
                  label: "",
                },
                {
                  id: 7,
                  productsName: "玄武平台",
                  name: "苏州市立医院网络及运维平台设备采购及安装项目",
                  money: "3600",
                  label: "",
                },
              ],
            },
          ],
        },
        {
          province: "常州",
          solutionList: [
            {
              id: 3,
              province: "常州",
              projectList: [
                {
                  id: 8,
                  productsName: "智慧社区",
                  name: "经开区智慧社区电动车安全隐患防范治理项目",
                  money: "424.9",
                  label: "标杆案例",
                },
              ],
            },
          ],
        },
        {
          province: "南通",
          solutionList: [
            {
              id: 4,
              province: "南通",
              projectList: [
                {
                  id: 9,
                  productsName: "智慧社区",
                  name: "文峰街道中央商务社区居民委员会智慧社区项目",
                  money: "9.87",
                  label: "",
                },
                {
                  id: 10,
                  productsName: "智慧社区",
                  name: "苏锡通管委会园区智慧后勤管理小程序",
                  money: "7",
                  label: "",
                },
                {
                  id: 11,
                  productsName: "智慧社区",
                  name: "唐闸街道随时拍",
                  money: "2",
                  label: "",
                },
                {
                  id: 12,
                  productsName: "智慧社区",
                  name: "秦灶社区智慧社区",
                  money: "5",
                  label: "",
                },
              ],
            },
          ],
        },
        {
          province: "镇江",
          solutionList: [
            {
              id: 5,
              province: "镇江",
              projectList: [
                {
                  id: 13,
                  productsName: "玄武平台",
                  name: "扬中人民医院IT运维监控管理系统",
                  money: "40",
                  label: "",
                },
                {
                  id: 14,
                  productsName: "数字乡村",
                  name: "琅琊区数字乡村一体化平台",
                  money: "12.3",
                  label: "",
                },
                {
                  id: 15,
                  productsName: "智慧社区",
                  name: "镇江官塘桥街道社区治理项目",
                  money: "5",
                  label: "",
                },
              ],
            },
          ],
        },
        {
          province: "泰州",
          solutionList: [
            {
              id: 6,
              province: "泰州",
              projectList: [
                {
                  id: 16,
                  productsName: "玄武平台",
                  name: "市委组织部干部教育培训报送系统维保项目",
                  money: "3",
                  label: "",
                },
                {
                  id: 17,
                  productsName: "玄武平台",
                  name: "智慧城市运维平台",
                  money: "20",
                  label: "",
                },
                {
                  id: 18,
                  productsName: "智慧社区",
                  name: "泰州姜堰智慧社区平台",
                  money: "19.7",
                  label: "",
                },
              ],
            },
          ],
        },
        {
          province: "徐州",
          solutionList: [
            {
              id: 7,
              province: "徐州",
              projectList: [
                {
                  id: 19,
                  productsName: "数字乡村",
                  name: "利国镇数字乡村平台项目",
                  money: "72.1",
                  label: "",
                },
                {
                  id: 20,
                  productsName: "玄武平台",
                  name: "铜山区公安局信号控制系统运维项目",
                  money: "0.4",
                  label: "",
                },
              ],
            },
          ],
        },
        {
          province: "盐城",
          solutionList: [
            {
              id: 8,
              province: "盐城",
              projectList: [
                {
                  id: 21,
                  productsName: "数字乡村",
                  name: "新岗村“慧村”小程序",
                  money: "0.9",
                  label: "",
                },
                {
                  id: 22,
                  productsName: "玄武平台",
                  name: "生物工程技术学校运维管理平台",
                  money: "19",
                  label: "",
                },
              ],
            },
          ],
        },
        {
          province: "连云港",
          solutionList: [
            {
              id: 9,
              province: "连云港",
              projectList: [
                {
                  id: 23,
                  productsName: "数字乡村",
                  name: "板浦镇东新村数字乡村平台建设项目",
                  money: "109",
                  label: "",
                },
                {
                  id: 24,
                  productsName: "智慧社区",
                  name: "房山镇凤凰新村智慧社区项目",
                  money: "24",
                  label: "",
                },
                {
                  id: 25,
                  productsName: "5G消息",
                  name: "连云港文广旅局城市欢迎名片项目",
                  money: "18.4",
                  label: "标杆案例",
                },
                {
                  id: 26,
                  productsName: "智慧园区",
                  name: "诺泰医药智慧园区项目",
                  money: "280",
                  label: "",
                },
                {
                  id: 27,
                  productsName: "玄武平台",
                  name: "灌云县人民医院5G智慧运维项目",
                  money: "9",
                  label: "",
                },
              ],
            },
          ],
        },
        {
          province: "宿迁",
          solutionList: [
            {
              id: 10,
              province: "宿迁",
              projectList: [
                {
                  id: 28,
                  productsName: "玄武平台",
                  name: "泗阳县农村基层防汛预报预警体系平台运维服务项目",
                  money: "24",
                  label: "标杆案例",
                },
                {
                  id: 29,
                  productsName: "流程引擎",
                  name: "航道中运河水系影响工程信息化建设项目",
                  money: "156",
                  label: "",
                },
                {
                  id: 30,
                  productsName: "智慧社区",
                  name: "沭阳县钱集镇张圩社区为民服务云平台项目",
                  money: "2.7",
                  label: "",
                },
              ],
            },
          ],
        },
      ],
    });

    // 个数修改
    const countAdd = () => {
      data.countClick++;
      if (data.countClick == 3) {
        data.previewVisible = true;
        data.countClick = 0;
      }
    };
    const handleOk = () => {
      data.virtually.id = 1;
      countUpdate(data.virtually).then((res) => {
        data.previewVisible = false;
        getCountDate();
      });
    };

    const getCountDate = () => {
      getCount().then((res) => {
        for (let i = 0; i < data.countList.length; i++) {
          for (let j = 0; j < res.data.length; j++) {
            if (data.countList[i].title in res.data[j]) {
              let value1 = res.data[j][data.countList[i].title];
              let value2 = res.data[j][data.countList[i].textDec];
              data.countList[i].number = value1;
              data.countList[i].num = value2.substring(0, value2.length - 1);
            }
          }
        }
      });
    };
    getCountDate();

    const closeModal2 = () => {
      data.previewVisible = false;
    };
    let echart = echarts;
    const initChart = (id, XName, YData1, YData2, max_YData1, max_YData2) => {
      document.getElementById(id).removeAttribute("_echarts_instance_");
      let chart = echarts.init(document.getElementById(id));
      chart.setOption({
        tooltip: {
          trigger: "none", // 设置触发方式为'none'，鼠标经过时不显示任何内容
        },
        legend: {
          x: "right",
          data: ["项目数量", "项目金额"],
          icon: "circle",
          itemGap: 40,
        },
        grid: {
          left: "3.5%",
          right: "3%",
          containLabel: true,
          bottom: "10%",
          top: "18%",
        },
        xAxis: [
          {
            type: "category",
            data: XName,
            axisLine: {
              lineStyle: {
                show: false, //是否显示坐标轴轴线，
                color: "#CBD2DA", //x轴轴线的颜色
                width: 2, //x轴粗细
              },
            },
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              formatter: function (params) {
                var newParamsName = ""; // 拼接后的新字符串
                var paramsNameNumber = params.length; // 实际标签数
                var provideNumber = 4; // 每行显示的字数
                var rowNumber = Math.ceil(paramsNameNumber / provideNumber); // 如需换回，算出要显示的行数
                if (paramsNameNumber > provideNumber) {
                  /** 循环每一行,p表示行 */
                  for (var i = 0; i < rowNumber; i++) {
                    var tempStr = ""; // 每次截取的字符串
                    var start = i * provideNumber; // 截取位置开始
                    var end = start + provideNumber; // 截取位置结束
                    if (end > provideNumber * 2) {
                      newParamsName += "...";
                      break;
                      // 最后一行的需要单独处理
                    } else {
                      if (i === rowNumber - 1) {
                        tempStr = params.substring(start, paramsNameNumber);
                      } else {
                        tempStr = params.substring(start, end) + "\n";
                      }
                    }
                    newParamsName += tempStr;
                  }
                } else {
                  newParamsName = params;
                }
                return newParamsName;
              },
              textStyle: {
                color: "#24456A", // 文字颜色
                fontSize: 14, // 文字大小
              },
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "项目数量（个）",
            axisLabel: {
              formatter: "{value} ",
              algin: "left",
              overflow: "truncate",
              margin: 22,
            },
            splitLine: {
              lineStyle: {
                // 分隔线
                type: "dashed", // 线的类型
                color: "#D2DAE5", // 分隔线颜色
                width: 2,
              },
            },
            min: 0, //最小值
            max: max_YData1, //最大值
            splitNumber: 5, //坐标轴的分割段数
            interval: max_YData1 / 5, // 强制设置坐标轴分割间隔度(取本Y轴的最大值 max / 分割段数 splitNumber )
            nameTextStyle: {
              padding: 5, // 设置与坐标轴的距离，单位为像素
            },
          },
          {
            type: "value",
            name: "项目金额（万元）",
            axisLabel: {
              formatter: "{value}",
            },
            splitLine: {
              lineStyle: {
                type: "dashed", // 线的类型
                color: "#D2DAE5", // 分隔线颜色
                width: 2,
              },
            },
            min: 0, //最小值
            max: max_YData2, //最大值
            splitNumber: 5, //坐标轴的分割段数
            interval: max_YData2 / 5, // 强制设置坐标轴分割间隔度(取本Y轴的最大值 max / 分割段数 splitNumber )
            nameTextStyle: {
              padding: 5, // 设置与坐标轴的距离，单位为像素
            },
          },
        ],
        series: [
          {
            name: "项目数量",
            type: "bar",
            showBackground: true,
            data: YData1,
            barGap: "55%",
            barCategoryGap: "40%",
            barWidth: 17,
            yAxisIndex: 0,
            label: {
              show: true,
              position: "top", // 顶部显示
              formatter: "{c}", // 显示数据值
              textStyle: {
                color: "#00C5B9", // 数字颜色
                fontSize: 12, // 字体大小
                fontWeight: "bold", // 字体加粗
              },
            },
            itemStyle: {
              normal: {
                barBorderRadius: [10, 10, 10, 10],
                color: "#216FED",
              },
            },
            backgroundStyle: {
              color: "#F2F6FB",
              borderRadius: [10, 10, 10, 10],
            },
          },
          {
            name: "项目金额",
            type: "bar",
            showBackground: true,
            data: YData2,
            yAxisIndex: 1,
            barWidth: 17,
            label: {
              show: true,
              position: "top", // 顶部显示
              formatter: "{c}", // 显示数据值
              textStyle: {
                color: "#15D0C5", // 数字颜色
                fontSize: 12, // 字体大小
                fontWeight: "bold", // 字体加粗
              },
            },
            itemStyle: {
              normal: {
                barBorderRadius: [10, 10, 10, 10],
                color: "#15D0C5",
              },
            },
            backgroundStyle: {
              color: "#F2F6FB",
              borderRadius: [10, 10, 10, 10],
            },
          },
        ],
      });
      setTimeout(() => {
        window.addEventListener("resize", resizeFn);
      }, 100);
      const resizeFn = () => {
        return chart.resize();
      };
    };

    const outside = () => {
      let outsideXMoney = [];
      let outsideXNum = [];
      let outsideXName = [];
      data.projectList.forEach((ele) => {
        outsideXMoney.push(Number(ele.value2));
        outsideXNum.push(Number(ele.value1));
        outsideXName.push(ele.name);
      });
      initChart(
        "outside",
        outsideXName,
        outsideXNum,
        outsideXMoney,
        Math.ceil(Math.max(...outsideXNum) / 9.5) * 10,
        Math.ceil(Math.max(...outsideXMoney) / 9.5) * 10
      );
    };
    const inside = () => {
      let outsideXMoney = [];
      let outsideXNum = [];
      let outsideXName = [];
      data.projectList1.forEach((ele) => {
        outsideXMoney.push(Number(ele.value2));
        outsideXNum.push(Number(ele.value1));
        outsideXName.push(ele.name);
      });
      initChart(
        "Province",
        outsideXName,
        outsideXNum,
        outsideXMoney,
        Math.ceil(Math.max(...outsideXNum) / 9.5) * 10,
        Math.ceil(Math.max(...outsideXMoney) / 9.5) * 10
      );
    };
    onMounted(() => {
      outside();
      inside();
    });
    onBeforeUnmount(() => {
      // 离开页面必须进行移除，否则会造成内存泄漏，导致卡顿
      window.removeEventListener("resize", outside);
      window.removeEventListener("resize", inside);
    });
    const Router = useRouter();
    const more = () => {
      Router.push({
        name: "abilityHome",
      });
    };
    return {
      ...toRefs(data),
      countAdd,
      Router,
      more,
      handleOk,
      echart,
      outside,
      inside,
      closeModal2,
      initChart,
    };
  },
});
</script>
<style lang='scss' scoped>
.contents {
  background-color: #f5f7fc;
  display: flex;
  // margin-left: 120px;
  padding-bottom: 104px;
  padding-left: 30px;

  .title {
    font-weight: 500;
    font-size: 22px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 26px;
    margin-top: 56px;
    margin-bottom: 7px;
    display: flex;
    position: relative;

    img {
      width: 27px;
      height: 18px;
      margin-right: 9px;
      position: absolute;
      bottom: 23px;
    }

    p {
      margin-left: 36px;
    }
  }

  .left {
    // margin-right: 40px;
    //   width: calc(100vw - 960px);
    // width: 60%;

    .bac {
      background-image: url("../../assets/images/echarts/bac.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
      height: 100vh;
    }

    .echarts {
      padding: 56px 32px 76px 32px;
      // width: 711px;
      // height: 1110px;

      .first {
        p {
          display: inline-block;
          font-weight: 500;
          font-size: 20px;
          color: #24456a;
          line-height: 22px;

          .num {
            color: #216fed;
            margin-left: 15px;
            margin-right: 15px;
          }

          .money {
            color: #00c5b9;
            margin-left: 15px;
            margin-right: 15px;
          }
        }

        .begin {
          margin-right: 24px;
          margin-bottom: 18px;
        }
      }

      .line {
        width: 96%;
        height: 2px;
        background: #cad5ea;
      }

      .bottom {
        margin-top: 48px;
      }
    }
  }

  .right {
    flex: auto;
    flex: 1;
    // margin-right: 120px;

    .list {
      padding: 24px;
      background-color: #ffffff;
      height: 91vh;
      overflow-y: scroll;
      // width: 870px;
      margin-bottom: 0;
      .card {
        padding: 0;
        margin-bottom: 24px;

        .header {
          background-color: #ebeff6;
          height: 48px;
          display: flex;
          justify-content: space-between;
          font-weight: bold;
          font-size: 16px;
          line-height: 22px;
          padding: 13px 24px 13px 31px;
          position: relative;

          span {
            color: #24456a;
            margin-right: 6px;
          }

          p {
            font-weight: 500;
            color: #216fed;
          }

          .tag {
            border-radius: 4px 4px 4px 4px;
            border: 1px solid #216fed;
            font-weight: 400;
            font-size: 14px;
            color: #216fed;
            position: absolute;
            bottom: 13px;
          }
        }

        .footer {
          // height: 168px;

          background: linear-gradient(
            159deg,
            #ffffff 0%,
            #f6f7f9 62%,
            #f1f3f6 100%
          );
          box-shadow: 8px 8px 12px 0px rgba(0, 0, 0, 0.04),
            -8px -8px 12px 0px rgba(255, 255, 255, 0.5);
          font-weight: 400;
          font-size: 14px;
          color: rgba(36, 69, 106, 0.65);
          line-height: 22px;
          padding: 20px 0 20px 33px;

          .data {
            padding-bottom: 16px;
            width: 92%;
            // border-bottom: 2px solid #E8EDF5;

            .tit {
              margin-bottom: 6px;
            }
            .data1 {
              display: flex;
              justify-content: space-between;
            }
          }

          .solution {
            font-weight: 500;
            font-size: 16px;
            color: #216fed;
            line-height: 22px;
            margin-bottom: 16px;
          }

          .cont {
            font-weight: 500;
            font-size: 16px;
            color: #24456a;
            margin-bottom: 12px;
          }

          div {
            display: inline-block;
          }

          .box {
            background: #ffffff;
            box-shadow: 8px 8px 20px 0px rgba(4, 58, 107, 0.06);
            font-weight: 500;
            font-size: 16px;
            color: #24456a;
            line-height: 22px;
            width: 91%;
            height: 36px;
            margin-top: 4px;
            padding-top: 6px;
            padding-left: 12px;
          }

          .detail {
            width: 120px;
            margin-right: 60px;
          }

          .red {
            color: #ff2b00;
          }
        }

        .line {
          height: 2px;
          background: #e8edf5;
          // margin: 16px 32px;
          margin-left: 0;
          margin-left: 24px;
          margin-right: 24px;
          display: block !important;
        }
      }
    }
  }
}
ul {
  list-style: none;
}
.list::-webkit-scrollbar {
  display: block;
  position: absolute;
  right: 5px;
}

/* 滚动槽 */
.list::-webkit-scrollbar-track {
  -webkit-box-shadow: inset006pxrgba(221, 165, 165, 0) !important;
  background: #ffffff !important;
  border-radius: 0 !important;
}

/* 滚动条滑块 */
.list::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: #d7e1f2;
  -webkit-box-shadow: inset006pxrgb(0, 0, 0);
}

.layPage {
  width: 100%;
  text-align: right;
  // margin: 15px 0;
  background-color: #ffffff;
  padding-top: 3vh;
  padding-bottom: 2.5vh;
  padding-right: 24px;
  height: 9vh;
}

:deep(.text-center) {
  text-align: right;
}

::-webkit-scrollbar {
  width: 4px;
  /*  设置纵轴（y轴）轴滚动条 */
  height: 4px;
  /*  设置横轴（x轴）轴滚动条 */
}

/* 滚动条滑块（里面小方块） */
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: rgba(0, 0, 0, 0.9);
}
.box {
  width: 100%;
  justify-content: space-between;
  .list-wapper1 {
    height: 572px;
    background: #fff;
    width: 32.5%;
    .top-title {
      padding: 20px 30px 18px 30px;
      border-bottom: 1px solid #d9d9d9;
      justify-content: space-between;
      .chanpin {
        font-weight: bold;
        font-size: 18px;
        color: rgba(0, 0, 0, 0.85);
        margin: 0 20px 0 0;
      }
      .num {
        color: #2e7fff;
        font-size: 20px;
        margin: 0 10px 0 10px;
      }
      .more {
        cursor: pointer;
        width: 64px;
        height: 32px;
        background: #f5f7fc;
        border-radius: 2px 2px 2px 2px;
        text-align: center;
        font-weight: 400;
        color: #2e7fff;
        line-height: 24px;
      }
    }
    .chanpin-list {
      padding: 20px 30px 0 30px;
      .list-wapper {
        width: 100%;
        .title {
          font-weight: bold;
          border-left: 3px solid #2e7fff;
          padding: 0 0 0 10px;
          margin: 0 0 20px 0;
          font-size: 16px;
        }
        .list-li {
          padding: 10px 20px 10px 30px;
          margin: 0 0 20px 0;
          display: flex;
          background: #f5f7fc;
          position: relative;
          .li-img {
            margin: 0 20px 0 0;
            display: inline-block;
            vertical-align: middle;
            width: 100px;
            height: 70px;
            img {
              width: 100%;
              height: 100%;
            }
          }
          .li-info {
            // display: flex;
            vertical-align: middle;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            width: 75%;
            height: 65px;
            .top-title-li {
              display: flex;
              justify-content: space-between;
              font-size: 16px;
              color: rgba(0, 0, 0, 0.65);
              margin: 0 0 10px 0;
              .company {
                font-size: 13px;
                font-family: Source Han Sans CN-Regular, Source Han Sans CN;
                font-weight: 400;
                color: rgba(0, 0, 0, 0.45);
                line-height: 24px;
              }
            }
            .bottom {
              font-size: 12px;
              font-family: Source Han Sans CN-Regular, Source Han Sans CN;
              font-weight: 400;
              color: rgba(0, 0, 0, 0.45);
              line-height: 14px;
              display: flex;
              margin-top: 12.5px;
              justify-content: space-between;
              .tag {
                text-align: center;
                width: 85px;
                margin-right: 8px;
                font-weight: 500;
                font-size: 12px;
                font-family: Source Han Sans CN-Regular, Source Han Sans CN;
                font-weight: 400;
                color: #2e7fff;
                line-height: 17px;
                padding: 0 10px;
                height: 19px;
                margin-top: 0;
                background: rgba(46, 127, 255, 0.04);
                border-radius: 3px 3px 3px 3px;
                opacity: 1;
                border: 1px solid rgba(46, 127, 255, 0.65);
              }
              div {
                display: flex;
                .last {
                  display: flex;
                  align-items: center;
                }
                img {
                  width: 16px;
                  height: 16px;
                  display: inline-block;
                  margin: auto;
                  margin-right: 5px;
                }
                p {
                  display: inline-block;
                  margin: auto;
                }
              }
            }
          }
        }
      }
    }
  }
  .list-wapper2 {
    height: 572px;
    background: #fff;
    width: 32.5%;
    .top-title {
      padding: 20px 30px 18px 30px;
      border-bottom: 1px solid #d9d9d9;
      justify-content: space-between;
      .chanpin {
        font-weight: bold;
        font-size: 18px;
        color: rgba(0, 0, 0, 0.85);
        margin: 0 20px 0 0;
      }
      .num {
        color: #2e7fff;
        font-size: 20px;
        margin: 0 10px 0 10px;
      }
      .more {
        cursor: pointer;
        width: 64px;
        height: 32px;
        background: #f5f7fc;
        border-radius: 2px 2px 2px 2px;
        text-align: center;
        font-weight: 400;
        color: #2e7fff;
        line-height: 24px;
      }
    }
    .chanpin-list {
      padding: 20px 30px 0 30px;
      .list-wapper {
        width: 100%;
        .title {
          font-weight: bold;
          border-left: 3px solid #2e7fff;
          padding: 0 0 0 10px;
          margin: 0 0 20px 0;
          font-size: 16px;
        }
        .list-li {
          padding: 10px 20px 10px 30px;
          margin: 0 0 20px 0;
          display: flex;
          background: #f5f7fc;
          position: relative;
          .li-img {
            margin: 0 20px 0 0;
            display: inline-block;
            vertical-align: middle;
            width: 100px;
            height: 70px;
            img {
              width: 100%;
              height: 100%;
            }
          }
          .li-info {
            // display: flex;
            vertical-align: middle;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            width: 75%;
            height: 65px;
            .top-title-li {
              display: flex;
              justify-content: space-between;
              font-size: 16px;
              color: rgba(0, 0, 0, 0.65);
              margin: 0 0 10px 0;
              .company {
                font-size: 13px;
                font-family: Source Han Sans CN-Regular, Source Han Sans CN;
                font-weight: 400;
                color: rgba(0, 0, 0, 0.45);
                line-height: 24px;
              }
            }
            .bottom {
              font-size: 12px;
              font-family: Source Han Sans CN-Regular, Source Han Sans CN;
              font-weight: 400;
              color: rgba(0, 0, 0, 0.45);
              line-height: 14px;
              display: flex;
              margin-top: 12.5px;
              justify-content: space-between;
              .tag {
                text-align: center;
                width: 85px;
                margin-right: 8px;
                font-weight: 500;
                font-size: 12px;
                font-family: Source Han Sans CN-Regular, Source Han Sans CN;
                font-weight: 400;
                color: #2e7fff;
                line-height: 17px;
                padding: 0 10px;
                height: 19px;
                margin-top: 0;
                background: rgba(46, 127, 255, 0.04);
                border-radius: 3px 3px 3px 3px;
                opacity: 1;
                border: 1px solid rgba(46, 127, 255, 0.65);
              }
              div {
                display: flex;
                .last {
                  display: flex;
                  align-items: center;
                }
                img {
                  width: 16px;
                  height: 16px;
                  display: inline-block;
                  margin: auto;
                  margin-right: 5px;
                }
                p {
                  display: inline-block;
                  margin: auto;
                }
              }
            }
          }
        }
      }
    }
  }
  .list-wapper3 {
    height: 572px;
    background: #fff;
    width: 32.5%;
    .top-title {
      padding: 20px 30px 18px 30px;
      border-bottom: 1px solid #d9d9d9;
      justify-content: space-between;
      .chanpin {
        font-weight: bold;
        font-size: 18px;
        color: rgba(0, 0, 0, 0.85);
        margin: 0 20px 0 0;
      }
      .num {
        color: #2e7fff;
        font-size: 20px;
        margin: 0 10px 0 10px;
      }
      .more {
        cursor: pointer;
        width: 64px;
        height: 32px;
        background: #f5f7fc;
        border-radius: 2px 2px 2px 2px;
        text-align: center;
        font-weight: 400;
        color: #2e7fff;
        line-height: 24px;
      }
    }
    .chanpin-list {
      padding: 20px 30px 0 30px;
      .list-wapper {
        width: 100%;
        .title {
          font-weight: bold;
          border-left: 3px solid #2e7fff;
          padding: 0 0 0 10px;
          margin: 0 0 20px 0;
          font-size: 16px;
        }
        .list-li {
          padding: 10px 20px 10px 30px;
          margin: 0 0 20px 0;
          display: flex;
          background: #f5f7fc;
          position: relative;
          .li-img {
            margin: 0 20px 0 0;
            display: inline-block;
            vertical-align: middle;
            width: 100px;
            height: 70px;
            img {
              width: 100%;
              height: 100%;
            }
          }
          .li-info {
            // display: flex;
            vertical-align: middle;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            width: 75%;
            height: 65px;
            .top-title-li {
              display: flex;
              justify-content: space-between;
              font-size: 16px;
              color: rgba(0, 0, 0, 0.65);
              margin: 0 0 10px 0;
              .company {
                font-size: 13px;
                font-family: Source Han Sans CN-Regular, Source Han Sans CN;
                font-weight: 400;
                color: rgba(0, 0, 0, 0.45);
                line-height: 24px;
              }
            }
            .bottom {
              font-size: 12px;
              font-family: Source Han Sans CN-Regular, Source Han Sans CN;
              font-weight: 400;
              color: rgba(0, 0, 0, 0.45);
              line-height: 14px;
              display: flex;
              margin-top: 12.5px;
              justify-content: space-between;
              .tag {
                text-align: center;
                width: 85px;
                margin-right: 8px;
                font-weight: 500;
                font-size: 12px;
                font-family: Source Han Sans CN-Regular, Source Han Sans CN;
                font-weight: 400;
                color: #2e7fff;
                line-height: 17px;
                padding: 0 10px;
                height: 19px;
                margin-top: 0;
                background: rgba(46, 127, 255, 0.04);
                border-radius: 3px 3px 3px 3px;
                opacity: 1;
                border: 1px solid rgba(46, 127, 255, 0.65);
              }
              div {
                display: flex;
                .last {
                  display: flex;
                  align-items: center;
                }
                img {
                  width: 16px;
                  height: 16px;
                  display: inline-block;
                  margin: auto;
                  margin-right: 5px;
                }
                p {
                  display: inline-block;
                  margin: auto;
                }
              }
            }
          }
        }
      }
    }
  }
}
.bottom-left {
  position: relative;
  width: 100%;
  font-weight: bold;
  margin-top: 20px;
  .left {
    margin-right: 20px;
    background: #fff;
  }
  .right {
    background: #fff;
  }
  .top-title {
    padding: 20px 30px 18px 30px;
    border-bottom: 1px solid #d9d9d9;
    justify-content: space-between;
    .chanpin {
      font-weight: bold;
      font-size: 18px;
      color: rgba(0, 0, 0, 0.85);
      margin: 0 20px 0 0;
    }
    .num {
      color: #2e7fff;
      font-size: 20px;
      margin: 0 10px 0 10px;
    }
    .more {
      cursor: pointer;
      width: 64px;
      height: 32px;
      background: #f5f7fc;
      border-radius: 2px 2px 2px 2px;
      text-align: center;
      font-weight: 400;
      color: #2e7fff;
      line-height: 24px;
    }
  }
  .middle-table {
    padding: 20px 20px 20px 20px;
  }
}
:deep(.ant-tabs-tabpane .middle-table .bottom-left .pagination) {
  bottom: 1.314815vh !important;
}
:deep(.ant-tabs-tab) {
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.45);
  line-height: 24px;
}
:deep(.ant-tabs-nav-scroll) {
  padding-left: 1%;
}
:deep(.ant-tabs-bar) {
  margin-right: 1%;
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.45);
  line-height: 24px;
  font-weight: bold;
}
:deep(.ant-table-align-center) {
  font-size: 14px;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.85);
  line-height: 22px;
}
</style>
<style lang="scss" scoped>
.home-Ftop {
  width: 100%;
  height: 206px;
  background: white;
  padding-top: 48px;
  padding-bottom: 48px;
  display: flex;
  justify-content: space-between;
  padding-left: 10%;
  padding-right: 10%;
  margin-bottom: 56px;
  .tip {
    width: 25%;
    border-right: 1px solid #d9d9d9;
    text-align: center;
    position: relative;
    .num {
      font-size: 40px;
      margin-bottom: 3px;
      font-weight: bold;
      color: #2e7fff;
      line-height: 47px;
      text-shadow: 0px 4px 6px rgba(46, 127, 255, 0.25);
    }
    .red {
      width: 18px;
      height: 18px;
      vertical-align: middle;
      background: #ff5b00;
      border-radius: 12px 12px 12px 12px;
      opacity: 1;
      color: #ffc19f;
      text-align: center;
      position: absolute;
      left: 57%;
      bottom: 82%;
      line-height: 16px;
    }
    .red1 {
      width: 18px;
      line-height: 16px;
      height: 18px;
      background: #ff5b00;
      border-radius: 12px 12px 12px 12px;
      opacity: 1;
      color: #ffc19f;
      text-align: center;
      position: absolute;
      left: 62%;
      bottom: 82%;
    }
    .red2 {
      width: 18px;
      line-height: 16px;
      height: 18px;
      background: #ff5b00;
      border-radius: 12px 12px 12px 12px;
      opacity: 1;
      color: #ffc19f;
      text-align: center;
      position: absolute;
      left: 70%;
      bottom: 82%;
    }
    .blod {
      font-size: 18px;
      margin-bottom: 13px;
      font-weight: bold;
      color: rgba(0, 0, 0, 0.85);
      line-height: 21px;
    }
    .light {
      font-size: 16px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.45);
      line-height: 19px;
    }
    .num1 {
      font-size: 22px;
      margin: 0 8px;
      font-weight: bold;
      color: rgba(0, 0, 0, 0.65);
      line-height: 26px;
    }
  }
}
.topImage {
  width: 100%;
  height: 400px;
  // background-image: url("@/assets/images/ability/topBg.jpg");
  background-repeat: no-repeat;
  background-size: cover;

  .topImage_content {
    padding: 63px 0 16px 0;
    margin: 0 auto;
    width: 80%;

    .topImage_title {
      font-weight: bold;
      font-size: 42px;
      color: #122c6c;
      margin-bottom: 26px;
    }

    .topImage_details {
      width: 640px;
      font-size: 14px;
      color: #2b3f66;
      line-height: 24px;
      margin-top: 8px;
    }

    .top_tag {
      border-color: #236cff;
      color: #236cff;
      border-radius: 0;
    }
  }
}

.card_content {
  width: 80%;
  margin: -300px auto 0 auto;
  justify-content: space-between;

  .card_dec {
    width: 23%;
    background: #fff;
    box-shadow: 0px 4px 19px 0px rgba(63, 68, 74, 0.08);
    border-radius: 6px;
    padding-bottom: 12px;
    background-size: cover;
    background-repeat: no-repeat;

    .count_img {
      display: block;
      margin: 20px auto;
      width: 62px;
      height: 62px;
    }
  }

  .line1,
  .line2 {
    text-align: center;
    padding-bottom: 10px;
    margin: 10px 35px;
  }

  .line1 {
    font-family: Source Han Sans SC, Source Han Sans SC;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.55);
    border-bottom: 1px dashed #125688;
  }

  .line2 {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.35);
  }

  .simplified_text {
    font-weight: 600;
    font-size: 16px;
    color: #24456a;
  }

  .sub_text :hover {
    cursor: pointer;
    color: rgba(0, 0, 0, 0.65);
  }

  .main_count {
    font-weight: 600;
    font-family: PingFang SC, PingFang SC;
    font-size: 20px;
    color: #24456a;
    display: inline-block;
    margin-left: 8px;
  }

  .unit {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.55);
  }

  .sub_count {
    font-size: 16px;
    font-weight: 600;
    color: rgba(36, 69, 106, 0.45);
    display: inline-block;
    margin-left: 6px;
  }
}

.totaoText {
  font-weight: bold;
  font-size: 28px;
  color: rgba(0, 0, 0, 0.85);
  text-align: center;
}
</style>