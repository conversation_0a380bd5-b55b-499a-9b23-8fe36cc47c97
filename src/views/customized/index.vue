<template>
  <div class="body">
    <div class="loading-overlay" v-if="loadShow">
      <a-spin :spinning="loadShow" tip="附件加载中"></a-spin>
    </div>
    <div class="loading-overlay" v-if="loadShow1">
      <a-spin :spinning="loadShow1"></a-spin>
    </div>
    <div class="loading-overlay" v-if="progressPercent > -1">
      <div class="loading-super">
        <superLoading :endFunction="endFunction" />
      </div>
    </div>

    <div class="combine">
      <div class="top" ref="topContainer">
        <div class="searchInfo AIBg">
          <div class="vocationPull" style="flex: 1">
            <div class="switch">
              <div class="AIlogo"></div>
              <!--<a-switch checked-children="on" un-checked-children="off" v-model:checked="switchOnOff" />-->
            </div>
            <a-config-provider
              :locale="zhCN"
              :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            >
              <!-- <a-input v-model:value="data.name" class="inputClass" allow-clear height="56px" @focus="handleFocus(true)"
          @keyup.enter="seekContent1" placeholder="您好！我是麒麟AI助手，请问有什么可以帮您的吗？" /> -->
              <a-textarea
                v-model:value="name"
                class="inputClass"
                style="min-height: 55px; font-size: 14px"
                allow-clear
                auto-size
                @focus="handleFocus(true)"
                @keydown.enter.prevent="seekContent1"
                placeholder="您好！我是麒麟AI助手，请问有什么可以帮您的吗？"
              />
              <!--<voiceRecorder style="margin-right: 60px; height: 44px; background-color: #ffffff"
          :isTranslating="data.isTranslating" :canBtnUse="data.canBtnUse" @audioReady="handleAudio" />-->
              <new-voiceRecorder
                ref="recorderCom"
                style="
                  height: 44px;
                  background-color: #ffffff;
                  position: absolute;
                  top: 30px;
                  right: 100px;
                "
                :isTranslating="isTranslating"
                :canBtnUse="canBtnUse"
                @audioReady="handleAudio"
              ></new-voiceRecorder>
              <i
                class="sendbtn iconfont icon-fasong pointer"
                style="color: #0c70eb"
                @click="seekContent1()"
              ></i>
            </a-config-provider>
          </div>
        </div>
        <!-- <div class="flex just-sb">
          <span class="guide" @click="goGuide()">新方案定制</span>
          <div
            style="display: flex; align-items: center; cursor: pointer"
            @click="AIPost"
          >
            <span
              style="
                font-size: 16px;
                line-height: 40px;
                color: #122c6c;
                font-weight: 500;
              "
              >AI方案定制</span
            >
            <img
              width="24px"
              height="24px"
              src="@/assets/images/AI/ai.png"
              alt=""
              style="margin-left: 20px"
            />
          </div>
        </div> -->
      </div>
      <div class="listContent scrollable-content" ref="container">
        <div class="flex just-sb line">
          <div class="title">
            封面
            <span class="sub-count"></span>
          </div>
          <a-button
            type="primary"
            :class="{ fontDisabled: isDisabled }"
            @click="view('选择预览文件')"
          >
            <span>生成方案</span>
          </a-button>
        </div>
        <a-form :model="formData" class="formRef1" ref="formRef1" id="1">
          <a-form-item
            label="封面标题"
            name="title"
            :rules="[
              { required: true, message: '请输入封面标题', trigger: 'blur' },
            ]"
          >
            <a-input
              class="no-border"
              v-model:value="formData.title"
              @blur="setTitle('title')"
              placeholder="请输入封面标题"
            ></a-input>
          </a-form-item>
        </a-form>
      </div>
      <div class="listContent scrollable-content">
        <div class="flex just-sb line">
          <div class="title">
            方案概述（政策、需求等）
            <span class="sub-count">
              ({{
                AllcombineList.combineList
                  ? AllcombineList.combineList.length
                  : 0
              }})
            </span>
          </div>
        </div>
        <div
          class="tabContent"
          v-if="
            AllcombineList.combineList && AllcombineList.combineList.length > 0
          "
        >
          <div class="cardContent">
            <div class="card_total flex-1">
              <draggable :list="AllcombineList.combineList" animation="300">
                <template
                  v-for="(element, index) in AllcombineList.combineList"
                  :key="index"
                  #item="{ element }"
                >
                  <div :class="['card_content']" @click="goDetail(element)">
                    <img
                      v-if="element.new"
                      style="width: 48px; height: 20px"
                      class="newImg"
                      src="@/assets/images/combine/new.png"
                    />
                    <div style="display: flex; padding: 24px">
                      <div class="imgbox">
                        <img
                          style="width: 28px; height: 28px"
                          class="moduleImg"
                          src="@/assets/images/combine/modelBefore.png"
                        />
                        <a-image
                          :width="168"
                          :height="105"
                          :preview="false"
                          v-if="element.picture"
                          :src="`${element.picture}`"
                        />
                        <img
                          src="@/assets/images/home/<USER>"
                          style="width: 168px; height: 105px"
                          v-else
                        />
                      </div>
                      <div class="card_center">
                        <div class="card_text">
                          <div class="card_tag">
                            <div class="card_title">
                              {{ element.name }}
                            </div>
                            <div>
                              <span
                                class="cardTag"
                                style="
                                  background-color: #d7e6ff;
                                  color: #2e7fff;
                                "
                                >{{ element.category }}</span
                              >
                              <span class="author"
                                >联系人：{{ element.contact }}</span
                              >
                              <span
                                >时间：{{
                                  getYearMonthDay(element.createTime)
                                }}</span
                              >
                            </div>
                          </div>
                          <div class="flex">
                            <div
                              class="viewStyle"
                              @click.stop="viewFile(element.fileList[0])"
                            >
                              <img
                                class="add-icon"
                                src=" @/assets/images/newProject/view.png"
                              />
                              <span class="add"> &nbsp;预览</span>
                            </div>
                          </div>
                        </div>
                        <div class="card_des">
                          {{ element.introduce }}
                        </div>
                        <div
                          style="
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                          "
                          v-if="element.functionNames"
                        >
                          <div
                            style="
                              display: flex;
                              align-items: center;
                              justify-content: space-between;
                            "
                          >
                            <span
                              v-for="(tagItem, index) in element.functionNames"
                              :key="index"
                              class="tag"
                            >
                              {{ tagItem }}
                            </span>
                          </div>
                        </div>
                        <div
                          style="
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                          "
                        >
                          <div
                            style="
                              display: flex;
                              align-items: center;
                              justify-content: space-between;
                            "
                          >
                            <span
                              v-for="(fileItem, index) in element.fileList"
                              :key="index"
                              style="color: #0c70eb"
                              @click.stop="viewFile(fileItem)"
                            >
                              <!--
                                    <PaperClipOutlined />
                                    <span style="margin-left: 4px">{{ fileItem.fileName }}</span>
                                    -->
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
              </draggable>
            </div>
          </div>
        </div>
      </div>

      <div class="listContent scrollable-content">
        <div class="flex just-sb line">
          <div class="title">
            应用场景
            <span class="sub-count"
              >({{
                AllcombineList.combineList5
                  ? AllcombineList.combineList5.length
                  : 0
              }})</span
            >
          </div>
        </div>
        <div
          class="tabContent"
          v-if="
            AllcombineList.combineList5 &&
            AllcombineList.combineList5.length > 0
          "
        >
          <div class="cardContent">
            <div class="card_total flex-1">
              <draggable :list="AllcombineList.combineList5" animation="300">
                <template
                  v-for="(element, index) in AllcombineList.combineList5"
                  :key="index"
                  #item="{ element }"
                >
                  <div :class="['card_content']" @click="goDetail(element)">
                    <img
                      v-if="element.new"
                      style="width: 48px; height: 20px"
                      class="newImg"
                      src="@/assets/images/combine/new.png"
                    />
                    <div style="display: flex; padding: 24px">
                      <div class="imgbox">
                        <img
                          style="width: 28px; height: 28px"
                          class="moduleImg"
                          src="@/assets/images/combine/modelBefore.png"
                        />
                        <a-image
                          :width="168"
                          :height="105"
                          :preview="false"
                          v-if="element.picture"
                          :src="`${element.picture}`"
                        />
                        <img
                          src="@/assets/images/home/<USER>"
                          style="width: 168px; height: 105px"
                          v-else
                        />
                      </div>
                      <div class="card_center">
                        <div class="card_text">
                          <div class="card_tag">
                            <div class="card_title">
                              {{ element.name }}
                              <span
                                v-if="showNew.includes(element.schemeId)"
                                style="color: red"
                                >NEW</span
                              >
                            </div>
                            <div>
                              <span
                                class="cardTag"
                                style="
                                  background-color: #d7e6ff;
                                  color: #2e7fff;
                                "
                                >{{ element.category }}</span
                              >
                              <span class="author"
                                >联系人：{{ element.contact }}</span
                              >
                              <span
                                >时间：{{
                                  getYearMonthDay(element.createTime)
                                }}</span
                              >
                            </div>
                          </div>
                          <div class="flex">
                            <div
                              class="viewStyle"
                              @click.stop="viewFile(element.fileList[0])"
                            >
                              <img
                                class="add-icon"
                                src=" @/assets/images/newProject/view.png"
                              />
                              <span class="add"> &nbsp;预览</span>
                            </div>
                            <div class="cityStyle" @click.stop="move(element)">
                              <img
                                class="add-icon"
                                src=" @/assets/images/AI/cancelAdd.png"
                              />
                              <span class="add"> &nbsp;移出组合</span>
                            </div>
                          </div>
                        </div>
                        <div class="card_des">
                          {{ element.introduce }}
                        </div>
                        <div
                          style="
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                          "
                          v-if="element.functionNames"
                        >
                          <div
                            style="
                              display: flex;
                              align-items: center;
                              justify-content: space-between;
                            "
                          >
                            <span
                              v-for="(tagItem, index) in element.functionNames"
                              :key="index"
                              class="tag"
                            >
                              {{ tagItem }}
                            </span>
                          </div>
                        </div>
                        <div
                          style="
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                          "
                        >
                          <div
                            style="
                              display: flex;
                              align-items: center;
                              justify-content: space-between;
                            "
                          >
                            <span
                              v-for="(fileItem, index) in element.fileList"
                              :key="index"
                              style="color: #0c70eb"
                              @click.stop="viewFile(fileItem)"
                            >
                              <!--  
  
                                <PaperClipOutlined />
                                <span style="margin-left: 4px">{{fileItem.fileName}}</span>
                                -->
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
              </draggable>
            </div>
            <div class="btncontent">
              <div class="btn" @click="goChose(5, '应用场景')">
                <PlusCircleOutlined /> 点击添加应用场景
              </div>
            </div>
          </div>
        </div>
        <div v-else>
          <div class="btncontent">
            <div
              class="btn"
              @click="goChose(5, '应用场景')"
              style="margin-bottom: 24px"
            >
              <PlusCircleOutlined /> 点击添加应用场景
            </div>
          </div>
        </div>
      </div>

      <div class="listContent scrollable-content">
        <div class="flex just-sb line" ref="btnContent">
          <div class="title">
            应用案例
            <span class="sub-count"
              >({{
                AllcombineList.combineList8
                  ? AllcombineList.combineList8.length
                  : 0
              }})</span
            >
          </div>
        </div>
        <div
          class="tabContent"
          v-if="
            AllcombineList.combineList8 &&
            AllcombineList.combineList8.length > 0
          "
        >
          <div class="cardContent">
            <div class="card_total flex-1">
              <draggable :list="AllcombineList.combineList8" animation="300">
                <template
                  v-for="(element, index) in AllcombineList.combineList8"
                  :key="index"
                  #item="{ element }"
                >
                  <div :class="['card_content']" @click="goDetail(element)">
                    <img
                      v-if="element.new"
                      style="width: 48px; height: 20px"
                      class="newImg"
                      src="@/assets/images/combine/new.png"
                    />
                    <div style="display: flex; padding: 24px">
                      <div class="imgbox">
                        <img
                          style="width: 28px; height: 28px"
                          class="moduleImg"
                          src="@/assets/images/combine/modelBefore.png"
                        />
                        <a-image
                          :width="168"
                          :height="105"
                          :preview="false"
                          v-if="element.picture"
                          :src="`${element.picture}`"
                        />
                        <img
                          src="@/assets/images/home/<USER>"
                          style="width: 168px; height: 105px"
                          v-else
                        />
                      </div>
                      <div class="card_center">
                        <div class="card_text">
                          <div class="card_tag">
                            <div
                              class="card_title"
                              v-if="element.projectName != null"
                            >
                              {{ element.projectName }}
                            </div>
                            <div class="card_title" v-else>
                              {{ element.name }}
                            </div>
                            <div>
                              <span
                                class="cardTag"
                                style="
                                  background-color: #d7e6ff;
                                  color: #2e7fff;
                                "
                                >{{ element.category }}</span
                              >
                              <span class="author"
                                >联系人：{{ element.contact }}</span
                              >
                              <span
                                >时间：{{
                                  getYearMonthDay(element.createTime)
                                }}</span
                              >
                            </div>
                          </div>
                          <div class="flex">
                            <div
                              class="viewStyle"
                              @click.stop="viewFile(element.fileList[0])"
                            >
                              <img
                                class="add-icon"
                                src=" @/assets/images/newProject/view.png"
                              />
                              <span class="add"> &nbsp;预览</span>
                            </div>
                            <div class="cityStyle" @click.stop="move(element)">
                              <img
                                class="add-icon"
                                src=" @/assets/images/AI/cancelAdd.png"
                              />
                              <span class="add"> &nbsp;移出组合</span>
                            </div>
                          </div>
                        </div>
                        <div
                          class="card_des"
                          v-if="element.projectIntroduction != null"
                        >
                          {{ element.projectIntroduction }}
                        </div>
                        <div class="card_des" v-else>
                          {{ element.introduce }}
                        </div>
                        <div
                          style="
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                          "
                          v-if="element.functionNames"
                        >
                          <div
                            style="
                              display: flex;
                              align-items: center;
                              justify-content: space-between;
                            "
                          >
                            <span
                              v-for="(tagItem, index) in element.functionNames"
                              :key="index"
                              class="tag"
                            >
                              {{ tagItem }}
                            </span>
                          </div>
                        </div>
                        <div
                          style="
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                          "
                        >
                          <div
                            style="
                              display: flex;
                              align-items: center;
                              justify-content: space-between;
                            "
                          >
                            <span
                              v-for="(fileItem, index) in element.fileList"
                              :key="index"
                              style="color: #0c70eb"
                              @click.stop="viewFile(fileItem)"
                            >
                              <!--
                                <PaperClipOutlined />
                                <span style="margin-left: 4px">{{fileItem.fileName}}</span>
                                -->
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
              </draggable>
            </div>
            <div class="btncontent">
              <div class="btn" @click="goChose(8, '应用案例')">
                <PlusCircleOutlined /> 点击添加应用案例
              </div>
            </div>
          </div>
        </div>
        <div v-else class="btncontent">
          <div class="btncontent">
            <div
              class="btn"
              @click="goChose(8, '应用案例')"
              style="margin-bottom: 24px"
            >
              <PlusCircleOutlined /> 点击添加应用案例
            </div>
          </div>
        </div>
      </div>
      <div class="listContent scrollable-content">
        <div class="flex just-sb line">
          <div class="title">结束语<span class="sub-count"></span></div>
        </div>
        <a-form :model="formData" ref="formRef2" class="formRef2">
          <a-form-item
            label="结束语:"
            name="conclusion"
            :rules="[
              { required: true, message: '请输入结束语', trigger: 'blur' },
            ]"
          >
            <a-textarea
              v-model:value="formData.conclusion"
              placeholder="请输入结束语"
              :rows="1"
              @blur="setTitle('end')"
            />
          </a-form-item>
        </a-form>
      </div>
      <div class="buttons flex just-sb">
        <div class="flex left">
          <a-button @click="clear">
            <span>清除</span>
          </a-button>
          <a-button
            :class="{ fontDisabled: isDisabled }"
            @click="view('选择预览文件')"
          >
            <span>生成方案</span>
          </a-button>
        </div>
        <div class="flex right">
          <a-button
            :class="{ isDisabled: isDisabled }"
            @click="save('编辑组合名称')"
          >
            <span>合并保存</span>
          </a-button>
          <a-button :class="{ isDisabled: isDisabled }" @click="download">
            <span>合并下载</span>
          </a-button>
        </div>
      </div>
    </div>

    <write-apply
      ref="applyModal"
      v-if="applyShow"
      :sourceIds="sourceIds"
      @submit-cancel="closeApplyModal"
    />

    <a-modal
      :visible="previewVisible"
      @cancel="closeModal"
      :title="title"
      :width="modalWidth"
      :destroyOnClose="true"
      :maskClosable="false"
      :footer="null"
    >
      <templete #title>
        <img
          src="../../assets/images/combine/title.png"
          alt=""
          style="width: 14px; height: 8px"
        />
      </templete>
      <div v-if="type == '1'">
        <a v-if="fileUrlObj.wordFile" @click="viewFile(fileUrlObj.wordFile)"
          >组合文件.doc</a
        >
        <br />
        <a v-if="fileUrlObj.pptFile" @click="viewFile(fileUrlObj.pptFile)"
          >组合文件.ppt</a
        >
      </div>
      <div v-if="type == '2'">
        <a-form :model="formData" labelAlign="right" ref="groupForm">
          <a-row>
            <a-col :span="24">
              <a-form-item
                label="组合名称"
                name="name"
                :rules="[{ required: true, message: '请输入组合名称' }]"
              >
                <a-input
                  v-model:value="formData.name"
                  placeholder="请输入组合名称"
                >
                </a-input>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
        <div class="flex just-center">
          <a-button style="margin-right: 20px" @click="closeModal"
            >取消</a-button
          >
          <a-button type="primary" @click="submit()">
            <span>提交</span>
          </a-button>
        </div>
      </div>
      <div v-if="type == '3'">
        <chose-table
          :choseType="choseType"
          :labelId="labelId"
          :cover="formData.title"
          :conclusion="formData.conclusion"
          :showAbility="showAbility"
          @close="close"
        ></chose-table>
      </div>
      <div v-if="type == '4'">
        <guide-table @close="closeGuide"></guide-table>
      </div>
      <div v-if="type == '5'">
        <ai-table :showAIIdList="showAIIdList" @close="closeAI"></ai-table>
      </div>
      <div v-if="type == '6'">
        <choose-project
          @close="close"
          :cover="formData.title"
          :conclusion="formData.conclusion"
        ></choose-project>
      </div>
    </a-modal>
    <a-modal
      :visible="manualOrAiVisible"
      @cancel="manualOrAiVisible = false"
      title="选择定制方法"
      width="634px"
      style="top: 30vh"
      :destroyOnClose="true"
      :maskClosable="false"
      :footer="null"
      :closable="false"
    >
      <templete #header>
        <img
          src="@/assets/images/home/<USER>"
          style="width: 14px; height: 8px; margin-right: 10px"
        />
      </templete>
      <div class="flex just-sb" style="padding: 0 13px 13px">
        <div class="manual" @click="goGuide()">
          <img src="@/assets/images/newProject/manual.png" />
          <div>手动定制</div>
          <div>通过方案定制流程添加方案并选择场景</div>
        </div>
        <div class="ai" @click="AIPost()">
          <img src="@/assets/images/newProject/aiLogo.png" />
          <div>AI定制</div>
          <div>通过麒麟AI助手智能分析进行方案定制</div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import {
  defineComponent,
  reactive,
  toRefs,
  ref,
  onMounted,
  watch,
  computed,
} from "vue";
import draggable from "vuedraggable";
import writeApply from "./components/writeApply.vue";
import { useRouter, useRoute } from "vue-router";
import { VueDraggable } from "vue-draggable-plus";
import {
  myCombineList,
  mergeFile,
  combineSave,
  deleteCombine,
  isShowGuide,
  updateTitle,
} from "@/api/combine/combine.js";
import { pptTopdf } from "@/api/fileUpload/uploadFile.js";
import { PlusCircleOutlined, PaperClipOutlined } from "@ant-design/icons-vue";
import Recommend from "./components/recommendList.vue";
import ChoseTable from "./components/choseTable.vue";
import superLoading from "@/components/superLoading/superLoading.vue";
import { message } from "ant-design-vue";
import eventBus from "@/utils/eventBus";
import GuideTable from "./components/guideTable.vue";
import zhCN from "ant-design-vue/es/locale/zh_CN";
export default defineComponent({
  components: {
    writeApply,
    Recommend,
    PlusCircleOutlined,
    ChoseTable,
    PaperClipOutlined,
    VueDraggable,
    draggable,
    superLoading,
    GuideTable,
  },
  props: {
    isAIpush: {
      type: Boolean,
      default: false,
    },
  },
  setup(props) {
    const Router = useRouter();
    const groupForm = ref();
    const applyModal = ref();
    const container = ref(null);
    const btnContent = ref(null);

    const moduleRefs = ref([]);
    const topContainer = ref(null); // 引用按钮容器，获取其高度
    const formRef1 = ref();
    const formRef2 = ref();
    const recorderCom = ref(null);

    const data = reactive({
      manualOrAiVisible: false,
      name: "",
      historyShow: false,
      showNew: [],
      isAIpush: props.isAIpush,
      activeKey: "1", //政策背景、市场分析、需求分析
      activeKeyList: [
        { index: "1", name: "政策背景", listName: "combineList1" },
        { index: "2", name: "市场分析", listName: "combineList2" },
        { index: "3", name: "需求分析", listName: "combineList3" },
      ],
      AIUrl: false,
      showAIIdList: [],
      endFunction: false,
      progressPercent: -1,
      previewUrl: "",
      title: "",
      type: "",
      labelId: "",
      isDisabled: undefined,
      loading: false,
      viewLoading: false,
      downLoading: false,
      applyShow: false,
      previewVisible: false,
      solutionList: {},
      moduleList: [],
      solutionCount: 0,
      moduleCount: 0,
      fileUrlObj: {},
      formData: {
        conclusion: "谢谢聆听",
        title: "",
      },
      showAbility: false,
      // 方案 能力ids对象
      ids: {},
      sourceIds: [],
      combineList: [
        {
          title: "政策背景",
          list: [],
        },
        {
          title: "市场分析",
          list: [],
        },
        {
          title: "需求分析",
          list: [],
        },
        {
          title: "方案概述",
          list: [],
        },

        {
          title: "应用场景",
          list: [],
        },
        {
          title: "部署方案",
          list: [],
        },
        {
          title: "合作模式",
          list: [],
        },
        {
          title: "应用案例",
          list: [],
        },
      ],
      Conclusion: "", //结束语
      modalWidth: "600px",
      choseType: null,
      isAll: null,
      selcetIndex: null,
      loadShow1: false,
      AllcombineList: {
        combineList1: [],
        combineList2: [],
        combineList3: [],
        combineList5: [],
        combineList8: [],
      },
    });

    console.log(props.isAIpush);

    watch(
      () => props.isAIpush,
      (newV) => {
        data.isAIpush = newV;
        console.log(newV);
        if (newV) {
          getList();
        }
      }
    );

    const scrollToElement = () => {
      const topContainerHeight = container.value?.offsetHeight || 0; // 获取按钮容器的高度
      // 确保滚动的内容是在可滚动的容器中
      const scrollableContent = document.querySelector(".ant-layout-content");
      if (btnContent && scrollableContent) {
        const targetPosition =
          btnContent.value.offsetTop - topContainerHeight - 500; // 计算滚动的目标位置
        // 滚动可滚动容器内的内容
        scrollableContent.scrollTo({
          top: targetPosition,
          behavior: "smooth",
        });
      }
    };

    const showNewType = () => {
      if (localStorage.getItem("lastArr")) {
        let lastArr = JSON.parse(localStorage.getItem("lastArr"));
        data.showNew = [];
        lastArr.forEach((item) => {
          data.showNew.push(item.schemeId);
        });
        setTimeout(() => {
          scrollToElement();
        }, 500);
        localStorage.removeItem("lastArr");
      }
    };

    const getList = () => {
      myCombineList().then((res) => {
        data.combineList = res.data.list;
        if (
          res.data.list[0] &&
          res.data.list[0].list[0] &&
          res.data.list[0].list[0].parentCategoryId
        ) {
          data.labelId = res.data.list[0].list[0].parentCategoryId;
        }
        if (
          res.data.list[0] &&
          res.data.list[0].list[0] &&
          res.data.list[0].list[0].cover
        ) {
          data.formData.title = res.data.list[0].list[0].cover;
        }
        if (
          res.data.list[0] &&
          res.data.list[0].list[0] &&
          res.data.list[0].list[0].conclusion
        ) {
          data.formData.conclusion = res.data.list[0].list[0].conclusion;
        } else {
          data.formData.conclusion = "谢谢聆听";
        }
        data.combineList.forEach((el) => {
          el.list.forEach((item) => {
            if (item.label) {
              item.label = item.label.split(",");
            }
          });
          data.AllcombineList["combineList" + el.type] = el.list;
        });
        console.log(data.AllcombineList);
        
        data.combineList = [{ title: "封面", list: [] }, ...data.combineList];
        data.AllcombineList.combineList = [
          ...data.AllcombineList.combineList1,
          ...data.AllcombineList.combineList2,
          ...data.AllcombineList.combineList3,
        ];

        showNewType();
      });
    };
    getList();
    eventBus.on("customRefresh", getList);

    const move = (item, type) => {
      deleteCombine(item.id)
        .then((res) => {
          getList();
        })
        .catch((error) => {});
    };

    const clear = () => {
      getIds();
      data.formData.conclusion = "";
      data.formData.title = "";
      if (data.ids.length > 0) {
        deleteCombine(data.ids.join(","))
          .then((res) => {
            getList();
            setTimeout(() => {
              data.formData.conclusion = "";
              data.formData.title = "";
            }, 500);
            eventBus.emit("cartRefresh");
          })
          .catch((error) => {});
      }
    };

    // 过滤方案ids和能力ids;
    const getIds = () => {
      data.ids = [];
      data.sourceIds = [];
      data.showAIIdList = [];
      data.combineList.forEach((el) => {
        if (el.list) {
          el.list.forEach((item) => {
            data.ids.push(item.id);
            data.showAIIdList.push(item.schemeId);
            if (item.classify == 1) {
              data.sourceIds.push({
                type: 1,
                sourceId: item.schemeId,
              });
            } else if (item.classify == 2 || item.classify == 4) {
              data.sourceIds.push({
                type: 2,
                sourceId: item.schemeId,
              });
            }
          });
        }
      });
    };

    // 预览
    const view = (title) => {
      formRef1.value.validate().then(() => {
        formRef2.value.validate().then(() => {
          data.title = title;
          let requiredValid =
            (data.combineList[1].list.length > 0 ||
              data.combineList[2].list.length > 0 ||
              data.combineList[3].list.length > 0) &&
            data.combineList[5].list.length > 0;
          if (!requiredValid) {
            message.error("必填能力不能为空");
          } else {
            getIds();
            data.progressPercent = 0;
            let percentTime = setInterval(() => {
              if (data.progressPercent == 99) {
                data.progressPercent = 99;
              } else {
                data.progressPercent += 1;
              }
            }, 1000);
            combineSave({
              sourceIdList: data.sourceIds,
              conclusion: "谢谢聆听",
              name: formData.title,
              title: formData.title,
            })
              .then((res) => {
                data.loadShow1 = false;
                clearInterval(percentTime);
                data.progressPercent = 100;
                data.endFunction = true;
                data.progressPercent = -1;
                data.viewLoading = false;
                window.open(res.data.pptFile);
                //合并保存后清空
                clear();
                message.success("保存成功");
              })
              .catch((error) => {
                closeModal();
                clearInterval(percentTime);
                data.progressPercent = -1;
                data.viewLoading = false;
              });
            // mergeFile({
            //   sourceIdList: data.sourceIds,
            //   ...data.formData,
            // }).then((res) => {
            //   if (res.code == 200) {
            //     clearInterval(percentTime);
            //     data.progressPercent = 100;
            //     data.endFunction = true;
            //     data.progressPercent = -1;
            //     data.viewLoading = false;
            //     window.open(res.data.pptFile);
            //     return false;
            //   } else {
            //     clearInterval(percentTime);
            //     data.progressPercent = -1;
            //     data.viewLoading = false;
            //   }
            // });
          }
        });
      });
    };
    // 预览文件
    const viewFile = (val) => {
      data.viewLoading = true;
      pptTopdf({
        filePath: val.filePath,
        fileUrl: val.fileUrl,
      }).then((res) => {
        data.viewLoading = false;
        if (res.code == 200) {
          let windowOrigin = window.location.origin;
		      let token = localStorage.getItem("token");
		      let newHref = res.data;
					if(res.data.includes(windowOrigin)){
					  newHref = "/portal" + res.data.split(windowOrigin)[1]
					}
		      const newpage = Router.resolve({
		        name: "lookPdf",
		        query: {
		          urlMsg: encodeURIComponent(
		          	windowOrigin + newHref + "?token=" + token
		          ),
		          urlName: val.name,
		        },
		      });
          window.open(newpage.href, "_blank");
        }
      });
    };

    const showAI = () => {
      getIds();
      if (data.showAIIdList.length == 0) {
        message.error("请选择方案或能力");
        return false;
      }
      data.type = 5;
      data.modalWidth = "1400px";
      data.title = "麒麟AI助手建议";
      data.previewVisible = true;
    };

    // 保存
    const save = (title) => {
      formRef1.value.validate().then(() => {
        formRef2.value.validate().then(() => {
          data.title = title;
          let requiredValid =
            (data.combineList[1].list.length > 0 ||
              data.combineList[2].list.length > 0 ||
              data.combineList[3].list.length > 0) &&
            data.combineList[5].list.length > 0;
          if (!requiredValid) {
            message.error("必填能力不能为空");
          } else {
            data.type = "2";
            data.modalWidth = "600px";
            data.previewVisible = true;
          }
        });
      });
    };

    const submit = (val) => {
      formRef1.value.validate().then(() => {
        formRef2.value.validate().then(() => {
          groupForm.value.validate().then(() => {
            let requiredValid =
              (data.combineList[1].list.length > 0 ||
                data.combineList[2].list.length > 0 ||
                data.combineList[3].list.length > 0) &&
              data.combineList[5].list.length > 0;

            if (!requiredValid) {
              message.error("必填能力不能为空");
            } else {
              getIds();
              data.loading = true;
              data.loadShow1 = true;
              (data.viewLoading = false), (data.downLoading = false);
              data.loading = false;
              closeModal();
              combineSave({
                sourceIdList: data.sourceIds,
                ...data.formData,
              })
                .then((res) => {
                  data.loadShow1 = false;
                  //合并保存后清空
                  clear();
                  message.success("保存成功");
                })
                .catch((error) => {
                  closeModal();
                });
            }
          });
        });
      });
    };

    const download = () => {
      getIds();
      data.title = title;
      if (data.isAll) {
        message.error("必填能力不能为空");
      } else {
        data.downLoading = true;
        data.viewLoading = false;
        mergeFile({
          sourceIdList: data.sourceIds,
          ...data.formData,
        }).then((res) => {
          data.downLoading = false;
          window.open(res.data.pptFile);
        });
      }
    };
    const toApply = () => {
      getIds();
      data.applyShow = true;
      applyModal.value.previewVisible = true;
    };
    const closeModal = () => {
      data.previewVisible = false;
      getList();
      data.type = "";
    };

    const closeApplyModal = () => {
      data.applyShow = false;
    };

    const toList = (name) => {
      Router.push({
        name: name,
      });
    };

    const deleteRecord = () => {
      myCombineList()
        .then((res) => {})
        .catch((error) => {});
    };
    // 设置 moduleRefs 时，确保其长度一致，并且在索引有效时进行赋值
    const setModuleRef = (el, index) => {
      if (el && index >= 0 && index < data.combineList.length) {
        moduleRefs.value[index] = el;
      }
    };
    const scrollUp = (index) => {
      const section = moduleRefs.value[index];
      const topContainerHeight = topContainer.value?.offsetHeight || 0; // 获取按钮容器的高度
      let mathHeight = index == 0 ? 94 : 84;
      data.selcetIndex = index;
      // 确保滚动的内容是在可滚动的容器中
      const scrollableContent = document.querySelector(".ant-layout-content");

      if (section && scrollableContent) {
        const targetPosition =
          section.offsetTop - topContainerHeight - mathHeight; // 计算滚动的目标位置

        // 滚动可滚动容器内的内容
        scrollableContent.scrollTo({
          top: targetPosition,
          behavior: "smooth",
        });
      }
    };
    // 确保moduleRefs的长度足够
    const adjustModuleRefsLength = () => {
      if (moduleRefs.value.length < data.combineList.length) {
        moduleRefs.value = new Array(data.combineList.length).fill(null);
      }
    };
    const goChose = (type, title) => {
      data.type = 3;
      data.modalWidth = "1200px";
      data.choseType = type;
      data.title = `添加${title}`;
      data.previewVisible = true;
      if (title == "应用场景") {
        data.showAbility = true;
      } else {
        data.showAbility = false;
      }
    };
    const goGuide = () => {
      data.type = 4;
      data.modalWidth = "1400px";
      data.choseType = 1;
      data.title = "选择方案";
      data.previewVisible = true;
    };
    // 在组件挂载时或者combineList更新时确保长度足够
    onMounted(() => {
      adjustModuleRefsLength();
      showGuide();
    });

    watch(
      () => data.combineList,
      (newV) => {
        adjustModuleRefsLength();
      }
    );

    //是否显示新方案
    const showGuide = () => {
      isShowGuide().then((res) => {
        if (res.code == 200) {
          if (res.data) {
            data.manualOrAiVisible = true;
            // goGuide();
          } else {
            AIPost();
          }
        }
      });
    };

    const isAll = computed(() => {
      let flag = true;

      data.combineList.forEach((el) => {
        if (
          (el.title == "市场分析" && el.list.length > 0) ||
          (el.title == "政策背景" && el.list.length > 0) ||
          (el.title == "需求分析" && el.list.length > 0)
        ) {
          flag = false;
        }
      });
      data.isAll = flag;
      return flag;
    });
    const isApplication = computed(() => {
      // isApplication//应用案例是否已填
      let flag = true;
      data.combineList.forEach((el) => {
        if (el.title == "应用场景" && el.list.length > 0) {
          flag = false;
        }
      });
      data.isAll = flag;
      return flag;
    });

    const loadShow = computed(() => {
      return data.viewLoading || data.downLoading || data.loading;
    });
    const getYearMonthDay = (datetimeStr) => {
      const datetime = new Date(datetimeStr);
      const year = datetime.getFullYear();
      // getMonth() 返回的月份是从0开始的，因此需要加1
      const month = datetime.getMonth() + 1;
      const day = datetime.getDate();

      // 将月份和日期转换为两位数字
      const monthStr = month < 10 ? "0" + month : month;
      const dayStr = day < 10 ? "0" + day : day;

      return `${year}-${monthStr}-${dayStr}`;
    };
    const goDetail = (item) => {
      if (item.classify == 1) {
        Router.push({
          query: {
            id: item.schemeId,
          },
          name: "modulelNew",
        });
      } else if (item.classify == 2 || item.classify == 4) {
        Router.push({
          query: {
            id: item.schemeId,
          },
          name: "applyNew",
        });
      } else if (item.classify == 3) {
        Router.push({
          query: {
            id: item.schemeId,
          },
          name: "productDetail",
        });
      } else {
        Router.push({
          query: {
            id: item.schemeId,
          },
          name: "solveDetailNew",
        });
      }
    };
    const close = () => {
      data.previewVisible = false;
      setTimeout(() => {
        getList();
      }, 1000);
    };
    const closeGuide = () => {
      data.previewVisible = false;
      setTimeout(() => {
        getList();
      }, 1000);
    };
    const closeAI = () => {
      data.previewVisible = false;
      setTimeout(() => {
        getList();
      }, 1000);
    };
    const closeAImodel = () => {
      data.AIUrl = false;
      setTimeout(() => {
        getList();
      }, 1000);
    };
    const setTitle = (type) => {
      updateTitle({
        cover: data.formData.title,
        conclusion: data.formData.conclusion,
      })
        .then((res) => {})
        .catch((error) => {});
    };
    const AIPost = () => {
      Router.push({
        query: {
          type: 2,
        },
        name: "newAllProject",
      });
    };

    //搜索框
    const seekContent = () => {};
    const seekContent1 = (event) => {
      if (data.name == "") {
        message.error("请输入搜索内容");
        return false;
      }
      data.historyShow = false;
      // 点击搜索关闭webscoket连接
      if (recorderCom.value) {
        console.log("关闭webscoket连接");
        recorderCom.value.closeConnection();
      }
      //data.searchHistory.push(data.name);
      //data.searchHistory = [...new Set(data.searchHistory)];
      //data.searchHistory.reverse();
      //localStorage.setItem("historyHome", JSON.stringify(data.searchHistory));
      Router.push({
        path: "/newProject/newProject",
      });
      localStorage.setItem("seekName", data.name);
      localStorage.setItem("isFromHome", true);
    };
    const handleFocus = (value) => {
      data.historyShow = value;
    };
    // 语音输入
    const handleAudio = (text) => {
      console.log("text", text);
      data.name = text;
    };
    return {
      ...toRefs(data),
      zhCN,
      applyModal,
      setTitle,
      deleteRecord,
      close,
      closeGuide,
      move,
      clear,
      loadShow,
      view,
      save,
      showAI,
      closeAI,
      download,
      closeModal,
      viewFile,
      groupForm,
      submit,
      toApply,
      toList,
      getList,
      scrollUp,
      moduleRefs,
      adjustModuleRefsLength,
      setModuleRef,
      closeApplyModal,
      topContainer,
      formRef1,
      formRef2,
      goChose,
      goGuide,
      isAll,
      getYearMonthDay,
      isApplication,
      goDetail,
      AIPost,
      closeAImodel,
      seekContent,
      container,
      btnContent,
      scrollToElement,
      seekContent1,
      handleFocus,
      handleAudio,
      recorderCom,
    };
  },
});
</script>

<style lang="scss" scoped src="./index.scss"></style>
<style lang="scss" scoped>
.combine {
  width: 1200px;
  margin: 0 auto 40px;
}

// [ant-click-animating-without-extra-node='true']::after {
//   -antd-wave-shadow-color:none !important;
// }
.ant-btn.active {
  box-shadow: none;
}

.guide {
  background: linear-gradient(270deg, #0142fd 0%, #2475f9 100%);
  border-radius: 4px 4px 4px 4px;
  color: #ffffff;
  padding: 9px 24px;
  cursor: pointer;
}

.isDisabled {
  background: rgba(12, 112, 235, 0.02);
  color: #fff !important;
  pointer-events: none !important;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.7);
  /* 半透明遮罩 */
  z-index: 9999;
}

.loading-super {
  position: fixed;
  top: 20vh;
  left: 20vw;
  width: 60vw;
  height: 60vh;
  z-index: 9999;
}

.AI-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.45);
  /* 半透明遮罩 */
  z-index: 9999;

  .AImodel {
    border-radius: 8px;
    width: calc(100vw - 352px);
    /*height: calc(100vh - 120px);*/
    position: relative;
    overflow: hidden;

    .title {
      width: 100%;
      height: 40px;
      line-height: 40px;
      text-align: center;
      font-size: 16px;
      background-color: #ffffff;
    }

    .close {
      position: absolute;
      top: 10px;
      right: 30px;
      cursor: pointer;
    }
  }
}

.manual {
  display: flex;
  flex-flow: column;
  justify-content: space-around;
  align-items: center;
  padding: 10px;
  width: 260px;
  height: 180px;
  background: linear-gradient(135deg, #b1faff 0%, #a4cbff 100%);
  border-radius: 8px;
  opacity: 0.7;

  > img {
    width: 50px;
    height: 45px;
  }

  > div:nth-child(2) {
    font-weight: bold;
    font-size: 18px;
    color: #0176f5;
  }

  > div:last-child {
    font-weight: 400;
    font-size: 14px;
    color: #0176f5;
  }
}
.ai {
  display: flex;
  flex-flow: column;
  justify-content: space-around;
  align-items: center;
  padding: 10px;
  width: 260px;
  height: 180px;
  background: linear-gradient(135deg, #d2b1ff 0%, #a4cbff 100%);
  border-radius: 8px;
  opacity: 0.7;

  > img {
    width: 50px;
    height: 45px;
  }

  > div:nth-child(2) {
    font-weight: bold;
    font-size: 18px;
    color: #6b18ff;
  }

  > div:last-child {
    font-weight: 400;
    font-size: 14px;
    color: #6b18ff;
  }
}

.fontDisabled {
  color: rgb(12, 112, 235, 0.3) !important;
  pointer-events: none !important;
}

.tag {
  padding: 0 8px;
  margin-right: 6px;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.65);
  border: 1px solid rgba(0, 0, 0, 0.25);
  border-radius: 2px;
}

.top {
  //   position: fixed;
  //   top: 180px;
  z-index: 999;
  width: 1200px;
  margin-top: 20px;
  // background-color: rgba(245, 247, 252);
}

.scrollable-content {
  overflow-y: auto;
  /* 使内容部分可以滚动 */
  // max-height: 500px;
  /* 根据需要调整滚动区域的高度 */
}

.btn_box {
  width: 100%;
  display: flex;
  background: #ffffff;
  border-bottom: 1px solid #f8fafd;
  color: rgba(0, 0, 0, 0.45);
  font-size: 16px;

  .btn {
    flex: 1;
    padding: 16px 20px;
    cursor: pointer;
    text-align: center;
  }

  .activeBtn {
    font-weight: 500;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.85);
  }
}

.formRef1 {
  margin-top: 12px;

  :deep(.ant-input) {
    border: 1px solid #00000010;
  }

  :deep(.ant-input):focus {
    border: 1px solid #00000010;
    box-shadow: none;
  }

  :deep(.ant-input):hover {
    border: 1px solid #00000010;
  }
}

:deep(.ant-btn) {
  border: none !important;
}

.formRef2 {
  margin-top: 12px;

  :deep(.ant-input) {
    border: 1px solid #00000010;
  }

  :deep(.ant-input):focus {
    border: 1px solid #00000010;
    box-shadow: none;
  }

  :deep(.ant-input):hover {
    border: 1px solid #00000010;
  }
}

.isNot {
  color: #ff4d4f;
  margin-left: 80px;
}

.moduleImg {
  display: inline-block;
  margin-right: 24px;
}

.imgbox {
  display: flex;
  align-items: center;
}
:deep(.ant-tabs-bar) {
  margin: 0;

  .ant-tabs-tab {
    font-size: 16px;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 500;
  }
}
</style>