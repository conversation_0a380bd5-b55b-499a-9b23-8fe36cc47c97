#ability_list {
    width: 80%;
    margin: 36px auto;
    background: white;
}

.ant-modal {
    .ant-modal-content {}
}

:deep(.ant-modal-body) {
    margin-top: 50px;
}

:deep(.ant-modal-header) {
    background-image: url('../../../../assets/images/ability/alertBgc.png') !important;
}

:deep(.down) {
    display: inline-block;
    font-weight: 400;
    font-size: 14px;
    color: #1E63FF;
    line-height: 16px;
    cursor: pointer;
    margin-right: 6px;
}

:deep(.edit) {
    display: inline-block;
    font-weight: 400;
    font-size: 14px;
    color: #1E63FF;
    line-height: 16px;
    cursor: pointer;
    margin-right: 6px;
}

.banner {
    background-image: url("@/assets/images/ability/adlityDetail/banner.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    height: 440px;
    width: 100%;
    padding-top: 73px;
    padding-bottom: 32px;
    margin-bottom: 20px;

    .top_card {
        // margin: 0px 7%;
        margin-left: 120px;
        margin-right: 120px;
        display: flex;

        .left {
            width: 50%;
            position: relative;
            display: inline-block;

            .left_tit {
                // position: absolute;

                p {
                    font-weight: bold;
                    font-size: 36px;
                    color: #24456A;
                    line-height: 42px;
                    display: inline-block;
                    margin-right: 16px;
                }

                .tag {
                    position: relative;
                    bottom: 7px;
                    // width: 48px;
                    height: 24px;
                    background-color: #ECF2FA;
                    font-weight: 400;
                    font-size: 14px;
                    color: #236CFF;
                    line-height: 22px;
                    border: 1px solid #236CFF;
                    border-radius: 0;
                    text-align: center;
                }

                img {
                    display: inline-block;
                    width: 18px;
                    height: 17px;
                    margin-bottom: 14px;
                    margin-left: 15px;
                }

                .little {
                    width: 12px;
                    height: 12px;
                }

                span {
                    position: relative;
                    bottom: 5px;
                    font-weight: 400;
                    font-size: 14px;
                    color: #0C213A;
                    line-height: 16px;
                    margin-left: 5px;
                }
            }

            .left_middle {
                margin-top: 22px;
                // position: absolute;
                width: 100%;

                .info {
                    font-weight: 400;
                    font-size: 18px;
                    color: #0C213A;
                    line-height: 33px;
                    margin-bottom: 28px;
                    display: -webkit-box;
                    -webkit-line-clamp: 3;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                    text-overflow: ellipsis;

                }

                .info_bottom {
                    height: 32px;
                    font-weight: 300;
                    background: rgba(243, 247, 255, 0.6);
                    border-radius: 26px 26px 26px 26px;
                    line-height: 30px;
                    padding-left: 22px;
                    display: inline-block;
                    padding-right: 11px;

                    div {
                        span {
                            margin-right: 12px;
                            font-weight: 400;
                            font-size: 16px;
                            color: #0C213A;
                            line-height: 22px;
                        }
                    }
                }
            }

            .left_bottom {
                font-weight: 300;
                margin-top: 33px;

                div {
                    display: inline-block;
                    margin-left: 16px;

                    p {
                        display: inline-block;
                        color: #1972FF;

                    }

                    img {
                        display: inline-block;
                        margin-bottom: 3px;
                        margin-right: 4px;
                        width: 16px;
                        height: 16px;
                    }
                }

                .btn {
                    background: linear-gradient(270deg, #5790FF 0%, #1A66FB 100%);
                    border-radius: 2px 2px 2px 2px;
                    font-weight: 400;
                    font-size: 18px;
                    color: #FFFFFF;
                    line-height: 13px;
                }
            }
        }

        .right {
            display: inline-block;
            margin-left: 70px;

            img {
                width: 440px;
                // height: 239px;
            }

        }
    }
}

.top_nav {
    padding-left: 120px;
    height: 60px;
    background-color: #f5f7fc;
    width: 100%;
    margin-top: 8px;
    padding-right: 70px;
    display: flex;
    justify-content: space-between;
    position: fixed;
    top: 50px;
    z-index: 21;
    padding-top: 20px;

    div {
        display: inline-block;
    }

    .left_nav {
        padding-bottom: 8px;

        .title {
            font-weight: 400;
            font-size: 12px;
            color: #84899A;
            line-height: 20px;
            margin-right: 8px;
            cursor: pointer;
        }

        .current {
            font-weight: 400;
            font-size: 12px;
            color: #2E3852;
            line-height: 20px;
        }
    }

    .right_nav {
        margin-right: 55px;
        color: #2E7FFF;
        cursor: pointer;
    }
}

:deep(.ant-anchor-link) {
    height: 2px !important;
    margin-bottom: 7px;

    a {
        font-weight: 400;
        font-size: 18px;
        color: #24456A;
        line-height: 21px;
    }
}

:deep(.currentActive) {
    a {
        font-weight: bold !important;
        color: #236CFF !important;
    }
}

.content {
    background: #f5f7fc;
    padding-top: 127px;
    padding-bottom: 8px;
    box-shadow: inset 0px 8px 32px 0px #eaedf3;
}




.box {
    // width: 1200px;
    margin: 0 auto;
}



.desc {
    // width: 86%;
    margin-left: 120px;
    margin-right: 120px;
    margin-bottom: 154px;

    .tit {
        font-weight: 500;
        font-size: 40px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 46px;
        text-align: center;
        margin-bottom: 51px;
    }

    .key {
        text-align: center;
        margin-bottom: 37px;
    }

    .desc_con {
        list-style: none;
        padding-left: 0;

        li {
            text-indent: 2em;
            font-family: Source Han Sans SC, Source Han Sans SC;
            font-weight: 400;
            font-size: 18px;
            color: rgba(0, 0, 0, 0.65);
            line-height: 40px;
        }


    }

    li:first-child {
        text-align: center;
        margin-bottom: 37px;
    }

    .img {
        margin-top: 80px;
        display: flex;
        justify-content: center;
        position: relative;
        width: 99%;
        height: 455px;

        .bac {
            width: 380px;
            height: 164px;
        }

        .first_bac {
            position: absolute;
            top: 50px;
            left: 54%
        }

        .second_bac {
            position: absolute;
            bottom: 0;
            left: 15%
        }

        .main_img {
            position: absolute;
            left: 25%;
            width: 720px;
            height: 400px;
            border-radius: 15px 15px 15px 15px;
            border: 6px solid #245298;
        }
    }
}

.function {
    display: flex;
    // width: 86%;
    margin-left: 120px;
    margin-right: 120px;
    margin-bottom: 94px;


    .left {
        img {
            width: 541px;
            height: 512px;
        }
    }

    .right {
        width: 60%;
        margin-left: 85px;
        padding-top: 50px;

        .tit {
            font-weight: 500;
            font-size: 32px;
            color: rgba(0, 0, 0, 0.85);
            line-height: 38px;
            text-align: left;
        }

        .line {
            margin-top: 9px;
            width: 83px;
            height: 3px;
            background: #216AFB;
            margin-bottom: 21px;
        }

        :deep(.ant-tabs-nav) {
            height: 40px;

        }

        :deep(.ant-tabs-nav-list::after) {
            display: none;
        }



        :deep(.ant-tabs-ink-bar) {
            display: none !important;
        }

        :deep(.ant-tabs-bar) {
            border-bottom: none;
        }

        :deep(.ant-tabs-tab) {
            border-radius: 2px 2px 2px 2px;
            border: 1px solid #C5D2E4;
            font-weight: 400;
            font-size: 14px;
            color: #24456A;
            line-height: 14px;
            height: 36px;
        }

        :deep(.ant-tabs-tab-active) {
            background: rgba(214, 228, 255, 0.6);
            border-radius: 2px 2px 2px 2px;
            font-weight: 400;
            font-size: 14px;
            color: #216AFB;
            line-height: 14px;
            height: 36px;
            border: none;
        }

    }
}

.ant-tabs-nav-list::after {
    display: none;
}

.advantage {
    display: flex;
    // width: 86%;
    margin-left: 120px;
    margin-right: 120px;
    margin-bottom: 94px;
    background-image: url("@/assets/images/ability/adlityDetail/anli.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;

    .left {
        width: 50%;
        background-image: url("@/assets/images/ability/adlityDetail/scene_left.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        height: 450px;
        padding: 54px 43px 0 48px;
        position: relative;

        .line {
            position: absolute;
            right: 183px;
            top: 85px;
            width: 10px;
            height: 10px;
            border-top: 2px solid #738FB2;
            border-right: 2px solid #738FB2;
            transform: rotate(-135deg);
        }

        .right_line {
            position: absolute;
            right: 2px;
            top: 89px;
            width: 190px;
            height: 2px;
            background-color: #738FB2;
        }

        :deep(.ant-tabs-nav) {
            height: 40px;

        }

        :deep(.ant-tabs-nav-list::after) {
            display: none;
        }



        :deep(.ant-tabs-ink-bar) {
            display: none !important;
        }

        :deep(.ant-tabs-bar) {
            border-bottom: none;
        }

        :deep(.ant-tabs-tab) {
            border-radius: 2px 2px 2px 2px;
            border: 1px solid #C5D2E4;
            font-weight: 400;
            font-size: 14px;
            color: #24456A;
            line-height: 12px;
            height: 36px;
        }

        :deep(.ant-tabs-tab-active) {
            border-radius: 2px 2px 2px 2px;
            font-weight: 500;
            font-size: 14px;
            color: #236CFF;
            line-height: 12px;
            height: 36px;
            background: rgba(215, 230, 255, 0.6);
            border: 1px solid #236CFF;
        }

        .title {
            font-weight: 400;
            font-size: 16px;
            color: #0C213A;
            line-height: 32px;
            border-bottom: 1px dashed #B7C8E3;
            width: 70%;
            margin-bottom: 40px;
            margin-top: 25px;
            padding-bottom: 10px;

            img {
                display: inline-block;
                margin-left: 15%;
                width: 13px;
                height: 13px;
                margin-bottom: 3px;
                margin-right: 6px;
            }

            .tit {
                font-weight: 600;
                font-size: 16px;
                color: #0C213A;
                line-height: 32px;
            }
        }
    }



    .right {
        width: 50%;


        .tit {
            font-weight: 500;
            font-size: 32px;
            color: #0C213A;
            line-height: 32px;
            margin-top: 70px;
            margin-left: 15px;
        }
    }
}

.download {
    background-color: #FFFFFF;
    padding: 52px 120px 50px 120px;
    margin-bottom: 94px;

    .tit {
        font-weight: 500;
        font-size: 32px;
        color: #24456A;
        line-height: 32px;
        padding-left: 45%;
    }

    .list {
        list-style-type: none;
        margin-top: 32px;

        li {
            width: 49%;
            display: inline-block;
            background: linear-gradient(180deg, #EDF0F9 0%, #FEFEFF 100%);
            box-shadow: 0px 4px 24px 0px #EAEDF3;
            border-radius: 10px 10px 10px 10px;
            border: 2px solid #FFFFFF;
            padding: 4px 24px 4px 24px;
            margin-bottom: 24px;

            img {
                display: inline-block;
                width: 72px;
                height: 72px;
            }

            p {
                display: inline-block;
                font-weight: 500;
                font-size: 16px;
                color: #2E3852;
                line-height: 28px;
            }

            .left_box {
                display: inline-block;
                padding-top: 19px;
            }
        }

        .li_box {
            display: flex;
            justify-content: space-between;
        }

        li:nth-of-type(odd) {
            margin-right: 24px;
        }
    }
}

.otherInfo {
    display: flex;
    width: 100%;
    margin-bottom: 94px;
    background-color: #FFFFFF;
    padding-left: 120px;
    margin-right: 120px;
    padding-bottom: 90px;
    padding-top: 98px;

    .center {
        background-image: url("@/assets/images/ability/adlityDetail/bottom_center.png");

        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-right: 40px;

        span {
            margin-right: 15px;
        }
    }

    .card {
        width: 29%;
        padding-top: 11%;
        padding-left: 3%;

        p {
            font-weight: 400;
            font-size: 16px;
            color: #24456A;
            line-height: 28px;
        }

        .tit {
            font-weight: 500;
            font-size: 24px;
            color: #24456A;
            line-height: 32px;
            margin-bottom: 11px;
        }

        .line {
            width: 40px;
            height: 4px;
            background: #2E7FFF;
            border-radius: 2px 2px 2px 2px;
            margin-bottom: 25px;
        }

        span {
            font-weight: 400;
            font-size: 16px;
            color: rgba(36, 69, 106, 0.65);
            line-height: 28px;
            // width: 18%;
            display: inline-block;
            // margin-right: 15px;
        }

        .people {
            .first {
                margin-right: 0px;
            }
        }
    }

    .right {
        background-image: url("@/assets/images/ability/adlityDetail/bottom_right.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;


        .people {
            .first {
                margin-right: 17px;
            }
        }
    }

    .left {
        background-image: url("@/assets/images/ability/adlityDetail/bottom_left.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-right: 40px;

        .btn {
            cursor: pointer;
            border: 2px solid #4E89FE;
            // width: 92px;
            height: 37px;
            display: inline-block;
            font-weight: 400;
            font-size: 16px;
            color: #24456A;
            line-height: 32px;
            padding-left: 6px;
            padding-right: 6px;
        }
    }
}

.anchors {
    display: flex;
    justify-content: center;

    :deep(.ant-anchor-ink::before) {
        display: none;
    }

    :deep(.currentActive) {
        a {
            padding-bottom: 8px;
            border-bottom: 2px solid #236CFF;
        }
    }
}

*::-webkit-scrollbar-vertical {
    width: 101px;
}

.navList {
    list-style: none;
}


:deep(.ant-anchor) {
    // padding-left: 26%;
    padding-bottom: 15px;

    .ant-anchor-link {

        margin-right: 99px;
        padding-bottom: 20px;
    }

    .ant-anchor-ink {
        bottom: 7px;
    }

    .ant-anchor-link-title {
        font-weight: 400;
        font-size: 18px;
        color: #24456A;
        line-height: 21px;
    }
}



:deep(.ant-breadcrumb) {
    li {
        cursor: pointer;

    }
}


:deep(.ant-tabs-nav-list) {
    width: 100%;
}

:deep(.ant-modal-content) {
    height: 100% !important;
}

.fileData {
    .list {
        list-style-type: none;
        margin-top: 32px;

        li {
            width: 49%;
            display: inline-block;
            padding: 4px 24px 4px 24px;
            margin-bottom: 24px;

            img {
                display: inline-block;
                width: 72px;
                height: 72px;
            }

            p {
                display: inline-block;
                font-weight: 500;
                font-size: 16px;
                color: #2E3852;
                line-height: 28px;
            }

            .left_box {
                display: inline-block;
                padding-top: 19px;
            }
        }

        .li_box {
            display: flex;
            justify-content: space-between;
            display: inline-block;
        }

        li:nth-of-type(odd) {
            margin-right: 24px;
        }
    }

    .foot {
        margin-top: 20px;
        text-align: center;

        span {
            width: 77px;
            display: inline-block;
            height: 31px;
            font-size: 16px;
            line-height: 29px;
            color: #FFFFFF;
            margin: 0 16px;
            cursor: pointer;
        }

        .all {
            background-color: #1863FA;

            label {
                color: #FFFFFF;

            }
        }

        .null {
            background-color: gray;
        }

        .down {
            background-color: #FFBF00;
        }
    }

    .content {
        padding-top: 15px;
    }
}

:deep(.ant-tabs-tabpane) {
    // margin-top: 32px;
    height: 220px;
    // display: flex;
}

:deep(.ant-tabs-ink-bar) {
    margin-top: 16px;
    height: 3px !important;
}

:deep(.ant-tabs-nav) {
    height: 80px;
    margin: 0;
}


// :deep(.ant-form-item-control-input) {
//     text-align: center;
// }

:deep(.ant-form-item-label) {
    width: 143px;
    text-align: right;
}

:deep(.remove) {
    display: inline-block;
    font-weight: 400;
    font-size: 14px;
    color: #F5222D;
    line-height: 16px;
    cursor: pointer;
}

.searchInfo {
    background-image: url("@/assets/images/ability/search.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    padding: 40px 0 40px 0;
    margin-top: 80px;
    position: relative;

    .vocationPull {
        background: #FFFFFF;
        margin: 0 12%;
        display: flex;
        align-items: center;
        background: #FFFFFF;
        box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);
        border-radius: 5px;
    }


    .inputClass {
        border: none;
        width: 100%;
        height: 100%;
        box-shadow: none !important;
    }

    .line {
        width: 1px;
        height: 26px;
        background: #EFF0F4;
    }

    .lines {
        width: 1px;
        height: 56px;
        background: #EFF0F4;
    }

    .seekInfo {
        background: linear-gradient(270deg, #0142FD 0%, #2475F9 100%);
        font-weight: 400;
        font-size: 16px;
        color: #FFFFFF;
        display: flex;
        align-items: center;
        width: 160px;
        height: 56px;
        cursor: pointer;
        border-top-right-radius: 5px;
        border-bottom-right-radius: 5px;

        img {
            width: 24px;
            height: 24px;
            margin-left: 20px;
            margin-right: 8px;
        }
    }

    .label {
        margin-left: 12%;
        margin-top: 16px;
    }

    .top_tag {
        border: none;
        color: #236CFF;
        border-radius: 2px;
        background: #E8EDF7;
        font-weight: 400;
        font-size: 12px;
        color: #24456A;
    }

    .stage {
        margin-left: 44px;
        margin-top: 16px;
    }
}

.tabContent {
    margin: 24px 0 40px 0;
    background: #fff;
    display: flex;
    align-items: flex-start;

    :deep(.ant-tree-switcher) {
        top: 2px;
    }

    .tabModel {
        width: 260px;
        margin-top: -24px;
        // height: 850px;
        margin-right: 16px;
        overflow: hidden auto;
        border-left: 1px solid #d9d9d9;
        position: relative;

        .parentType {
            font-weight: 600;
            font-size: 16px;
            color: #24456A;
        }

        .divider {
            position: absolute;
            width: 6px;
            height: 180px;
            background-color: #236CFF;
            top: 0;
            left: -1px;
            margin: 0
        }

        img {
            margin-left: 55px;
        }

        .ant-tree .ant-tree-list {
            font-weight: 400;
            font-size: 16px;
            color: #24456A;
            background-color: blue;
        }
    }

    .tabModel::-webkit-scrollbar {
        display: none;
    }

    .cardContent {
        // height: 780px;
        overflow: hidden auto;
        display: flex;
        flex-wrap: wrap;
        padding-left: 24px;
    }

    .cardContent::-webkit-scrollbar {
        display: none;
    }

    .tab_text {
        width: 190px;
        height: 45px;
        line-height: 45px;
        font-weight: 400;
        font-size: 16px;
        color: #24456A;
        position: relative;
        cursor: pointer;
    }


    .topTab {
        font-size: 16px;
        font-weight: bold;
    }

    :deep(.ant-tag) {
        font-size: 12px;
        color: #2E7FFF;
    }

    .emptyPhoto {
        margin: auto;

        img {
            width: 240px;
            height: 248px;
        }
    }

    .card_content {
        position: relative;
        min-height: 330px;
        width: 30%;
        margin-right: 3%;
        background-color: white;
        box-shadow: 0px 2px 8px 0px rgba(64, 103, 131, 0.13);
        cursor: pointer;
        margin-bottom: 32px;
        border-radius: 4px;

        :deep(.ant-image-img) {
            width: 100%;
            height: 180px;   
        }
    }

    .ant-checkbox-wrapper {
        position: absolute;
        left: 10px;
        bottom: 10px;
    }

    .action {
        font-size: 14px;
        padding: 16px;

        .delete {
            margin-right: 8px;
        }
    }



    .card_content:nth-of-type(3n) {
        margin-right: 0;
    }

    .rightActive {
        border-right: none;
    }

    .cardActive {
        background-color: #FFFFFF;
        transition: all 0.2s;
        box-shadow: 0 0 10px #DAE2F5;
    }



    .card_center {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .card_text {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 0;
        border-bottom: 1px solid #EAECED;

        .card_tag {
            margin-left: 12px;
            display: flex;
            align-items: center;

            .a-tag {
                background-color: #D7E6FF;
                color: #236CFF
            }
        }

        .common {
            border: 1px solid #236CFF;
            color: #236CFF;
        }

        .industry {
            border: 1px solid #FA8C16;
            color: #FA8C16;
            ;
        }

        .card_title {
            font-weight: 500;
            font-size: 18px;
            color: #24456A;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
        }

        :deep(.cityStyle) {
            font-size: 12px;
            text-align: center;
            width: 48px;
            height: 20px;
            line-height: 18px;
            font-weight: 400;
            border-radius: 100px 100px 100px 100px;
            background-color: #fff;
        }
    }

    .card_des {
        margin: 12px 12px;
        font-family: Source Han Sans SC, Source Han Sans SC;
        min-height: 38px;
        font-weight: 300;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.45);
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        /* 控制显示的行数 */
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
    }

    .footer {
        display: flex;
        justify-content: space-between;
        padding: 0 8px 12px 12px;

        img {
            width: 15px;
            height: 14px;
        }

        .browse {
            display: inline-block;
            margin-right: 8px;
            font-weight: 400;
            font-size: 12px;
            color: #24456A;
        }
    }

    .layPage {
        width: 100%;
        text-align: center;
        margin: 15px 0;
    }

    .loading {
        line-height: 780px;
        margin: 0 auto;

    }
}