<template>
  <div class="loading-overlay" v-if="data.loadShow">
    <a-spin :spinning="data.loadShow"></a-spin>
    <div style="margin-left: 10px">{{ data.loadShowTitle }}</div>
  </div>
  <div class="print">
    <!-- <div class="btns" @click="shareLink">分享</div> -->
    <div class="btns" @click="downloadPdf">下载</div>
    <img src="@/assets/images/buyList/printIcon.png" v-print="printObj" />
  </div>
  <div style="width: 100%; height: 47px"></div>
  <div id="canvas-contaniner" style="flex: 1; overflow: auto">
    <div ref="canvasRef" class="buyListTable" id="buyList-table">
      <div
        class="con"
        v-for="(item, index) in data.buyDetail.newProductPackageLists"
        :key="index"
      >
        <div>
          <div class="top flex">
            <img
              class="left_img"
              src="../../assets/images/newProject/loginLogo.png"
              alt=""
            />
            <img
              class="right_img"
              src="../../assets/images/buyList/china.png"
              alt=""
            />
          </div>
          <div class="title">标准产品封装包订购单</div>
          <div class="boot">
            <div class="box">
              <span class="left_tit">客户经理</span>
              <span
                class="left_con"
                v-if="data.buyDetail.customerManager != ''"
                >{{ data.buyDetail.customerManager }}</span
              >
              <span class="left_con" v-else>-</span>
            </div>
            <div class="box">
              <span class="left_tit">联系电话</span>
              <span
                class="left_con"
                v-if="data.buyDetail.managerNumber != ''"
                >{{ data.buyDetail.managerNumber }}</span
              >
              <span class="left_con" v-else>-</span>
            </div>
            <div class="box">
              <span class="left_tit">生成日期</span>
              <span class="left_con" v-if="data.buyDetail.contractDate != ''">{{
                data.buyDetail.contractDate
              }}</span>
              <span class="left_con" v-else>-</span>
            </div>
          </div>
          <!-- v-if="item.findIndex(item=>item.demandId != '-1') != -1" -->
          <div class="list">
            <div class="list_tit">
              {{
                route.query?.sourceType == 1 ? "需求方案产品" : "产品分类列表"
              }}
            </div>
            <div class="box">
              <div
                style="width: 100%"
                v-for="(citem, cindex) in item"
                :key="cindex"
              >
                <!-- v-if="citem.demandId != -1" -->
                <div class="demandNameBox">
                  <div class="demandName">
                    {{
                      citem.demandName == "通用产品"
                        ? "补充产品"
                        : citem.demandName
                    }}
                  </div>
                </div>
                <!-- v-if="citem.demandId != -1" -->
                <a-table
                  :dataSource="citem.productPackageLists"
                  :columns="data.columns"
                  bordered
                  :pagination="{ defaultPageSize: 100 }"
                >
                  <template #productName="{ record, text }">
                    <span class="name">{{ text }}</span>
                  </template>
                  <template #productDesc="{ record, text }">
                    <span class="desc">{{ text }}</span>
                  </template>
                  <template #productType_specification="{ record, text }">
                    <span class="name">{{ dealData(text, "1") }}</span>
                  </template>
                  <template #price="{ record, text }">
                    <span class="name">{{ dealData(text, "2") }}</span>
                  </template>
                </a-table>
              </div>
            </div>
          </div>
          <div
            class="list"
            v-if="
              false && item.findIndex((item) => item.demandId == '-1') != -1
            "
          >
            <div class="list_tit">{{ "补充产品" }}</div>
            <div class="box">
              <div style="height: 20px"></div>
              <div
                style="width: 100%"
                v-for="(citem, cindex) in item"
                :key="cindex"
              >
                <a-table
                  v-if="citem.demandId == -1"
                  :dataSource="citem.productPackageLists"
                  :columns="data.columns"
                  bordered
                  :pagination="{ defaultPageSize: 100 }"
                >
                  <template #productName="{ record, text }">
                    <span class="name">{{ text }}</span>
                  </template>
                  <template #productDesc="{ record, text }">
                    <span class="desc">{{ text }}</span>
                  </template>
                  <!-- <template #productType_specification="{ record, text }">
                    <span class="name">{{ dealData(text, "1") }}</span>
                  </template>
                  <template #price="{ record, text }">
                    <span class="name">{{ dealData(text, "2") }}</span>
                  </template> -->
                </a-table>
              </div>
            </div>
          </div>
        </div>
        <div class="page">
          <div class="pageSize">{{ index + 1 + " / " + data.pageSize }}</div>
        </div>
      </div>
    </div>
  </div>
  <a-modal
    v-model:visible="data.shareVisable"
    title="分享链接"
    :mask-closable="false"
    :footer="null"
    :destroyOnClose="true"
    width="500px"
  >
    <div class="shareBody">
      <div class="shareContent">
        <LinkOutlined style="font-size: 26px" />
        <div class="shareContent-link">
          <div :title="`【麒麟平台】${data.buyDetail.title}方案`">
            【麒麟平台】{{ data.buyDetail.title }}方案
          </div>
          <div :title="data.link">{{ data.link }}</div>
        </div>
      </div>
      <div class="copyLink">
        <div @click="copy">复制链接</div>
      </div>
    </div>
  </a-modal>
</template>
<!-- <script>
import { defineComponent, reactive, toRefs, ref, onMounted } from "vue";
import { useHomeStore } from "@/store";
export default defineComponent({
  setup() {
    // 打印功能配置项
    let printObj = reactive({
      id: "qilin-table",
      popTitle: "数据统计表",
    });
    const homeStore = useHomeStore();
    const data = reactive({
      buyDetail: homeStore.aiBuyListStroe,
      columns: [
        {
          title: "产品名称",
          dataIndex: "productName",
          key: "productName",
          align: "center",
          slots: { customRender: "productName" },
          width: "15%",
        },
        {
          title: "产品介绍",
          dataIndex: "productDesc",
          key: "productDesc",
          align: "left",
          ellipsis: false,
          slots: { customRender: "productDesc" },
        },
        // {
        //   title: "产品规格",
        //   dataIndex: "productType_specification",
        //   key: "productType_specification",
        //   align: "left",
        //   ellipsis: false,
        //   slots: { customRender: "productType_specification" },
        //   width: "20%",
        // },
        // {
        //   title: "标准价格",
        //   dataIndex: "price",
        //   key: "price",
        //   align: "center",
        //   ellipsis: false,
        //   slots: { customRender: "price" },
        //   width: "20%",
        // },
        {
          title: "产品数量",
          dataIndex: "productQuantity",
          key: "productQuantity",
          align: "center",
          ellipsis: true,
          slots: { customRender: "productQuantity" },
          width: "10%",
        },
      ],
      pageSize: "",
    });
    window.addEventListener("beforeunload", function (e) {
      // 在这里执行你的代码
      homeStore.aiBuyListStroe = {};
    });
    console.log(data.buyDetail);

    const dealData = (e, type) => {
      if (e === undefined || e === null || e === "" || e === "-" || e >= 8888) {
        if (type == 1) return "以具体业务规格为准";
        if (type == 2) return "以具体业务定价为准";
        if (type == 3) return "-";
      } else {
        return e;
      }
    };
    const splitArr = (arr) => {
      const total = arr.length;
      const partSize = Math.ceil(total / 8);
      const result = [];
      for (let i = 0; i < partSize; i++) {
        result.push(arr.slice(i * 8, (i + 1) * 8));
      }
      return result.slice(0, 8);
    };
    onMounted(() => {
      console.log("data.buyDetail", data.buyDetail);

      if (data.buyDetail?.productPackageLists) {
        let arr = data.buyDetail.productPackageLists;
        arr.forEach((item, index) => {
          item.pIndex = index + 1;
        });
        let num = Math.ceil(arr.length / 8);
        data.pageSize = num;
        data.buyDetail.newProductPackageLists = splitArr(arr);
      }
    });
    return {
      ...toRefs(data),
      printObj,
      homeStore,
      dealData,
      splitArr,
    };
  },
});
</script> -->

<script setup>
import { LinkOutlined } from "@ant-design/icons-vue";
import {
  defineComponent,
  reactive,
  toRefs,
  ref,
  onMounted,
  computed,
} from "vue";
import { useHomeStore } from "@/store";
import { generatePDF } from "@/utils/htmlPdf.js";
import { uploadFileList } from "@/api/fileUpload/uploadFile.js";
import { message } from "ant-design-vue";
import clipboard3 from "vue-clipboard3";
import { useRoute } from "vue-router";
import html2canvas from "html2canvas";

const canvasRef = ref(null);
// 打印功能配置项
let printObj = reactive({
  id: "buyList-table",
  popTitle: "",
  extraCss: `
    @media print {
      @page { margin: 0;}
      html { padding: 0; }  /* 内容安全区域 */
    }
  `,
});
const route = useRoute();
const homeStore = useHomeStore();
const data = reactive({
  buyDetail: homeStore.aiBuyListStroe,
  columns: [
    {
      title: "产品名称",
      dataIndex: "productName",
      key: "productName",
      align: "center",
      slots: { customRender: "productName" },
      width: "20%",
    },
    {
      title: "产品介绍",
      dataIndex: "productDesc",
      key: "productDesc",
      align: "left",
      ellipsis: false,
      slots: { customRender: "productDesc" },
    },
    // {
    //   title: "产品规格",
    //   dataIndex: "productType_specification",
    //   key: "productType_specification",
    //   align: "left",
    //   ellipsis: false,
    //   slots: { customRender: "productType_specification" },
    //   width: "20%",
    // },
    // {
    //   title: "标准价格",
    //   dataIndex: "price",
    //   key: "price",
    //   align: "center",
    //   ellipsis: false,
    //   slots: { customRender: "price" },
    //   width: "20%",
    // },
    // {
    //   title: "产品数量",
    //   dataIndex: "productQuantity",
    //   key: "productQuantity",
    //   align: "center",
    //   ellipsis: true,
    //   slots: { customRender: "productQuantity" },
    //   width: "16%",
    // },
  ],
  pageSize: "",
  shareVisable: false,
  link: "",
  loadShow: false,
  loadShowTitle: "",
});
window.addEventListener("beforeunload", function (e) {
  // 在这里执行你的代码
  homeStore.aiBuyListStroe = {};
});
console.log(data.buyDetail);

const copy = async () => {
  const { toClipboard } = clipboard3();
  await toClipboard(data.link);
  message.success("复制成功");
};

const downloadPdf = async () => {
  data.loadShow = true;
  data.loadShowTitle = "下载中";
  const pdfBlob = await generatePDF("buyList-table");
  const link = document.createElement("a");
  link.href = URL.createObjectURL(pdfBlob.blobData);
  link.download = `${route.query.title}.pdf`;
  link.click(); // 触发下载
  data.loadShow = false;
};

const shareLink = async () => {
  // const element = document.getElementById('buyList-table');
  const element = document.getElementById("canvas-contaniner");
  let height = 0;
  let windowHeight = window.innerHeight - 47;
  let canvasList = [];
  let height1 = 0
  window.scrollTo(0,0)
  function startCanvas() {
    if (height < element.scrollHeight) {
      console.log('windowHeight',windowHeight);
      console.log('scrollHeight',element.scrollHeight);
      console.log('height',height);
      element.scrollTo(0, height);
      setTimeout(() => {
        html2canvas(element, {
          scale: 2, // 提升清晰度
          useCORS: true, // 解决跨域图片问题
          backgroundColor: "#FFFFFF", // 强制白底
          width: 794,
          y: height1 <= windowHeight && height1 > 0 ? windowHeight - height1 : 0,
          height: height1 <= windowHeight && height1 > 0 ? height1 : windowHeight,
          // scrollY: element.scrollHeight,
          // windowHeight: element.scrollHeight,
          windowWidth: 794,
        }).then((canvas) => {
          const imgUrl = canvas.toDataURL("image/png");
          // console.log("imgUrl", imgUrl);
          canvasList.push(canvas);
          height = height + windowHeight;
          if(element.scrollHeight - height <= windowHeight ){
            height1 = element.scrollHeight - height
          }else{
            height1 = height
          }
          startCanvas();
        });
      }, 1000);
    } else {
      let totalWidth = Math.max(...canvasList.map((c) => c.width)); // 获取最宽的canvas宽度
      let totalHeight = canvasList.reduce(
        (acc, canvas) => acc + canvas.height,
        0
      ); // 计算总高度
      let combinedCanvas = document.createElement("canvas");
      combinedCanvas.width = totalWidth;
      combinedCanvas.height = totalHeight;
      let ctx = combinedCanvas.getContext("2d");
      let y = 0;      
      canvasList.forEach((canvas) => {
        ctx.drawImage(canvas, 0, y); // 在新canvas上绘制每个小canvas
        y = y + canvas.height; // 更新y坐标以绘制下一个canvas
      });
      // const imgUrl1 = combinedCanvas.toDataURL("image/png");
      // console.log("imgUrl1", imgUrl1);
      combinedCanvas.toBlob(blob => {
        // blob 图片 Blob 对象
        // console.log(blob,'=========================='); // 处理或上传 blob
        let formData = new FormData();
        formData.append("file", blob, `${route.query.title}.png`);
        formData.append("name", `${route.query.title}.png`);
        // return
        uploadFileList(formData)
          .then((res) => {
            // console.log("res", res);
            if (res.code == 200) {
              // let url = res.data.fileUrl.split("/portal");
              // let windowOrigin = window.location.origin;
              // let token = localStorage.getItem("token");
              // data.link = `${windowOrigin}/#/sharePdf?urlMsg=${
              //   windowOrigin + url[1] + "?token=" + token
              // }&urlName=${data.buyDetail.title}`;
              data.link = res.data.fileUrl
              data.loadShow = false;
              data.shareVisable = true;
            } else {
              data.loadShow = false;
            }
          })
          .catch((err) => {});
      }, 'image/png'); // 指定图片格式，例如 'image/png' 或 'image/jpeg'
      return
      const link = document.createElement('a');
      link.download = '====';
      link.href = combinedCanvas.toDataURL('image/png');
      link.click();
    }
  }
  startCanvas()
  return;

  return;
  data.loadShow = true;
  data.loadShowTitle = "链接生成中";
  const pdfBlob = await generatePDF("buyList-table");
  console.log("pdfBlob", pdfBlob);
  pdfBlob.blobData.name = "产品订购单.pdf";
  console.log("imgUrl", pdfBlob.blobData.imgUrl);
  data.loadShow = false;
  return;
  let formData = new FormData();
  formData.append("file", pdfBlob.blobData, "产品订购单.pdf");
  formData.append("name", "产品订购单.pdf");
  // return
  await uploadFileList(formData)
    .then((res) => {
      console.log("res", res);
      if (res.code == 200) {
        let url = res.data.fileUrl.split("/portal");
        let windowOrigin = window.location.origin;
        let token = localStorage.getItem("token");
        data.link = `${windowOrigin}/#/sharePdf?urlMsg=${
          windowOrigin + url[1] + "?token=" + token
        }&urlName=${data.buyDetail.title}`;
        // data.link = `${windowOrigin + url[1]}`
        data.loadShow = false;
        data.shareVisable = true;
      } else {
        data.loadShow = false;
      }
    })
    .catch((err) => {});
};

const dealData = (e, type) => {
  if (e === undefined || e === null || e === "" || e === "-" || e >= 8888) {
    if (type == 1) return "以具体业务规格为准";
    if (type == 2) return "以具体业务定价为准";
    if (type == 3) return "-";
  } else {
    return e;
  }
};
const splitArr = (arr) => {
  // console.log('arr',arr);
  const total = arr.length;
  const partSize = Math.ceil(total / 6);
  let currentIndex = 0;
  const result = [];
  while (currentIndex < total) {
    let sliceList = arr.slice(currentIndex, currentIndex + 8);
    const hasA = sliceList.some((item) => item.demandName == "补充产品");
    const hasB = sliceList.some((item) => item.demandName != "补充产品");
    console.log("sliceList", sliceList);
    let sameClass = [];
    let newList = sliceList.filter((item) => {
      return item.demandName;
    });
    newList.forEach((item) => {
      if (!sameClass.includes(item.demandName)) {
        sameClass.push(item.demandName);
      }
    });
    console.log("sameClass", sameClass);

    let count = 0;
    sliceList.forEach((item) => {
      if (item.productDesc.length > 150) {
        count = count + 150;
      } else {
        count = count + item.productDesc.length;
      }
    });
    console.log("count", count);
    if (sameClass.length == 1) {
      result.push(sliceList.slice(0, 8));
      currentIndex += 8;
    } else if (sameClass.length == 2 && count >= 890) {
      result.push(sliceList.slice(0, 7));
      currentIndex += 7;
    } else if (sameClass.length == 2 && count < 890) {
      result.push(sliceList.slice(0, 8));
      currentIndex += 8;
    } else if (sameClass.length == 3 && count >= 600) {
      result.push(sliceList.slice(0, 6));
      currentIndex += 6;
    } else if (sameClass.length == 3 && count < 600) {
      result.push(sliceList.slice(0, 7));
      currentIndex += 7;
    } else if (sameClass.length >= 4 && count < 400) {
      result.push(sliceList.slice(0, 6));
      currentIndex += 6;
    } else if (sameClass.length >= 4 && count >= 500) {
      result.push(sliceList.slice(0, 4));
      currentIndex += 4;
    } else {
      result.push(sliceList);
      currentIndex += 8;
    }
    // if(hasA && hasB && sameClass.length == 1){
    //   result.push(sliceList.slice(0,7))
    //   currentIndex +=7
    // }else if(hasA && hasB && sameClass.length > 2 && count > 610){
    //   result.push(sliceList.slice(0,6))
    //   currentIndex +=6
    // }else if(hasA && hasB && sameClass.length > 2 && count <= 610){
    //   result.push(sliceList.slice(0,7))
    //   currentIndex +=7
    // }else if(hasA && !hasB){
    //   result.push(sliceList.slice(0,8))
    //   currentIndex +=8
    // }else if(!hasA && hasB && sameClass.length == 1){
    //   result.push(sliceList.slice(0,8))
    //   currentIndex +=8
    // }else if(!hasA && hasB && sameClass.length == 2 && count >= 890){
    //   result.push(sliceList.slice(0,7))
    //   currentIndex +=7
    // }else if(!hasA && hasB && sameClass.length == 2 && count < 890){
    //   result.push(sliceList.slice(0,8))
    //   currentIndex +=8
    // }else if(!hasA && hasB && sameClass.length == 3 && count >= 890){
    //   result.push(sliceList.slice(0,6))
    //   currentIndex +=6
    // }else if(!hasA && hasB && sameClass.length == 3 && count < 890){
    //   result.push(sliceList.slice(0,7))
    //   currentIndex +=7
    // }else if(!hasA && hasB && sameClass.length >= 4){
    //   result.push(sliceList.slice(0,6))
    //   currentIndex += 6
    // }else{
    //   result.push(sliceList)
    //   currentIndex +=8
    // }
  }
  // console.log('res',result);

  return result;
  // for (let i = 0; i < partSize; i++) {
  //   result.push(arr.slice(i * 6, (i + 1) * 6));
  // }
  // return result.slice(0, 6);
};
const groupToArray = (arr) => {
  const map = {};
  arr.forEach((item) => {
    if (!map[item.demandId]) map[item.demandId] = [];
    map[item.demandId].push(item);
    map[item.demandId]["demandName"] = item.demandName;
  });
  // console.log('map',Object.keys(map));
  if (route.query.sourceType == 1) {
    return Object.keys(map).map((id) => ({
      demandId: id,
      demandName: map[id]["demandName"],
      productPackageLists: map[id],
    }));
  } else {
    let num = Object.keys(map).sort((a, b) => {
      return b - a;
    });
    return num.map((id) => ({
      demandId: id,
      demandName: map[id]["demandName"],
      productPackageLists: map[id],
    }));
  }
};
const containTypes = (val) => {
  const hasA = val.some((item) => item.demandName == "补充产品");
  const hasB = val.some((item) => item.demandName != "补充产品");
  return hasA && hasB;
};
onMounted(() => {
  console.log("data.buyDetail1", data.buyDetail);
  if (route.query.sourceType == 1 && data.buyDetail?.productPackageLists) {
    let groupArr = groupToArray(data.buyDetail.productPackageLists);
    let arr = [];
    groupArr.forEach((item) => {
      item.productPackageLists.forEach((pitem) => {
        arr.push(pitem);
      });
    });
    arr.forEach((item, index) => {
      item.pIndex = index + 1;
    });
    let num = Math.ceil(arr.length / 6);
    data.pageSize = num;
    let newList = splitArr(arr);
    data.buyDetail.newProductPackageLists = [];
    newList.forEach((item) => {
      data.buyDetail.newProductPackageLists.push(groupToArray(item));
    });
    data.pageSize = data.buyDetail.newProductPackageLists.length;
    // console.log('data.buyDetail.newProductPackageLists',data.buyDetail.newProductPackageLists);
  }
  if (route.query.sourceType == 2 && data.buyDetail?.productClassifyLists) {
    let classifyList = [];
    data.buyDetail.productClassifyLists.forEach((item) => {
      console.log("item", item);

      // if(item.productLists){
      //   item.productLists.forEach(pitem=>{
      //     pitem.demandName = item.classifyName
      //     pitem.classifyId = item.classifyId
      //     pitem.demandId = item.classifyId
      //     classifyList.push(pitem)
      //   })
      // }
      item.demandId = item.productClassify * 1;
      classifyList.push(item);
    });
    // console.log('classifyList',classifyList);
    let groupArr = groupToArray(classifyList);
    // console.log('groupArr',groupArr);
    groupArr.sort((a, b) => {
      return b.productClassify - a.productClassify;
    });
    let arr = [];
    groupArr.forEach((item) => {
      item.productPackageLists.forEach((pitem) => {
        arr.push(pitem);
      });
    });
    arr.forEach((item, index) => {
      item.pIndex = index + 1;
    });
    let num = Math.ceil(arr.length / 6);
    data.pageSize = num;
    let newList = splitArr(arr);
    // console.log('newList===============',newList);

    data.buyDetail.newProductPackageLists = [];
    newList.forEach((item) => {
      data.buyDetail.newProductPackageLists.push(groupToArray(item));
    });
    data.pageSize = data.buyDetail.newProductPackageLists.length;
    console.log(
      "data.buyDetail.newProductPackageLists",
      data.buyDetail.newProductPackageLists
    );
  }
});
</script>

<style lang="scss" scoped>
@import "./index.scss";
.buyListTable {
  width: 100%;
  height: 100%;
  // overflow: auto;
}
.print {
  position: fixed;
  width: 100%;
  height: 47px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  background-color: #fff;
  z-index: 999;
  > img {
    width: 20px;
    height: 20px;
    margin-right: 20px;
    cursor: pointer;
  }
}
:deep(.ant-table-pagination.ant-pagination) {
  display: none !important;
}
:deep(.ant-table-thead > tr > th) {
  text-align: center !important;
}
.page {
  display: flex;
  justify-content: center;
  padding-bottom: 11px;

  > .pageSize {
    position: relative;
    font-size: 12px;
    padding: 1px 6px;
    color: #fff;
    border-radius: 4px;
    background-color: rgba(9, 124, 238, 1);
  }
}
.shareBody {
  display: flex;
  justify-content: space-around;
  align-items: center;
  .shareContent {
    flex: 1;
    display: flex;
    align-items: center;
    > img {
      width: 32px;
      height: 32px;
    }
    > .shareContent-link {
      min-width: 160px;
      max-width: 320px;
      > div {
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      > div:last-child {
        text-indent: 0.6em;
      }
    }
  }
  .copyLink {
    width: 74px;
    padding: 4px 9px;
    border-radius: 8px;
    background-color: #96cfed;
  }
}
// @media print {
//   @page {
//     margin: 0 !important;
//     padding: 0 !important;
//   }
//   body{
//     padding: 0 !important;
//     -webkit-print-color-adjust: exact; /* Chrome/Safari/Edge */
//     print-color-adjust: exact; /* 标准属性 */
//     background-color: #fff;
//   }
//   #buyList-table {
//     width: 100% !important;
//     margin: 0 !important;
//     padding: 0 !important;
//     height: auto !important;
//   }

//   html {
//     -webkit-print-color-adjust: exact; /* Chrome/Safari/Edge */
//     print-color-adjust: exact; /* 标准属性 */
//     background-color: #fff;
//   }
//   .con {
//     background: linear-gradient(
//       135deg,
//       #abc2ff 0%,
//       #ace8ff 51%,
//       #5779ff 100%
//     ) !important;
//   }
//   .list-tit {
//     background: linear-gradient(90deg, #02a2f7 0%, #0c70eb 100%) !important;
//   }
//   .cell-topp {
//     background: linear-gradient(
//       90deg,
//       #ffffff 39%,
//       rgba(255, 255, 255, 0) 100%
//     ) !important;
//   }
//   .ant-table-thead > tr > th {
//     background: linear-gradient(180deg, #02a2f7 0%, #0c70eb 100%) !important;
//   }
// }
</style>

<style lang="scss">
body {
  width: 100% !important;
  overflow: auto;
}
#app {
  // height: auto;
  display: flex;
  flex-flow: column;
  overflow: auto;
  > div:first-child {
    flex: 1;
    display: flex;
    flex-flow: column;
    overflow: auto;
  }
}
.btns {
  margin-right: 10px;
}
@media print {
  @page {
    margin: 0 !important;
    padding: 0 !important;
    size: auto;
    scale: 0.98;
  }
  body {
    padding: 0 !important;
    background-color: #fff;
  }
  #buyList-table {
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    height: auto !important;
  }

  html {
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
    background-color: #fff;
  }
  .con {
    background: linear-gradient(
      135deg,
      #abc2ff 0%,
      #ace8ff 51%,
      #5779ff 100%
    ) !important;
  }
  .list-tit {
    background: linear-gradient(90deg, #02a2f7 0%, #0c70eb 100%) !important;
  }
  .cell-topp {
    background: linear-gradient(
      90deg,
      #ffffff 39%,
      rgba(255, 255, 255, 0) 100%
    ) !important;
  }
  .ant-table-thead > tr > th {
    background: linear-gradient(180deg, #02a2f7 0%, #0c70eb 100%) !important;
  }
}
</style>
