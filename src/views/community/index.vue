<template>
  <div class="contentCommunity">
    <div class="top">
      <p class="top_title">麒麟社区</p>
      <p class="top_desc" style="margin-bottom: 64px">
        麒麟社区具备信息发布与共享、话题讨论与互动、用户社交与群组形成、知识积累与传播以及社区管理与规范等功能，为用户提供多元化的交流服务和空间。
      </p>
      <div class="searchBar">
        <div style="display: flex">
          <div
            class="spanBox"
            :class="{ activeBtn: selectIndex === item.index }"
            v-for="(item, index) in selectList"
            :key="index"
            @click="selectBtn(item)"
          >
            {{ item.name }}
          </div>
        </div>
        <div class="seekInput">
          <a-input
            v-model:value="inputValue"
            class="inputClass"
            allow-clear
            @keyup.enter="seekContent"
            placeholder="请输入关键词进行搜索"
          />
          <div class="seekInfo" @click="seekContent">
            <img src="@/assets/images/home/<USER>" />
            <div>搜索</div>
          </div>
          <a-button
            @click="resect"
            style="
              background: linear-gradient(270deg, #0142fd 0%, #2475f9 100%);
              border-radius: 6px 6px 6px 6px;
              font-weight: 500;
              font-size: 16px;
              color: #ffffff;
              padding: 24px;
              align-items: center;
              cursor: pointer;
              line-height: 1px;
              margin-left: 17px;
            "
            >重置</a-button
          >
        </div>
      </div>
    </div>
    <div class="flex" style="justify-content: space-between">
      <div class="left" v-if="totalItemCount != 0">
        <div class="cardContent">
          <div class="card_total flex-1">
            <template v-for="(item, index) in dataList" :key="index">
              <div
                class="card_content"
                @click="proDetail(item.id)"
                style="cursor: pointer; width: 100%"
              >
                <div style="display: flex; margin: 24px">
                  <div class="card_center" style="width: 800px">
                    <div class="card_text">
                      <div style="display: flex; align-items: center">
                        <div class="card_tag">
                          {{ dealType(item.type) }}
                        </div>
                        <div class="title">
                          {{ item.title }}
                        </div>
                        <span v-if="item.top == '1'" class="toTop">置顶</span>
                      </div>
                      <span v-if="item.status == '-1'" class="Rejected"
                        >已驳回</span
                      >
                      <span v-if="item.status == '1'" class="Published"
                        >已发布</span
                      >
                      <span v-if="item.status == '2'" class="Assigned"
                        >已分配</span
                      >
                      <span v-if="item.status == '3'" class="processed"
                        >已处理</span
                      >
                      <span v-if="item.status == '4'" class="Completed"
                        >已完成</span
                      >
                    </div>
                    <div class="card_desc">
                      {{ item.content }}
                    </div>
                    <div class="flex" style="align-items: center">
                      <span style="margin-right: 8px">{{
                        dealCreat(item)
                      }}</span>
                      <span
                        style="margin-right: 8px"
                        v-if="item.anonymity == 0"
                        >{{ item.orgPathName }}</span
                      >
                      <span
                        style="
                          font-weight: 400;
                          font-size: 12px;
                          color: rgba(0, 0, 0, 0.45);
                          margin-right: 48px;
                        "
                        >{{ item.createTime }}</span
                      >
                      <div style="display: flex; align-items: center">
                        <img
                          src="@/assets/images/home/<USER>"
                          style="width: 16px; height: 16px"
                        />
                        <span
                          style="
                            font-size: 12px;
                            color: rgba(0, 0, 0, 0.45);
                            margin-right: 14px;
                          "
                          v-if="item.viewCount"
                          >{{ item.viewCount }}</span
                        >
                        <span v-else style="margin-right: 14px">-</span>
                        <img
                          src="@/assets/images/commiunty/word.png"
                          style="width: 16px; height: 16px"
                        />
                        <span
                          style="font-size: 12px; color: rgba(0, 0, 0, 0.45)"
                          v-if="item.commentCount"
                          >{{ item.commentCount }}</span
                        >
                        <span v-else>-</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>
        <div class="layPage" v-if="totalItemCount != 0">
          <a-pagination
            v-model:pageSize="pageItemSize"
            v-model:current="currentPage"
            :pageSizeOptions="pageSizeOptions"
            show-quick-jumper
            show-size-changer
            :total="totalItemCount"
            @change="pageChange"
            @showSizeChange="sizeChange"
            class="mypage"
          />
        </div>
      </div>
      <div v-if="totalItemCount == 0" class="emptyPhotos">
        <img class="imgs" src="@/assets/images/home/<USER>" />
      </div>
      <div class="right">
        <div
          class="action"
          v-if="
            !(
              userInfo.roleKeyList.includes('auditManager') &&
              userInfo.roleKeyList.length === 1
            )
          "
        >
          <div class="btn flex" @click="pushNew">
            <img
              src="@/assets/images/commiunty/add.png"
              style="width: 18px; height: 18px; margin-right: 8px"
              alt=""
            />
            <span>发布新帖</span>
          </div>
        </div>
        <div class="right_top" style="width: 258px">
          <div class="top flex">
            <div class="line"></div>
            <span>论坛数据</span>
          </div>
          <div class="li_box">
            <div class="li_con flex">
              <div>总帖子数</div>
              <div>
                <span class="num">{{ totalCount }}</span
                >条
              </div>
            </div>
            <div class="li_con flex">
              <div>今日帖子数</div>
              <div>
                <span class="num">{{ todayCount }}</span
                >条
              </div>
            </div>
            <div class="li_con flex">
              <div>总回复数</div>
              <div>
                <span class="num">{{ totalReplyCount }}</span
                >条
              </div>
            </div>
          </div>
        </div>
        <div class="right_top">
          <div class="top flex">
            <div class="line"></div>
            <span>与我有关</span>
          </div>
          <div class="li_box">
            <div
              :class="[
                selectNum == '1' ? 'selected' : '',
                'poiner',
                'flex',
                'li_con',
              ]"
              v-if="allocatedCount != undefined"
              @click="select('1')"
            >
              <div>待分配的</div>
              <div>
                <span class="num">{{ allocatedCount }}</span
                >条
              </div>
            </div>
            <div
              :class="[
                selectNum == '4' ? 'selected' : '',
                'poiner',
                'flex',
                'li_con',
              ]"
              @click="select('4')"
              v-if="pendingCount != undefined"
            >
              <div>待处理的</div>
              <div>
                <span class="num">{{ pendingCount }}</span
                >条
              </div>
            </div>
            <div
              :class="[
                selectNum == '5' ? 'selected' : '',
                'poiner',
                'flex',
                'li_con',
              ]"
              @click="select('5')"
              v-if="processedCount != undefined"
            >
              <div>已处理的</div>
              <div>
                <span class="num">{{ processedCount }}</span
                >条
              </div>
            </div>
            <div
              :class="[
                selectNum == '6' ? 'selected' : '',
                'poiner',
                'flex',
                'li_con',
              ]"
              @click="select('6')"
              v-if="myPostCount != undefined"
            >
              <div>我发起的</div>
              <div>
                <span class="num">{{ myPostCount }}</span
                >条
              </div>
            </div>
            <div
              :class="[
                selectNum == '2' ? 'selected' : '',
                'poiner',
                'flex',
                'li_con',
              ]"
              @click="select('2')"
              v-if="myCommentCount != undefined"
            >
              <div>我回复的</div>
              <div>
                <span class="num">{{ myCommentCount }}</span
                >条
              </div>
            </div>
            <div
              :class="[
                selectNum == '3' ? 'selected' : '',
                'poiner',
                'flex',
                'li_con',
              ]"
              @click="select('3')"
              v-if="receivedCommentCount != undefined"
            >
              <div>评论我的</div>
              <div>
                <span class="num">{{ receivedCommentCount }}</span
                >条
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <a-modal
    v-model:visible="visible"
    width="800px"
    :footer="null"
    @cancel="handleCancel"
  >
    <template #title>
      <div style="display: flex; align-items: center">
        <img
          src="@/assets/images/commiunty/title.png"
          alt=""
          style="width: 14px; height: 8px; margin-right: 9px"
        />
        <span>发布新帖</span>
      </div>
    </template>

    <div style="border: 1px solid #e8e8e8; border-radius: 4px">
      <a-row class="form-row">
        <a-col :span="4" class="label-col">
          <span class="warning" style="color: red;">*</span>
          <div class="label-text">主题</div>
        </a-col>
        <a-col :span="20" class="input-col">
          <a-input
            v-model:value="formState.title"
            placeholder="请输入主题（20字以内）"
            bordered="false"
            :maxlength="20"
          />
        </a-col>
      </a-row>

      <a-row class="form-row">
        <a-col :span="4" class="label-col">
          <span class="warning" style="color: red;">*</span>
          <div class="label-text">问题类型</div>
        </a-col>
        <a-col :span="20" class="input-col">
          <a-select
            v-model:value="formState.type"
            placeholder="请选择问题类型"
            bordered="false"
            allowClear
            style="width: 100%; border: none"
          >
            <a-select-option value="1">需求建议</a-select-option>
            <a-select-option value="2">官方公告</a-select-option>
            <!-- <a-select-option value="3">技术交流</a-select-option>
            <a-select-option value="4">经验分享</a-select-option>
            <a-select-option value="5">问答互助</a-select-option> -->
          </a-select>
        </a-col>
      </a-row>

      <a-row class="form-row">
        <a-col
          :span="4"
          class="label-col"
          style="align-items: flex-start; padding-top: 4px"
        >
          <span class="warning" style="color: red;">*</span>
          <div class="label-text">内容</div>
        </a-col>
        <a-col :span="20" class="input-col">
          <a-textarea
            v-model:value="formState.content"
            placeholder="请输入内容（2000字以内）"
            :rows="20"
            style="width: 100%"
            bordered="false"
            showCount
            :maxlength="2000"
          />
        </a-col>
      </a-row>

      <a-row class="form-row">
        <a-col :span="4" class="label-col">
          <div class="label-text">附件</div>
        </a-col>
        <a-col :span="20" class="input-col" style="align-items: center">
          <a-upload
            v-model:file-list="formState.file"
            :customRequest="uploadFile"
            :multiple="false"
            :max-count="1"
            accept=".doc,.docx,.xls,.xlsx,.pdf,.jpg,.jpeg,.png,.mp4,.mov,.avi"
            :show-upload-list="false"
          >
            <a-button
              v-if="ShowBtn"
              style="
                margin-left: 14px;
                background: linear-gradient(270deg, #0142fd 0%, #2475f9 100%);
                border-radius: 4px 4px 4px 4px;
                font-weight: 500;
                font-size: 14px;
                color: #ffffff;
                line-height: 20px;
                padding: 6px 16px;
              "
              :loading="uploading"
            >
              上传附件
            </a-button>
            <template v-if="!ShowBtn">
              <div class="custom-upload-item">
                <span
                  class="file-name"
                  v-if="formState.file && formState.file.length > 0"
                  >{{ formState.file[0].fileName }}</span
                >
                <a-tooltip title="删除">
                  <a-button
                    type="text"
                    danger
                    @click.stop="handleRemove"
                    class="delete-btn"
                  >
                    <delete-outlined v-if="formState.file" />
                  </a-button>
                </a-tooltip>
              </div>
            </template>
          </a-upload>
          <div
            v-if="formState.file.length < 1"
            style="
              font-size: 14px;
              color: rgba(0, 0, 0, 0.25);
              margin-left: 14px;
            "
          >
            注：视频限制20M以内
          </div>
        </a-col>
      </a-row>

      <a-row class="form-row">
        <a-col :span="4" class="label-col">
          <span class="warning" style="color: red;">*</span>
          <div class="label-text">是否匿名发布</div>
        </a-col>
        <a-col :span="20" class="input-col">
          <a-radio-group
            v-model:value="formState.anonymity"
            style="margin-left: 16px"
          >
            <a-radio :value="1">是</a-radio>
            <a-radio :value="0">否</a-radio>
          </a-radio-group>
        </a-col>
      </a-row>
    </div>

    <div style="text-align: center; margin-top: 16px">
      <a-button
        style="
          margin-right: 16px;
          background: rgba(12, 112, 235, 0.08);
          color: #0c70eb;
          font-size: 15px;
          line-height: 11px;
          border: none;
          border-radius: 4px 4px 4px 4px;
          padding: 9px 24px;
        "
        @click="handleCancel"
      >
        取消
      </a-button>
      <a-button
        style="
          background: linear-gradient(270deg, #0142fd 0%, #2475f9 100%);
          color: #ffffff;
          border-radius: 4px 4px 4px 4px;
          font-size: 15px;
          line-height: 11px;
          padding: 9px 24px;
        "
        @click="handleSubmit"
        :loading="addLoading"
        >提交</a-button
      >
    </div>
  </a-modal>
</template>
<script>
import { defineComponent, reactive, toRefs } from "vue";
import { uploadFileList } from "@/api/fileUpload/uploadFile.js";
import { message } from "ant-design-vue";
import {
  setMessage,
  getMsgList,
  getAllData,
  getAbouteMe,
} from "@/api/community/index.js";
import { DeleteOutlined } from "@ant-design/icons-vue";
import { useRouter } from "vue-router";
import { useHomeStore } from "@/store";
export default defineComponent({
  components: {
    DeleteOutlined,
  },
  setup() {
    const data = reactive({
      selectList: [
        { name: "全部", index: 0 },
        { name: "需求建议", index: 1 },
        { name: "官方公告", index: 2 },
        //{ name: "技术交流", index: 3 },
        //{ name: "经验分享", index: 4 },
        //{ name: "问答互勉", index: 5 },
      ],
      uploading: false,
      ShowBtn: true,
      inputValue: "",
      addLoading: false,
      userInfo: JSON.parse(localStorage.getItem("userInfo")),
      formState: {
        title: "",
        type: "",
        content: "",
        anonymity: "",
      },
      searchData: {
        pageSize: 10,
        pageNo: 1,
        title: "",
        type: "",
        status: "",
        myType: "",
      },
      dataList: [],
      rules: [
        {
          title: [
            { required: true, message: "请输入主题", trigger: "blur" },
            { max: 100, message: "主题不能超过100个字符", trigger: "blur" },
          ],
          type: [
            { required: true, message: "请选择问题类型", trigger: "change" },
          ],
          content: [
            { required: true, message: "请输入内容", trigger: "blur" },
            { min: 10, message: "内容至少10个字符", trigger: "blur" },
          ],
        },
      ],
      visible: false,
      selectIndex: 0,
      currentPage: 1,
      pageItemSize: 10,
      totalItemCount: 0,
      pageSizeOptions: ["10", "20", "30", "50"],
      name: undefined,
      formState: {
        title: "",
        type: undefined,
        content: "",
        anonymity: "",
        file: [],
      },
      selectNum: undefined,
      todayCount: 0,
      totalCount: 0,
      totalReplyCount: 0,
      myCommentCount: undefined, //我回复的
      myPostCount: undefined, //我发起的
      pendingCount: undefined, //待处理的
      processedCount: undefined, //已处理的
      receivedCommentCount: undefined, //评论我的
      allocatedCount: undefined, //待分配的
    });
    const getData = () => {
      data.searchData.title = data.inputValue;
      getMsgList(data.searchData).then((res) => {
        data.dataList = res.data.rows;
        data.totalItemCount = res.data.totalRows;
      });
      getAbouteMe(data.searchData).then((res) => {
        data.processedCount = res.data.processedCount;
        data.myCommentCount = res.data.myCommentCount;
        data.myPostCount = res.data.myPostCount;
        data.receivedCommentCount = res.data.receivedCommentCount;
        data.pendingCount = res.data.pendingCount;
        data.allocatedCount = res.data.allocatedCount;
        if (res.data.pendingCount) {
          counterStore.shequNum = res.data.pendingCount;
        } else if (res.data.allocatedCount) {
          counterStore.shequNum = res.data.allocatedCount;
        }
      });
    };
    getData();
    const seekContent = (value) => {
      data.searchData.title = data.inputValue;
      data.searchData.pageNo = 1;
      data.searchData.pageSize = 10;
      getData();
    };
    const selectBtn = (item) => {
      data.selectIndex = item.index;
      data.searchData.type = item.index == "0" ? "" : item.index;
      getData();
    };
    const pageChange = (page, pageSize) => {
      data.searchData.pageNo = page;
      getData();
    };
    const sizeChange = (current, size) => {
      data.searchData.pageSize = size;
      getData();
    };
    const pushNew = () => {
      data.formState = {
        title: "",
        type: undefined,
        content: "",
        anonymity: "",
        file: [],
      };
      data.visible = true;
    };
    const handleCancel = () => {
      data.formState = {
        title: "",
        type: undefined,
        content: "",
        anonymity: "",
        file: [],
      };
      data.visible = false;
      data.ShowBtn = true;
    };
    const counterStore = useHomeStore();
    const uploadFile = async (info) => {
      const file = info.file;
      const isVideo = file.type.startsWith("video/");
      const maxVideoSize = 20 * 1024 * 1024;

      if (isVideo && file.size > maxVideoSize) {
        message.error("视频文件大小不能超过20MB");
        info.onError();
        return;
      }
      data.uploading = true;
      data.ShowBtn = true;
      try {
        let formData = new FormData();
        formData.append("file", file);
        const result = await uploadFileList(formData);
        if (result.code === 200) {
          if (!data.formState.file) {
            data.formState.file = [{}];
          }
          data.formState.file[0] = {
            fileName: result.data.fileName,
            filePath: result.data.filePath,
            fileUrl: result.data.fileUrl,
          };
          message.success("上传成功");
          info.onSuccess();
          return result;
        } else {
          message.error(result.message || "上传失败");
          info.onError();
        }
      } catch (error) {
        console.error("上传出错:", error);
        message.error(error.message || "上传出错");
        info.onError();
      } finally {
        data.uploading = false;
        data.ShowBtn = false;
      }
    };

    const handleRemove = (e) => {
      e.stopPropagation();
      data.formState.file = [];
      data.ShowBtn = true;
    };
    const handleSubmit = () => {
      if (data.uploading == true) {
        message.error("附件上传中");
        return;
      }
      data.addLoading = true;
      if (!data.formState.title) {
        message.error("请填写主题！");
        data.addLoading = false;
        return;
      }

      if (!data.formState.type) {
        message.error("请选择问题类型！");
        data.addLoading = false;
        return;
      }

      if (!data.formState.content) {
        message.error("请填写内容！");
        data.addLoading = false;
        return;
      }

      if (data.formState.anonymity === "") {
        message.error("请选择是否匿名发布！");
        data.addLoading = false;
        return;
      }
      const submitData = {
        ...data.formState,
        type: Number(data.formState.type) || data.formState.type,
      };
      if (submitData.file && submitData.file.length > 0) {
        submitData.file = submitData.file[0];
      } else {
        submitData.file = {};
      }
      console.log(submitData, `提交数据`);

      setMessage(submitData).then((res) => {
        if (res.code == 200) {
          message.success("发帖成功");
          getData();
          data.visible = false;
          data.ShowBtn = true;
          data.addLoading = false;
        } else {
          data.addLoading = false;
        }
      });
    };
    const getView = () => {
      let type = "1";
      getAllData({ type: type }).then((res) => {
        data.totalReplyCount = res.data.totalCommentCount.match(/\d+/)[0];
        data.totalCount = res.data.totalCount.match(/\d+/)[0];
        data.todayCount = res.data.todayCount.match(/\d+/)[0];
      });
    };
    const dealType = (v) => {
      switch (v) {
        case 1:
          return "需求建议";
        case 2:
          return "官方公告";
        // case 3:
        //   return "技术交流";
        // case 4:
        //   return "经验分享";
        // case 5:
        //   return "问答互勉";
        default:
          return v;
      }
    };
    const dealCreat = (v) => {
      if (v.anonymity == 1) {
        return "匿名";
      } else if (v.anonymity == 0) {
        return v.createName;
      }
    };
    getView();
    const Router = useRouter();
    const proDetail = (v) => {
      Router.push({
        name: "communityDetail",
        query: {
          id: v,
        },
      });
    };
    const select = (v) => {
      (data.searchData.myType = v), getData();
      data.selectNum = v;
    };
    const resect = () => {
      data.searchData = {
        pageNo: 1,
        pageSize: 10,
      };
      data.selectNum = "";
      data.selectIndex = 0;
      data.inputValue = "";
      getData();
    };
    return {
      ...toRefs(data),
      resect,
      uploadFile,
      dealType,
      select,
      proDetail,
      counterStore,
      Router,
      dealCreat,
      getView,
      handleSubmit,
      getData,
      handleCancel,
      pageChange,
      handleRemove,
      seekContent,
      selectBtn,
      sizeChange,
      pushNew,
    };
  },
});
</script>
<style>
@import "./index.scss";
</style>