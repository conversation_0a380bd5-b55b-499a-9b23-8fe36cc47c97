<template>
  <div class="audit flex align-center">
    <div
      v-if="
        (userInfo.roleKeyList.includes('auditManager') && isRefuse == 1) ||
        isRefuse > 1 ||
        isRefuse == '-1'
      "
    >
      审核管理员审核意见
    </div>
    <a-button
      type="primary"
      @click="allotView"
      v-if="userInfo.roleKeyList.includes('auditManager') && isRefuse == 1"
      style="
        background: linear-gradient(270deg, #0142fd 0%, #2475f9 100%);
        border-radius: 4px;
        font-weight: 500;
        border: none;
        margin-left: 16px;
      "
    >
      分配
    </a-button>
  </div>
  <div class="margin_t_16" v-if="(text && isRefuse != 1) || isRefuse == '-1'">
    <div class="flowPath">
      <div style="padding: 12px 16px">
        <a-tag class="tag publish font_14" v-if="isRefuse > 1">已分配</a-tag>
        <a-tag class="tag refuse font_14" v-if="isRefuse == '-1'">已驳回</a-tag>
        <div class="desc" v-if="isRefuse == '-1'" style="white-space: pre-wrap">
          {{ auditReason }}
        </div>
        <div class="desc" v-if="text">{{ text }}阅处，谢谢</div>
        <div class="time" v-if="dealTime">{{ dealTime }}</div>
      </div>
    </div>
  </div>

  <div
    class="audit flex align-center"
    v-if="
      isRefuse == 3 ||
      isRefuse == 4 ||
      (isRefuse == 2 && mainUserId == userInfo.id)
    "
  >
    <div>主办部门反馈结果</div>
    <a-button
      @click="feedBack"
      v-if="isRefuse == 2 && mainUserId == userInfo.id"
      type="primary"
      style="
        background: linear-gradient(270deg, #0142fd 0%, #2475f9 100%);
        border-radius: 4px;
        font-weight: 500;
        border: none;
        margin-left: 16px;
      "
    >
      情况反馈
    </a-button>
  </div>
  <div class="margin_t_16" v-if="isRefuse == 3 || isRefuse == 4">
    <div class="flowPath">
      <div style="padding: 12px 16px">
        <a-tag class="tag future font_14" v-if="mainBack.finishTime"
          >预计{{ mainBack.finishTime }}完成</a-tag
        >
        <div class="depict" style="white-space: pre-wrap">
          {{ mainBack.content }}
        </div>
        <div class="flex align-center">
          <div style="margin-right: 40px">
            <span class="name">{{ mainBack.createName }}</span>
            <span class="sector">{{ mainBack.orgPathName }}</span>
          </div>
        </div>
        <div class="file" @click="previewFile(mainBack)">
          {{ mainBack.fileName }}
        </div>
      </div>
    </div>
  </div>
  <div class="margin_t_16" v-if="dataDetail.hostReject == 1">
    <div>主办部门处理结果</div>
    <div class="flowPath">
      <div style="padding: 12px 16px">
        <a-tag class="tag refuse font_14">已驳回</a-tag>
        <div class="desc" style="white-space: pre-wrap">
          {{
            dataDetail.processList[dataDetail.processList.length - 1]
              .auditReason
          }}
        </div>
        <div class="time">
          {{
            dataDetail.processList[dataDetail.processList.length - 1].createTime
          }}
        </div>
      </div>
    </div>
  </div>
  <div
    class="audit flex"
    style="align-items: center"
    v-if="
      (isRefuse == 2 && subUserIds.includes(userInfo.id)) ||
      (subBack &&
        subBack.length > 0 &&
        (isRefuse != 1 || dataDetail.hostReject == 1))
    "
  >
    <div>协办部门反馈结果</div>
    <a-button
      @click="feedBack"
      v-if="
        isRefuse == 2 &&
        subUserIds.includes(userInfo.id) &&
        !subBackEd.includes(userInfo.id)
      "
      type="primary"
      style="
        background: linear-gradient(270deg, #0142fd 0%, #2475f9 100%);
        border-radius: 4px;
        font-weight: 500;
        border: none;
        margin-left: 16px;
      "
    >
      情况反馈
    </a-button>
  </div>
  <div
    class="margin_t_16"
    v-if="subBack != undefined && (isRefuse > 1 || dataDetail.hostReject == 1)"
  >
    <div class="flowPath" v-for="(item, key) in subBack" :index="key">
      <div style="padding: 12px 16px">
        <a-tag class="tag future font_14" v-if="item.finishTime"
          >预计{{ item.finishTime }}完成</a-tag
        >
        <div class="depict" style="white-space: pre-wrap">
          {{ item.content }}
        </div>
        <div class="flex align-center">
          <div style="margin-right: 40px">
            <span class="name">{{ item.createName }}</span>
            <span class="sector">{{ item.orgPathName }}</span>
          </div>
          <div class="time">{{ item.createTime }}</div>
        </div>
        <div class="file" @click="previewFile(item)">{{ item.fileName }}</div>
      </div>
    </div>
  </div>

  <div
    class="submit flex align-center"
    v-if="userInfo.id == dataDetail.createBy && isRefuse == 3"
  >
    <div>提交人评价意见</div>
    <a-button
      @click="unsolved"
      type="primary"
      style="
        background: rgba(1, 61, 253, 0.1);
        border-radius: 4px;
        font-weight: 500;
        border: none;
        margin-left: 16px;
        color: #0c70eb;
      "
    >
      未解决
    </a-button>
    <a-button
      @click="resolved"
      type="primary"
      style="
        background: linear-gradient(270deg, #0142fd 0%, #2475f9 100%);
        border-radius: 4px;
        font-weight: 500;
        border: none;
        margin-left: 16px;
      "
    >
      已解决
    </a-button>
  </div>
  <div class="submit" v-if="isRefuse == 4">
    <div>提交人评价意见</div>
    <div class="margin_t_16">
      <div class="flowPath">
        <div style="padding: 12px 16px">
          <div class="flex align-center">
            <div class="flowPath">
              <div>
                <a-tag
                  class="tag publish font_14"
                  v-if="dataDetail.evaluation.auditResult == 1"
                  >已解决</a-tag
                >
                <a-tag class="tag refuse font_14" v-else>未解决</a-tag>
              </div>
            </div>
          </div>
          <div class="depict" style="white-space: pre-wrap">
            {{ dataDetail.evaluation.content }}
          </div>
          <div class="file" @click="previewFile(dataDetail.evaluation)">
            {{ dataDetail.evaluation.fileName }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { defineComponent, reactive, toRefs } from "vue";
import { setConfirm } from "@/api/community/index.js";
export default defineComponent({
  emits: ["allot-view", "feed-back", "unsolved", "preview-file"],
  props: {
    dataDetail: {
      type: Object,
      default: () => ({
        processList: [],
        feedbackList: [],
        dealUserList: [],
        evaluation: {},
      }),
    },
  },
  setup(props, { emit }) {
    const data = reactive({
      dataDetail: props.dataDetail,
      userInfo: JSON.parse(localStorage.getItem("userInfo")),
      mainUserId: props.dataDetail.dealUserList.find((item) => item.type == 1)
        ?.userId,
      subUserIds: props.dataDetail.dealUserList
        .filter((item) => item.type === 2)
        .map((item) => item.userId),
      mainBack: props.dataDetail.feedbackList.find((item) => item.type == 1),
      subBack: props.dataDetail.feedbackList.filter((item) => item.type == 2),
      text: props.dataDetail.dealUserList.find((item) => item.type == 1)
        ?.orgPathName,
      dealTime: props.dataDetail.processList.find((item) => item.process == 2)
        ?.createTime,
      isRefuse: props.dataDetail.status,
      subBackEd: props.dataDetail.feedbackList
        .filter((item) => item.type === 2)
        .map((item) => item.createBy),
      auditReason: props.dataDetail.processList.find(
        (item) => item.process == 2 && item.auditResult == 0
      )?.auditReason,
    });

    const allotView = () => {
      emit("allot-view");
    };

    const feedBack = () => {
      let noRefuse = false;
      if (data.subUserIds.includes(data.userInfo.id)) {
        noRefuse = true;
      }
      let params = {
        where: "feedBack",
        type: 1,
        noRefuse: noRefuse,
      };
      emit("feed-back", params);
    };

    const unsolved = () => {
      let params = {
        type: 2,
        where: "idea",
      };
      emit("unsolved", params);
    };

    const resolved = () => {
      let params = {
        type: 1,
        where: "idea",
      };
      emit("unsolved", params);
    };
    const previewFile = async (e) => {
      try {
        const { fileUrl: href, fileName: downName } = e;
        const token = localStorage.getItem("token") || "";
        const fileExtension = downName.split(".").pop().toLowerCase();
        const isImage = ["jpg", "jpeg", "png"].includes(fileExtension);
        const isVideo = ["mp4", "mov", "avi", "wmv", "mkv", "flv"].includes(
          fileExtension
        );
        if (isVideo) {
          let type = 1;
          emit("preview-file", e, type);
        } else if (isImage) {
          let type = 2;
          emit("preview-file", e, type);
        } else {
          let downloadUrl = new URL(href, window.location.origin).href;
          if (href.includes(window.location.origin)) {
            downloadUrl = href.replace(window.location.origin, "/portal");
          }
          const urlObj = new URL(downloadUrl);
          urlObj.searchParams.set("token", token);
          downloadUrl = urlObj.toString();
          window.open(downloadUrl, "_blank");
        }
      } catch (error) {
        console.error("文件处理失败:", error);
      }
      return false;
    };
    return {
      ...toRefs(data),
      allotView,
      feedBack,
      previewFile,
      resolved,
      unsolved,
    };
  },
});
</script>
<style lang="scss" scoped>
.audit {
  margin-top: 24px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}
.submit {
  margin-top: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}
.flowPath {
  background: #f5f7fc;
  border-radius: 4px;
  .publish {
    background: rgba(12, 112, 235, 0.1);
    color: #0c70eb;
    border-radius: 4px;
    border: none;
  }
  .refuse {
    background: rgba(245, 29, 15, 0.1);
    color: #f51d0f;
    border-radius: 4px;
    border: none;
  }
  .future {
    background: rgba(246, 118, 0, 0.1);
    color: #f67600;
    border-radius: 4px;
    border: none;
  }
  .desc {
    color: rgba(0, 0, 0, 0.65);
    margin-top: 9px;
  }
  .depict {
    color: rgba(0, 0, 0, 0.85);
    margin-top: 9px;
    width: 100%;
    word-wrap: break-word;
  }
  .time {
    color: rgba(0, 0, 0, 0.25);
  }
  .name {
    color: rgba(0, 0, 0, 0.45);
    margin-right: 12px;
  }
  .sector {
    color: rgba(0, 0, 0, 0.45);
  }
}
.file {
  font-weight: 400;
  font-size: 14px;
  color: #0c70eb;
  cursor: pointer;
}
</style>