<template>
  <div class="maxWidth flex align-center just-center spinClass">
    <a-spin tip="加载中，请稍后..." />
  </div>
</template>
<script>
import { defineComponent, reactive, toRefs, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import { productDetail } from "@/api/ticket/ticket.js";

export default defineComponent({
  setup() {
    const Router = useRouter();
    const Route = useRoute();
    const getSiderMenu = JSON.parse(window.localStorage.getItem("userInfo"));
    const roleKey = getSiderMenu.roleKeyList;
    const realName = getSiderMenu.realName;
    const data = reactive({
      menuDate: getSiderMenu.menuList,
    });

    //普通流程跳转
    const deal = (item) => {
      if (item.type == 6) {
        jumpToIpartner(item, 1);
        return false;
      }
      if (item.businessType == 1 && item.type == 5) {
        // 解决方案
        const searchParams = new URLSearchParams({
          id: item.businessId,
          workId: item.id,
          ticket: item.type == 5 ? item.id : "",
          action: "audit",
          routeName: "waitWork",
          auditStatus: 0,
          whereFrom: "ticket",
        });
        window.location.href = window.location.origin + "/backend/#/cityManage/ecologyDetail?" + searchParams.toString()
        return;
      }
      if (item.businessType == 1 && item.type == 7) {
        // 解决方案
        const searchParams = new URLSearchParams({
          id: item.businessId,
          ticket: item.type == 3 ? item.id : "",
          action: "audit",
          routeName: "waitWork",
          workId: item.id,
          policyId: `${item.businessId}/detail`,
          whereFrom: "ticket",
          businessType: item.businessType,
        });
        window.location.href = window.location.origin + "/backend/#/ticket/ticketDetail?" + searchParams.toString()
        return;
      }
      if (item.businessType == 1) {
        // 解决方案
        const searchParams = new URLSearchParams({
          id: item.businessId,
          ticket: item.type == 3 ? item.id : "",
          workId: item.id,
          action: "audit",
          auditStatus: 0,
          routeName: "waitWork",
          whereFrom: "ticket",
        });
        window.location.href = window.location.origin + "/backend/#/programManage/detailProgram?" + searchParams.toString()
        return;
      }
      if (item.businessType == 2 && item.type == 11) {
        // 能力
        const searchParams = new URLSearchParams({
          id: item.businessId,
          ticket: item.id,
          workId: item.id,
          action: "audit",
          auditStatus: 0,
          routeName: "waitWork",
          whereFrom: "ticket",
        });
        window.location.href = window.location.origin + "/backend/#/schemeModule/schemeEcoDetail?" + searchParams.toString()
        return;
      }
      if (item.businessType == 2) {
        // 能力
        const searchParams = new URLSearchParams({
          id: item.businessId,
          ticket: item.type == 3 ? item.id : "",
          workId: item.id,
          action: "audit",
          auditStatus: 0,
          routeName: "waitWork",
          whereFrom: "ticket",
        });
        window.location.href = window.location.origin + "/backend/#/schemeModule/detailModule?" + searchParams.toString()
        return;
      }
      if (item.businessType == 3) {
        // 用户场景
        const searchParams = new URLSearchParams({
          id: item.businessId,
          workId: item.id,
          action: "audit",
          ticket: item.type == 3 ? item.id : "",
          routeName: "waitWork",
          auditStatus: 0,
          whereFrom: "ticket",
        });
        window.location.href = window.location.origin + "/backend/#/sceneManage/detailScene?" + searchParams.toString()
        return;
      }
      if (item.businessType == 4) {
        // 产品
        productDetail(item.businessId).then((res) => {
          let market = res.data.marketplaceList.map((val) => {
            return val.applicationMarket;
          });
          if (market.includes(0)) {
            const searchParams = new URLSearchParams({
              id: item.businessId,
              ticket: item.type == 3 ? item.id : "",
              workId: item.id,
              action: "audit",
              routeName: "waitWork",
              auditStatus: 0,
              whereFrom: "ticket",
            });
            window.location.href = window.location.origin + "/backend/#/productManage/detailProduct?" + searchParams.toString()
            return;
          }
          if (market.includes(2)) {
            const searchParams = new URLSearchParams({
              id: item.businessId,
              ticket: item.type == 3 ? item.id : "",
              workId: item.id,
              action: "audit",
              routeName: "waitWork",
              auditStatus: 0,
              whereFrom: "ticket",
            });
            window.location.href = window.location.origin + "/backend/#/busProductManage/detailBusProduct?" + searchParams.toString()
            return;
          }
          if (market.includes(3)) {
            const searchParams = new URLSearchParams({
              id: item.businessId,
              ticket: item.type == 3 ? item.id : "",
              workId: item.id,
              action: "audit",
              routeName: "waitWork",
              auditStatus: 0,
              whereFrom: "ticket",
            });
            window.location.href = window.location.origin + "/backend/#/productManage/detailProduct?" + searchParams.toString()
            return;
          }
        }).catch((err) => { });
        return;
      }
      if (item.businessType == 5) {
        // 专区
        const searchParams = new URLSearchParams({
          id: item.businessId,
          action: "audit",
          routeName: "waitWork",
          workId: item.id,
          expertId: `${item.businessId}/detail`,
          whereFrom: "ticket",
        });
        window.location.href = window.location.origin + "/backend/#/prefecture/detailExpert?" + searchParams.toString()
        return;
      }
      if (item.businessType == 6) {
        // 政策
        const searchParams = new URLSearchParams({
          id: item.businessId,
          action: "audit",
          routeName: "waitWork",
          workId: item.id,
          policyId: `${item.businessId}/detail`,
          whereFrom: "ticket",
        });
        window.location.href = window.location.origin + "/backend/#/policy/policyDetail?" + searchParams.toString()
        return;
      }
      if (item.businessType == 7) {
        // 撰写
        const searchParams = new URLSearchParams({
          id: item.businessId,
          action: "audit",
          workId: item.id,
          routeName: "waitWork",
          auditStatus: 0,
          whereFrom: "ticket",
        });
        window.location.href = window.location.origin + "/backend/#/writeManage/detailWrite?" + searchParams.toString()
      }
      if (item.businessType == 8 && item.type == 3) {
        // 需求方案
        const searchParams = new URLSearchParams({
          id: item.businessId,
          ticket: item.type == 3 ? item.id : "",
          action: "audit",
          workId: item.id,
          routeName: "waitWork",
          auditStatus: 0,
          whereFrom: "ticket",
        });
        window.location.href = window.location.origin + "/backend/#/reqProgramManage/detailRequirementProgram?" + searchParams.toString()
        return;
      }
      if (item.businessType == 8) {
        // 需求方案
        const searchParams = new URLSearchParams({
          id: item.businessId,
          action: "audit",
          workId: item.id,
          routeName: "waitWork",
          auditStatus: 0,
          whereFrom: "ticket",
        });
        window.location.href = window.location.origin + "/backend/#/reqProgramManage/detailRequirementProgram?" + searchParams.toString()
        return;
      }
      if (item.businessType == 9 && item.type == 7) {
        // 政策洞察
        const searchParams = new URLSearchParams({
          id: item.businessId,
          ticket: item.type == 3 ? item.id : "",
          action: "audit",
          routeName: "waitWork",
          workId: item.id,
          policyId: `${item.businessId}/detail`,
          whereFrom: "ticket",
          businessType: item.businessType,
        });
        window.location.href = window.location.origin + "/backend/#/ticket/ticketDetail?" + searchParams.toString()
        return;
      }
      if (item.businessType == 9) {
        // 政策洞察
        const searchParams = new URLSearchParams({
          id: item.businessId,
          ticket: item.type == 3 ? item.id : "",
          action: "audit",
          routeName: "waitWork",
          workId: item.id,
          policyId: `${item.businessId}/detail`,
          whereFrom: "ticket",
        });
        window.location.href = window.location.origin + "/backend/#/policyView/viewDetail?" + searchParams.toString()
        return;
      }
      if (item.businessType == 10 && item.type == 11) {
        // 方案场景
        const searchParams = new URLSearchParams({
          id: item.businessId,
          ticket: item.id,
          action: "audit",
          routeName: "waitWork",
          workId: item.id,
          policyId: `${item.businessId}/detail`,
          whereFrom: "ticket",
        });
        window.location.href = window.location.origin + "/backend/#/programManage/proSceneEcoDetail?" + searchParams.toString()
        return;
      }
      if (item.businessType == 10) {
        // 方案场景
        const searchParams = new URLSearchParams({
          id: item.businessId,
          ticket: item.type == 3 ? item.id : "",
          action: "audit",
          routeName: "waitWork",
          workId: item.id,
          policyId: `${item.businessId}/detail`,
          whereFrom: "ticket",
        });
        window.location.href = window.location.origin + "/backend/#/programManage/proSceneDetail?" + searchParams.toString()
        return
      }
      if (item.businessType == 12 && item.type == 5) {
        // 地市方案
        const searchParams = new URLSearchParams({
          id: item.businessId,
          workId: item.id,
          ticket: item.type == 5 ? item.id : "",
          action: "audit",
          routeName: "waitWork",
          auditStatus: 0,
          whereFrom: "ticket",
        });
        window.location.href = window.location.origin + "/backend/#/cityManage/ecologyDetail?" + searchParams.toString()
        return;
      }
      if (item.businessType == 12) {
        // 地市方案
        const searchParams = new URLSearchParams({
          id: item.businessId,
          action: "audit",
          routeName: "waitWork",
          workId: item.id,
          policyId: `${item.businessId}/detail`,
          whereFrom: "ticket",
        });
        window.location.href = window.location.origin + "/backend/#/cityManage/schemeDetail?" + searchParams.toString()
        return;
      }
      if (item.businessType == 13) {
        // 地市案例
        const searchParams = new URLSearchParams({
          id: item.businessId,
          workId: item.id,
          ticket: item.type == 3 ? item.id : "",
          action: "audit",
          routeName: "waitWork",
          policyId: `${item.businessId}/detail`,
          whereFrom: "ticket",
        });
        window.location.href = window.location.origin + "/backend/#/cityManage/caseDetail?" + searchParams.toString()
        return;
      }
      if (item.businessType == 15) {
        // 省级案例
        const searchParams = new URLSearchParams({
          id: item.businessId,
          workId: item.id,
          ticket: item.type == 3 ? item.id : "",
          action: "audit",
          routeName: "waitWork",
          policyId: `${item.businessId}/detail`,
          whereFrom: "ticket",
        });
        window.location.href = window.location.origin + "/backend/#/programManage/proCaseDetail?" + searchParams.toString()
        return;
      }
      if (item.businessType == 21) {
        // HDICT产品
        const searchParams = new URLSearchParams({
          ticket: item.type == 3 ? item.id : "",
          id: item.businessId,
          workId: item.id,
          action: "audit",
          routeName: "waitWork",
          policyId: `${item.businessId}/detail`,
          whereFrom: "ticket",
        });
        window.location.href = window.location.origin + "/backend/#/HdictList/HdictDetail?" + searchParams.toString()
        return;
      }
      if (item.businessType == 0) {
        const searchParams = new URLSearchParams({
          id: item.businessId,
          // ticket: item.type == 3 ? item.id : "",
          workId: item.id,
          action: "audit",
          auditStatus: 0,
          routeName: "waitWork",
          whereFrom: "ticket",
        });
        window.location.href = window.location.origin + "/backend/#/downloadReview/downloadReviewDetail?" + searchParams.toString()
      }
    };

    //方案审核
    const approveInRowSolution = (row) => {
      let json = row.procVars;
      //初审管理员
      let role1 = roleKey.includes("trialManager");// || roleKey.includes("industryManager");
      let chushenRole = ["肖明", "王达伟", "丁德胜", "徐剑宏", "魏宇珺", "吴鹏", "仲伟奇", "许文杰"];//行业固定人员初审
      let role2 = chushenRole.includes(realName);
      //是否为初审
      let isFanfa = row.taskName.includes("初审");
      if (json.businessType == 1) {
        if ((role1 || role2) && (json.type == 1 || json.type == 3) && isFanfa) {
          const searchParams = new URLSearchParams({
            id: json.solutionId,
            type: json.type,
            ticket: row.procInsId,
            workId: json.solutionId,
            action: "backlog",
            auditStatus: 0,
            routeName: "backlogProgram",
            whereFrom: 'newTicket',
            procInsId: row.procInsId,
            taskId: row.taskId,
          });
          window.location.href = window.location.origin + "/backend/#/programManage/addProgram?" + searchParams.toString()
          return
        }
        if ((role1 || role2) && json.type == 11 && isFanfa) {
          const searchParams = new URLSearchParams({
            id: json.solutionId,
            type: json.type,
            ticket: row.procInsId,
            workId: json.solutionId,
            action: "backlog",
            auditStatus: 0,
            routeName: "backlogProgram",
            whereFrom: 'newTicket',
            procInsId: row.procInsId,
            taskId: row.taskId,
          });
          window.location.href = window.location.origin + "/backend/#/programManage/ecopartnerChange?" + searchParams.toString()
          return
        }
        if (json.type == 11) {
          const searchParams = new URLSearchParams({
            id: json.solutionId,
            ticket: row.procInsId,
            workId: json.solutionId,
            action: "audit",
            auditStatus: 0,
            routeName: "backlogProgram",
            whereFrom: "newTicket",
            procInsId: row.procInsId,
            taskId: row.taskId,
          });
          window.location.href = window.location.origin + "/backend/#/programManage/ecopartnerDetail?" + searchParams.toString()
          return;
        }
        const searchParams = new URLSearchParams({
          id: json.solutionId,
          ticket: json.type == 1 || json.type == 3 ? row.procInsId : "",
          workId: json.solutionId,
          action: "audit",
          auditStatus: 0,
          routeName: "backlogProgram",
          whereFrom: "newTicket",
          procInsId: row.procInsId,
          taskId: row.taskId,
        });
        window.location.href = window.location.origin + "/backend/#/programManage/detailProgram?" + searchParams.toString()
        return;
      }
      if (json.businessType == 2) {
        if ((role1 || role2) && (json.type == 1 || json.type == 3) && isFanfa) {
          const searchParams = new URLSearchParams({
            id: json.businessId,
            type: json.type,
            ticket: row.procInsId,
            workId: json.businessId,
            action: "backlog",
            auditStatus: 0,
            routeName: "backlogSchemeProgram",
            whereFrom: 'newTicket',
            procInsId: row.procInsId,
            taskId: row.taskId,
          });
          window.location.href = window.location.origin + "/backend/#/schemeModule/addModule?" + searchParams.toString()
          return;
        }
        if ((role1 || role2) && json.type == 11 && isFanfa) {
          const searchParams = new URLSearchParams({
            id: json.businessId,
            type: json.type,
            ticket: row.procInsId,
            workId: json.businessId,
            action: "backlog",
            auditStatus: 0,
            routeName: "backlogSchemeProgram",
            whereFrom: 'newTicket',
            procInsId: row.procInsId,
            taskId: row.taskId,
          });
          window.location.href = window.location.origin + "/backend/#/schemeModule/schemeEcoForm?" + searchParams.toString()
          return;
        }
        if (json.type == 11) {
          const searchParams = new URLSearchParams({
            id: json.businessId,
            ticket: row.procInsId,
            workId: json.businessId,
            action: "audit",
            auditStatus: 0,
            routeName: "backlogSchemeProgram",
            whereFrom: "newTicket",
            procInsId: row.procInsId,
            taskId: row.taskId,
          });
          window.location.href = window.location.origin + "/backend/#/schemeModule/schemeEcoDetail?" + searchParams.toString()
          return;
        }
        const searchParams = new URLSearchParams({
          id: json.businessId,
          ticket: json.type == 1 || json.type == 3 ? row.procInsId : "",
          workId: json.businessId,
          action: "audit",
          auditStatus: 0,
          routeName: "backlogSchemeProgram",
          whereFrom: "newTicket",
          procInsId: row.procInsId,
          taskId: row.taskId,
        });
        window.location.href = window.location.origin + "/backend/#/schemeModule/addModule?" + searchParams.toString()
        return;
      }
    };

    //调度审核
    const approveInRowProcess = (row) => {
      if (row.procDefName == "售中调度工单") {
        if (row.taskName == "生态反馈") {
          Router.push({
            name: "coordination",
            query: {
              orderId: row.procVars.id,
              action: "selectApply",
              taskId: row.taskId,
              active: "调度中心"
            },
          });
        } else if (row.taskName == "生态评分") {
          Router.push({
            name: "coordination",
            query: {
              orderId: row.procVars.id,
              action: "writeScore",
              procInsId: row.procInstId,
              taskId: row.taskId,
              active: "调度中心"
            },
          });
        } else if (row.taskName == "提交申请") {
          Router.push({
            name: "coordination",
            query: {
              orderId: row.procVars.id,
              action: "reSelectPage",
              procInsId: row.procInstId,
              taskId: row.taskId,
              active: "调度中心"
            },
          });
        }
      } else if (row.procDefName == "售前调度工单") {
        if (row.taskName == "调度中" || row.taskName == "地市调度人" || row.taskName == "省级调度人") {
          Router.push({
            name: "starWork",
            query: {
              id: row.procInsId,
              type: "multiModule",
              // type: "toSelect",
              procInsId: row.procInsId,
              taskId: row.taskId,
              active: "调度中心",
              orderId: row.procVars.id,
            },
          });
        } else if (row.taskName == "省直调度管理员") {
          Router.push({
            name: "starWork",
            query: {
              id: row.procInsId,
              type: "multiModule",
              // type: "toSelectFirst",
              procInsId: row.procInsId,
              taskId: row.taskId,
              active: "调度中心",
              orderId: row.procVars.id,
            },
          });
        } else if (row.taskName == "生态反馈" || row.taskName == "能力方反馈") {
          Router.push({
            name: "starWork",
            query: {
              id: row.procInsId,
              type: "multiModule",
              // type: "selectApply",
              procInsId: row.procInsId,
              taskId: row.taskId,
              active: "调度中心",
              orderId: row.procVars.id,
            },
          });
        } else if (row.taskName == "生态评分") {
          Router.push({
            name: "starWork",
            query: {
              id: row.procInsId,
              type: "multiModule",
              // type: "writeScore",
              procInsId: row.procInsId,
              taskId: row.taskId,
              active: "调度中心",
              orderId: row.procVars.id,
            },
          });
        } else if (row.taskName == "调度人确认") {
          Router.push({
            name: "starWork",
            query: {
              id: row.procInsId,
              type: "multiModule",
              // type: "submitPage",
              procInsId: row.procInsId,
              taskId: row.taskId,
              active: "调度中心",
              orderId: row.procVars.id,
            },
          });
        } else if (row.taskName == "提交申请") {
          if (row.procVars.selfSupport) {
            Router.push({
              name: "starWork",
              // name: "reStarWork",// 废弃reStarWork
              query: {
                id: row.procInsId,
                type: "multiModule",
                // type: "submitPage",
                procInsId: row.procInsId,
                taskId: row.taskId,
                active: "调度中心",
                orderId: row.procVars.id,
              },
            });
          } else {
            Router.push({
              name: "starWork",
              // name: "reStarWork",// 废弃reStarWork
              query: {
                id: row.procInsId,
                type: "multiModule",
                // type: "reStar",
                procInsId: row.procInsId,
                taskId: row.taskId,
                active: "调度中心",
                orderId: row.procVars.id,
              },
            });
          }
        }
      }
    };

    const jumpToIpartner = async (item, styleType) => {
      try {
        // 获取工单状态
        const {
          data: { status },
        } = await detail(item.id);
        // 如果工单已审核,跳转到已办列表
        if (status === 1) {
          message.warning("工单已审核");
          // 跳转前刷新列表
          getTableData(data.searchParams);
          // 触发已办列表更新
          eventBus.emit("getFinishWorkTable");
          window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=finishWork&routeName=finishWork";
          return;
        }
        // 获取ipartner系统token


        const tokenRes = await getEcoToken({
          phone: getSiderMenu.phone,
          businessType: item.businessType,
          businessId: item.businessId,
          style: styleType,
        });
        // token获取成功,打开ipartner系统
        if (tokenRes.code === 200) {
          const token = encodeURIComponent(tokenRes.msg);
          const ipartnerUrl = "https://ipartner.jsdict.cn/static/checkLogin";
          // const ipartnerUrl = "http://***********:8013/static/checkLogin";
          window.open(`${ipartnerUrl}?token=${token}`, "_blank");
        }
      } catch (error) {
        console.error("跳转ipartner失败:", error);
        message.error("系统异常,请稍后重试");
      }
    };

    watch(
      () => Route,
      (newV) => {
        let params = newV.query;
        if (params.to == 'starWork') {
          // 售前调度
          Router.push({
            name: "starWork",
            query: params
          });
        }
        if (params.to == 'coordination') {
          // 售中调度
          Router.push({
            name: "coordination",
            query: params
          });
        }
        if (params.to == 'process') {
          // 待处理流程
          let processInfo = JSON.parse(localStorage.getItem('processInfo'));
          if (processInfo.businessType == 11 && roleKey.includes('sysAdmin')) {
            let routeName = "";
            if (processInfo.type == 8) {
              // 售前
              routeName = "endWork"
            } else if (processInfo.type == 9) {
              // 售中
              routeName = "coordination"
            }
            Router.push({
              name: routeName,
              query: {
                id: processInfo.procInstId,
                type: "dealedAll",
                procInsId: processInfo.procInstId,
                taskId: processInfo.taskId,
                active: "调度中心",
                orderId: processInfo.procVars.id,
              },
            });
            return
          }
          if (processInfo.formType == 0) {
            // 普通流程处理
            deal(processInfo);
          } else {
            // 工作流程处理
            if ((processInfo.businessType == 1 || processInfo.businessType == 2) && (processInfo.type == 0 || processInfo.type == 1 || processInfo.type == 3 || processInfo.type == 11)) {
              // 解决方案审核和能力审核
              approveInRowSolution(processInfo);
              return
            }
            if (processInfo.businessType == 11 && (processInfo.type == 8 || processInfo.type == 9)) {
              // 调度支撑审核
              approveInRowProcess(processInfo);
            }
          }
        }
      },
      { immediate: true, deep: true }
    );

    return {
      ...toRefs(data),
    };
  },
});
</script>

<style lang="scss" scoped>
.spinClass {
  min-height: 100vh;
}
</style>
