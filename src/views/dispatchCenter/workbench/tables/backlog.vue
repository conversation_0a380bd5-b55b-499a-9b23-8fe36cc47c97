<template>
  <div class="qilin-backlog">
    <div class="qilin-backlog-content">
      <QilinTable
        ref="tableRef"
        :loading="tableLoading"
        v-model:tableConfig="tableConfig"
        @changeCurrentPage="changeCurrentPage"
        @changeCurrentSize="changeCurrentSize"
      >
        <template v-slot:title="slotData">
          <div
            v-html="getTitle(slotData.data.scope.row)"
            style="
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            "
          ></div>
        </template>
        <template v-slot:typeName="slotData">
          <div v-html="getTypeName(slotData.data.scope.row)"></div>
        </template>
        <template v-slot:status="slotData">
          <div
            :class="
              getProcess(slotData.data.scope.row) == '审核中'
                ? 'inCheck'
                : 'complete'
            "
          >
            {{ getProcess(slotData.data.scope.row) }}
          </div>
        </template>
        <template v-slot:assigneeName="slotData">
          <a-popover title="" :trigger="['hover', 'focus']">
            <template #content>
              <p style="margin-bottom: 0">
                联系电话：{{ dealData(slotData.data.scope.row.assigneePhone) }}
              </p>
              <p style="margin-bottom: 0">
                所属部门：{{ dealData(slotData.data.scope.row.assigneeOrgName) }}
              </p>
            </template>
            {{ dealData(slotData.data.scope.row.assigneeName) }}
          </a-popover>
        </template>
        <template v-slot:businessType="slotData">
          <div v-html="getBusinessTypeName(slotData.data.scope.row)"></div>
        </template>
        <template v-slot:provider="slotData">
          <div class="ellipsis-cell">
            {{ providerWith(slotData.data.scope.row.startOrgName) }}
          </div>
        </template>
      </QilinTable>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { message } from "ant-design-vue";
import { useRouter } from "vue-router";
import QilinTable from "../components/table.vue";
import {
  getDispatchTodoList,
  getClassificationData,
} from "@/api/dispatchCenter/backlog&completed.js";

const router = useRouter();
const props = defineProps({
  searchParams: {
    type: Object,
    default: () => ({}),
  },
});
const tableRef = ref(null);
const tableLoading = ref(false);
/* 计算属性 */
const getTitle = computed(() => (data) => {
  return data.title || "-";
});

const getTypeName = computed(() => (data) => {
  const typeMap = {
    1: "上架审核",
    0: "下架审核",
    2: "能力撰写",
    8: "售前工单",
    4: "售前工单",
    9: "售中工单",
  };
  return typeMap[data.type] || "";
});

const getBusinessTypeName = computed(() => (data) => {
  const businessTypeMap = {
    1: "解决方案",
    2: "自有能力",
    3: "场景",
    4: "标准产品",
    5: "专区",
    6: "政策",
    7: "撰写",
    11: "调度支撑",
  };
  return businessTypeMap[data.businessType] || "";
});
const dealData = (v) => {
  if (v) {
    return v;
  } else {
    return "-";
  }
};
const getProcess = computed(() => () => "审核中");

const providerWith = (value) => {
  if (!value) return "";
  const parts = value.split("/");
  const [first, second, ...rest] = parts;
  if (first === "江苏公司") {
    return rest.length > 0 ? `${second}/${rest.join("/")}` : second;
  }
  return value;
};
// 切换页码事件监听
const changeCurrentPage = (page) => {
  tableConfig.paginationsObj.currentPage = page;
  getTableData(props.searchParams);
};
// 切换页码事件监听
const changeCurrentSize = (size) => {
  tableConfig.paginationsObj.pageSize = size;
  getTableData(props.searchParams);
};
const getTableData = (searchData) => {
  tableLoading.value = true;
  let params = {
    pageNo: tableConfig.paginationsObj.currentPage,
    pageSize: tableConfig.paginationsObj.pageSize,
  };
  if (searchData) {
    Object.keys(searchData).forEach((key) => {
      const val = searchData[key];
      if (val !== undefined && val !== null && (val || val === 0)) {
        params[key] = val;
      }
    });
  }
  const paramsOld = {
    pageNum: 1,
    pageSize: 10,
  };
  getClassificationData(paramsOld)
    .then((res) => {
      if (res.code !== 200) {
        return Promise.reject(new Error(res.msg || "分类数据获取失败"));
      }
      const rows = res.data.rows || [];
      const targetCategory = rows.find(
        (item) => item.categoryName === "售前调度工单"
      );
      if (!targetCategory) {
        return Promise.reject(new Error("未找到售前调度工单分类"));
      }
      params.category = targetCategory.code;
      return getDispatchTodoList(params);
    })
    .then((res) => {
      if (res.code === 200) {
        tableConfig.tableData = res.data.rows || [];
        tableConfig.paginationsObj.total = res.data.totalRows || 0;
      } else if (res.code !== 401) {
        message.error(res.msg || "系统错误");
      }
      tableLoading.value = false;
    })
    .catch((err) => {
      console.error(err);
      tableLoading.value = false;
      message.error(err.message || "请求失败");
    });
};
//表格审核
const approveInRow = (row) => {
   if (userInfo.roleKeyList.includes('sysAdmin')) {
    const routeName = row.procDefName === "售前调度工单" ? "endWork" : "coordination";
    router.push({
      name: routeName,
      query: {
        id: row.procInstId,
        type: "dealedAll",
        procInsId: row.procInstId,
        taskId: row.taskId,
        active: "调度中心",
        orderId: row.id,
      },
    });
    return
  }
  if (row.procDefName == "售中调度工单") {
    if (row.taskName == "生态反馈") {
      router.push({
        name: "coordination",
        query: {
          orderId: row.id,
          action: "selectApply",
          taskId: row.taskId,
          active: "调度中心"
        },
      });
    }else if (row.taskName == "生态评分") {
      router.push({
        name: "coordination",
        query: {
          orderId: row.id,
          action: "writeScore",
          procInsId: row.procInstId,
          taskId: row.taskId,
          active: "调度中心"
        },
      });
    }else if (row.taskName == "提交申请") {
      router.push({
        name: "coordination",
        query: {
          orderId: row.id,
          action: "reSelectPage",
          procInsId: row.procInstId,
          taskId: row.taskId,
          active: "调度中心"
        },
      });
    }
  } else if (row.procDefName == "售前调度工单") {
    if (
      row.taskName == "调度中" ||
      row.taskName == "地市调度人" ||
      row.taskName == "省级调度人"
    ) {
      router.push({
        name: "starWork",
        query: {
          id: row.procInstId,
          type: "multiModule",
          // type: "toSelect",
          procInsId: row.procInstId,
          taskId: row.taskId,
          active: "调度中心",
          orderId: row.id,
        },
      });
    } else if (row.taskName == "省直调度管理员") {
      router.push({
        name: "starWork",
        query: {
          id: row.procInstId,
          type: "multiModule",
          // type: "toSelectFirst",
          procInsId: row.procInstId,
          taskId: row.taskId,
          active: "调度中心",
          orderId: row.id,
        },
      });
    } else if (row.taskName == "生态反馈" || row.taskName == "能力方反馈") {
      router.push({
        name: "starWork",
        query: {
          id: row.procInstId,
          type: "multiModule",
          // type: "selectApply",
          procInsId: row.procInstId,
          taskId: row.taskId,
          active: "调度中心",
          orderId: row.id,
        },
      });
    } else if (row.taskName == "生态评分") {
      router.push({
        name: "starWork",
        query: {
          id: row.procInstId,
          type: "multiModule",
          // type: "writeScore",
          procInsId: row.procInstId,
          taskId: row.taskId,
          active: "调度中心",
          orderId: row.id,
        },
      });
    } else if (row.taskName == "调度人确认") {
      router.push({
        name: "starWork",
        query: {
          id: row.procInstId,
          type: "multiModule",
          // type: "submitPage",
          procInsId: row.procInstId,
          taskId: row.taskId,
          active: "调度中心",
          orderId: row.id,
        },
      });
    }
    if (row.taskName == "提交申请") {
      if (row.tasks[0].procVars.selfSupport) {
        router.push({
          name: "starWork",
          // name: "reStarWork",// 废弃reStarWork
          query: {
            id: row.procInstId,
            type: "multiModule",
            // type: "submitPage",
            procInsId: row.procInstId,
            taskId: row.taskId,
            active: "调度中心",
            orderId: row.id,
          },
        });
      } else {
        router.push({
          name: "starWork",
          // name: "reStarWork",// 废弃reStarWork
          query: {
            id: row.procInstId,
            type: "multiModule",
            // type: "reStar",
            procInsId: row.procInstId,
            taskId: row.taskId,
            active: "调度中心",
            orderId: row.id,
          },
        });
      }
    }
  }
};
const tableConfig = reactive({
  elTableConfig: {
    border: false,
    tableLayout: 'fixed',
    size: 'small',
  },
  headerConfig: [
    {
      label: "工单标题",
      prop: "title",
      type: "slot",
      slotName: "title",
      align: "left",
      ellipsis: true,
      width: 220,
    },
    {
      label: "项目名称",
      prop: "projectName",
      type: "text",
      align: "left",
      ellipsis: true,
      width: 220,
    },
    {
      label: "工单类型",
      prop: "type",
      type: "slot",
      slotName: "typeName",
      align: "left",
    },
    // {
    //   label: "业务模块",
    //   prop: "businessType",
    //   type: "slot",
    //   slotName: "businessType",
    //   align: "center",
    // },
    {
      label: "审核进度",
      prop: "status",
      type: "slot",
      slotName: "status",
      align: "left",
    },
    {
      label: "当前处理人",
      prop: "assigneeName",
      type: "slot",
      slotName: "assigneeName",
      align: "left",
    },
    {
      label: "提交人员",
      prop: "startUserName",
      type: "text",
      align: "left",
      ellipsis: true,
    },
    {
      label: "提交方",
      prop: "provider",
      type: "slot",
      slotName: "provider",
      align: "left",
      ellipsis: true,
    },
    {
      label: "提交时间",
      prop: "createTime",
      type: "text",
      align: "left",
      ellipsis: true,
    },
    {
      label: "操作",
      type: "operate",
      align: "center",
      width: 60,
      fixed: "right",
      hideSeparate: true,
      operateOptions: [
        {
          buttonName: "审核",
          buttonType: "danger",
          buttonSize: "default",
          text: true,
          buttonEvent: approveInRow,
        },
      ],
    },
  ],
  tableData: [],
  paginationsObj: {
    currentPage: 1,
    pageSize: 10,
    pageSizes: [10, 20, 30],
    total: 0,
    layout: "total,sizes,prev,pager,next,jumper",
  },
});
const setOperateButton = () => {
  const buttonName = isAdmin.value ? "详情" : "审核";
  const buttonType = isAdmin.value ? "primary" : "danger"; // 管理员为蓝色，非管理员为红色

  tableConfig.headerConfig[tableConfig.headerConfig.length - 1].operateOptions = [
    {
      buttonName: buttonName,
      buttonType: buttonType,
      buttonSize: "default",
      text: true,
      buttonEvent: approveInRow
    }
  ];
};

watch(
  () => props.searchParams,
  (newVal) => {
    tableConfig.paginationsObj.currentPage = 1;
    getTableData(newVal);
  },
  { immediate: true }
);
const userInfo = reactive(JSON.parse(window.localStorage.getItem("userInfo")));
const isAdmin = computed(() => userInfo.roleKeyList.includes('sysAdmin'));
onMounted(() => {
  setOperateButton();
  // tableConfig.headerConfig[tableConfig.headerConfig.length - 1].operateOptions[0].buttonName = userInfo.roleKeyList.includes('sysAdmin') ? "详情" : '审核';
});
watch(isAdmin, () => {
  setOperateButton();
});
</script>

<style lang="scss" scoped>
.qilin-backlog {
  width: 100%;
  height: 100%;
  display: flex;
  flex-flow: column nowrap;

  > .qilin-backlog-content {
    // margin-top: 16px;
    background-color: #fff;
    padding: 16px 20px;
    border-radius: 4px;
    flex: 1;
    display: flex;
    flex-flow: column nowrap;
    overflow: hidden;

    :deep(.el-table) {

      .ellipsis-cell {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: block;
      }
      .inCheck {
        width: 60px;
        text-align: center;
        background: rgba(246, 118, 0, 0.1);
        color: #f67600;
      }

      .complete {
        width: 60px;
        text-align: center;
        background: rgba(0, 189, 98, 0.1);
        color: #00bd62;
      }
      .returnBack {
        width: 60px;
        text-align: center;
        background: rgba(245, 29, 15, 0.1);
        color: #f51d0f;
      }
      .el-button:first-child {
        padding: 0;
      }
    }

    .slot-box > span.status-dot {
      position: relative;

      &::before {
        content: "";
        position: absolute;
        left: -15px;
        top: 50%;
        transform: translateY(-50%);
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 5px;
      }

      &.success::before {
        background-color: #19be6b;
      }

      &.reject::before {
        background-color: #ed4014;
      }

      &.normal::before {
        background-color: #2d8cf0;
      }
    }
  }
}
</style>