<template>
  <div>
    <div class="searchInfo">
      <div class="vocationPull">
        <a-config-provider :locale="zhCN" :getPopupContainer="(triggerNode) => triggerNode.parentNode">
          <a-input-search v-model:value="searchName" :placeholder="'请输入' + businessModuleTypeComputed + '名称'"
            @search="seekContent" @keyup.enter="seekContent">
            <template #enterButton>
              <a-button type="primary">
                搜索
              </a-button>
            </template>
          </a-input-search>
        </a-config-provider>
      </div>
    </div>
    <div class="tabContent">
      <div v-if="tableList && tableList.length > 0" style="width: 100%">
        <div class="cardContent">
          <div class="card_total">
            <template v-for="(item, index) in tableList" :key="index">
              <div :class="[
                'card_content',
                {
                  cardActive: cardActive == index,
                  rightActive: index % 2 != 0,
                  cardObvious: index < 2 && tableList.length < 3,
                  bottomLine:
                    (index == tableList.length - 1 ||
                      index == tableList.length - 2) &&
                    index > 1,
                  selectBorder: schemeSelectIds.includes(item.id),
                },
              ]" @mouseenter="contentColor(index)" @mouseleave="contentLeave" @click="proDetail(item)">
                <div style="display: flex; margin: 24px">
                  <div>
                    <div style="
                        width: 168px;
                        height: 105px;
                        text-align: center;
                        position: relative;
                      " :style="backgroundStyles()">
                      <p style="
                          font-weight: 700;
                          display: block;
                          color: #1f82c8;
                          position: absolute;
                          left: 50%;
                          top: 50%;
                          transform: translate(-50%, -50%);
                          font-size: 10px;
                        ">
                        {{ item.name }}
                      </p>
                    </div>
                  </div>
                  <div class="card_center">
                    <div class="card_text">
                      <div class="card_tag">
                        <div class="card_title">{{ item.name }}</div>
                        <span class="cardTag" v-if="item.industryName || item.categoryName || item.abilityType"
                          style="background-color: #d7e6ff; color: #2e7fff">{{ item.industryName ||
                            item.categoryName || item.abilityType
                          }}</span>
                        <span class="cityStyle" v-if="item.provider">{{
                          item.provider
                          }}</span>
                      </div>
                    </div>
                    <div class="card_des">
                      {{ item.abilityIntro || item.description || item.summary }}
                    </div>
                    <div style="
                          display: flex;
                          align-items: center;
                          justify-content: space-between;
                        ">
                      <div style="
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                          "></div>
                    </div>
                    <div style="
                          display: flex;
                          align-items: center;
                          justify-content: space-between;
                        ">
                      <div>
                      </div>
                      <div style="display: flex; align-items: center">
                        <img src="@/assets/images/home/<USER>" style="width: 16px; height: 16px" />
                        <span style="font-size: 12px; color: rgba(0, 0, 0, 0.45)">{{
                          item.viewCount || 0 }}</span>
                        <img src="@/assets/images/home/<USER>"
                          style="width: 16px; height: 16px; margin-left: 18px" />
                        <span style="font-size: 12px; color: rgba(0, 0, 0, 0.45)">{{
                          item.downloadCount || 0 }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>
        <div class="layPage">
          <a-pagination v-model:pageSize="pageItemSize" v-model:current="currentPage" :pageSizeOptions="pageSizeOptions"
            show-quick-jumper show-size-changer :total="totalItemCount" @change="pageChange"
            @showSizeChange="sizeChange" class="mypage" />
        </div>
        <div class="btn_box">
          <span></span>
          <div>
            <span class="refuse" @click="refuse">取消</span>
            <span class="submit" @click="submit">确定</span>
          </div>
        </div>
      </div>
      <div v-if="tableList && tableList.length == 0" class="emptyPhoto">
        <img src="@/assets/images/home/<USER>" />
      </div>
      <div class="loading" v-show="loadingShow">
        <a-spin />
      </div>
    </div>
  </div>
</template>
<script>
import { defineComponent, reactive, toRefs, computed } from "vue";
import { message } from "ant-design-vue";
import { getProgrammeList } from "@/api/solutionNew/home";
import { getNewSceneSchemeList, getProjectList } from "@/api/moduleList/home";
import zhCN from "ant-design-vue/es/locale/zh_CN";
import bac from "@/assets/images/noDataBac.png";
import eventBus from "@/utils/eventBus";
export default defineComponent({
  components: {},
  props: {
    sourceType: {
      type: String,
      default: "1",//1解决方案2场景方案3原子能力
    },
  },
  setup(props, { emit }) {
    const businessModuleTypeComputed = computed(() => {
      return props.sourceType == 1 ? '方案' : props.sourceType == 2 ? '场景方案' : '能力'
    });
    const data = reactive({
      backgroundImage: bac,
      selectItem: {},
      searchName: "",
      loadingShow: true,
      cardActive: "-1",
      pageSizeOptions: ["10", "20", "30", "50"],
      totalItemCount: 0,
      tableList: [],
      currentPage: 1,
      pageItemSize: 10,
      schemeSelectIds: [],
    });
    const getList = () => {
      let pageParams = {
        pageNo: data.currentPage,
        pageSize: data.pageItemSize,
        keyword: data.searchName,
      };
      if (props.sourceType === "1") {
        //解决方案
        getProgrammeList(pageParams).then((res) => {
          data.loadingShow = false;
          data.tableList = [];
          data.tableList = res.data.rows;
          data.totalItemCount = res.data.totalRows;
          data.tableList.map((item) => {
            item.label = item.label.split(",");
          });
        }).catch((error) => {
          data.loadingShow = false;
        });
      } else if (props.sourceType === "2") {
        // 场景方案
        getNewSceneSchemeList(pageParams).then((res) => {
          data.loadingShow = false;
          data.tableList = [];
          data.tableList = res.data.rows;
          data.totalItemCount = res.data.totalRows;
          data.tableList.map((item) => {
            item.label = item.label.split(",");
          });
        }).catch((error) => {
          data.loadingShow = false;
        });
      } else if (props.sourceType === "3") {
        //能力
        getProjectList(pageParams).then((res) => {
          data.loadingShow = false;
          data.tableList = [];
          data.tableList = res.data.rows;
          data.totalItemCount = res.data.totalRows;
          data.tableList.map((item) => {
            item.label = item.label.split(",");
          });
        }).catch((error) => {
          data.loadingShow = false;
        });
      }
      data.loadingShow = true;
    };
    eventBus.on("moduleRefresh", getList);
    getList();
    const seekContent = () => {
      getList();
    };
    const contentColor = (index) => {
      data.cardActive = index;
    };
    const proDetail = (item) => {
      data.selectItem = item;
      let isHave = false;
      for (let i in data.schemeSelectIds) {
        if (data.schemeSelectIds[i] == item.id) {
          isHave = true;
          break;
        }
      }
      if (!isHave) {
        data.schemeSelectIds = [item.id];
      } else {
        data.schemeSelectIds = [];
      }
    };
    const contentLeave = () => {
      data.cardActive = "-1";
    };
    const refuse = () => {
      data.schemeSelectIds = [];
      emit("close");
    };
    //确定
    const submit = () => {
      data.searchName = "";
      if (data.schemeSelectIds.length == 0) {
        message.warning(businessModuleTypeComputed.value + "必选一条");
        return false;
      }
      emit("ok", data.selectItem);
      return false;
    };
    const pageChange = (page, pageSize) => {
      data.currentPage = page;
      getList();
    };
    const sizeChange = (current, size) => {
      data.pageItemSize = size;
      getList();
    };
    const backgroundStyles = () => {
      return {
        backgroundImage: `url(${data.backgroundImage})`, // 使用模板字符串来插入变量
        backgroundRepeat: "no-repeat",
        backgroundSize: "cover",
      };
    };
    return {
      ...toRefs(data),
      businessModuleTypeComputed,
      zhCN,
      backgroundStyles,
      contentColor,
      sizeChange,
      contentLeave,
      proDetail,
      pageChange,
      refuse,
      seekContent,
      submit,
    };
  },
});
</script>
<style lang="scss" scoped src="./guideTable.scss"></style>
<style lang="scss">
.mypage {
  .ant-pagination {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-select-selector {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-pagination-item-active {
    background: #007eff;
  }

  .ant-pagination-item-active a {
    color: #ffffff;
  }

  .ant-pagination-item-active:focus a,
  .ant-pagination-item-active:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-active a:focus,
  .ant-pagination-item-active a:hover {
    color: #ffffff;
  }

  .ant-pagination-item:focus,
  .ant-pagination-item:hover {
    background: #007eff;
  }

  .ant-pagination-item:focus a,
  .ant-pagination-item:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-disabled:hover,
  .ant-pagination-item-disabled:focus {
    background-color: #ffffff;
    border-color: #d9d9d9;

    a {
      color: rgba(0, 0, 0, 0.25);
    }
  }

  .ant-pagination-options-size-changer.ant-select {
    margin-right: 0;
  }

  .ant-pagination-options-quick-jumper {
    margin-left: 8px;
  }
}

/*.shopping {
  position: absolute;
  right: 4px;
  bottom: 12px;
}*/
</style>