<template>
  <div :class="['message-container', isUser ? 'user-message' : 'ai-message']">
    <!-- 用户消息，头像在右边 -->
    <div v-if="isUser" class="user-message">
      <p>
        <span>{{ text }}</span>
        <img class="avatar margin_l_24" src=" @/assets/images/AI/user.png" alt="用户头像" />
      </p>
    </div>
    <!-- AI消息，头像在左边 -->
    <div v-else class="ai-message flex">
      <p v-if="!isLoading">
        <!-- <img class="avatar" src="" alt="AI头像" /> -->
        <img style="width: 40px;height: 40px;margin-right: 24px;" src="../../../assets/images/AI/ai.png" alt="">
      <div style="width:92%">
        <div class="flex align-center margin_b_16" style="display: inline-block;">{{ tips }}<span
            v-if="!isTyping">...</span></div>
        <div class="projectConten" v-if="isTyping">
          <div class="list" style="margin-bottom: 24rpx;width:100%;">
            <div class="con ">
              <!-- 方案 -->
              <div v-if="dataType == '2'" class="card flex left" v-for="(item, key) in modelList"
                @click="jump(item.id, 'project')" :key="key">
                <img v-if="item.logo == '' || item.logo == null"
                  src="https://smart.jsisi.cn:8099/portal/resource/2024/09/22/6e4d3993-d2dc-4e5a-bb9f-2ce05edffae2.png"
                  alt="" />
                <img v-else :src="`${item.logo}`" alt="" />
                <div style="flex: 1" class="right">
                  <div class="name desc" style="margin-bottom: 8rpx">{{
                    item.name
                  }}</div>
                  <a-button size="small" type="link" class="joinBtn" @click.stop="jionProject(item.id)"
                    :disabled="item.btnText == '已加入' ? true : false">
                    <img class="add" src=" @/assets/images/AI/isadded.png" v-if="item.btnText == '加入预选'" />
                    {{ item.btnText }}</a-button>
                </div>
              </div>
              <!-- 模块 -->
              <div v-if="dataType == '3'" class="card flex left" v-for="(item, key) in modelList"
                @click="jump(item.id, 'model')" :key="key">
                <img v-if="item.abilityPicture == '' || item.abilityPicture == null"
                  src="https://smart.jsisi.cn:8099/portal/resource/2024/09/22/6e4d3993-d2dc-4e5a-bb9f-2ce05edffae2.png"
                  alt="" />
                <img v-else :src="`${item.abilityPicture}`" alt="" />
                <div style="flex: 1" class="right">
                  <a-popover>
                    <template #content>
                      <span>{{
                        item.name
                      }}</span>
                    </template>
                    <div class="name desc pointer" style="margin-bottom: 8rpx">{{
                      item.name
                    }}</div>
                  </a-popover>

                  <a-button size="small" type="link" class="joinBtn" @click.stop="jionModel(item.id)"
                    :disabled="item.btnText == '已加入' ? true : false">
                    <img class="add" v-if="item.btnText == '加入预选'" src=" @/assets/images/AI/isadded.png" />{{
                      item.btnText }}</a-button>
                </div>
                <div class="identification">
                  <img v-if="item.specialTitle == '能力'" src="../../../assets/images/AI/moduleTip.png" alt="">
                </div>
              </div>
              <!-- 方案+能力 -->
              <div class="newCard" v-if="dataType == '4' && modelList.length !== 0">
                <div class="eachLine" style="white-space: nowrap;" v-for="(item, key) in modelList" :key="key">
                  <div class="margin_b_8" v-if="canTitleShow(item)">{{ item.title }}：</div>
                  <div class="flex" style="flex-wrap: wrap;">
                    <div class="eachNewCard margin_b_16 margin_r_16 pointer" style="min-width: 288px;"
                      v-for="(lineItem, lineKey) in item.moduleResVos" :key="lineKey"
                      @click="groupJump(lineItem.id, 'model', lineItem.specialTitle)">
                      <div class="flex eachContent">
                        <div class="imgContainer">
                          <img class="imgItem" v-if="lineItem.abilityPicture == '' || lineItem.abilityPicture == null"
                            src="https://smart.jsisi.cn:8099/portal/resource/2024/09/22/6e4d3993-d2dc-4e5a-bb9f-2ce05edffae2.png"
                            alt="" />
                          <img class="imgItem" v-else :src="`${lineItem.abilityPicture}`" alt="" />
                        </div>

                        <div style="flex: 1" class="right">
                          <a-tooltip>
                            <template #title>
                              <span>{{ lineItem.name }}</span>
                            </template>
                            <div class="name desc textEllipsis2" style="margin-bottom: 8rpx">{{ lineItem.name }}</div>
                          </a-tooltip>
                          <a-tooltip>
                            <template #title>{{ lineItem.description }}</template>
                            <div class="margin_t_6 ellipsis-text" v-if="lineItem.specialTitle === '方案'">
                              {{ lineItem.description }}
                            </div>
                          </a-tooltip>
                          <a-tooltip>
                            <template #title>{{ lineItem.summary }}</template>
                            <div class="margin_t_6 ellipsis-text" v-if="lineItem.specialTitle === '应用场景'">
                              {{ lineItem.summary }}
                            </div>
                          </a-tooltip>
                          <a-tooltip>
                            <template #title>{{ lineItem.abilityIntro }}</template>
                            <div class="margin_t_6 ellipsis-text" v-if="lineItem.specialTitle === '能力'">
                              {{ lineItem.abilityIntro }}
                            </div>
                          </a-tooltip>
                          <a-tooltip>
                            <template #title>{{ lineItem.introduction }}</template>
                            <div class="margin_t_6 ellipsis-text" v-if="lineItem.specialTitle === '产品'">
                              {{ lineItem.introduction }}
                            </div>
                          </a-tooltip>

                          <div class="flex" style="position: relative; margin-left: -10px;margin-top: 5px;">
                            <a-button size="small" type="link" class=""
                              @click.stop="jionGroup(lineItem.id, key, lineItem.specialTitle)"
                              :disabled="lineItem.btnText == '已加入' ? true : false">
                              <img class="add" v-if="lineItem.btnText == '加入预选'"
                                src=" @/assets/images/AI/isadded.png" />{{
                                  lineItem.btnText
                                }}</a-button>
                            <a-button v-if="canDelete(item, lineItem)" size="small" type="link" class="deleteBtn"
                              @click.stop="deleteThisCard(key, lineKey)">
                              <img class="add" src="@/assets/images/AI/deleteCard.png" /></a-button>
                          </div>
                        </div>
                      </div>
                      <div class="identification">
                        <img v-if="lineItem.specialTitle == '产品'" src="../../../assets/images/AI/productTip.png" alt="">
                        <img v-if="lineItem.specialTitle == '能力'" src="../../../assets/images/AI/moduleTip.png" alt="">
                        <img v-if="lineItem.specialTitle == '应用场景'" src="../../../assets/images/AI/sceneTip.png" alt="">
                      </div>
                    </div>
                  </div>
                </div>
                <div>
                </div>
                <div class="maxWidth flex">
                  <div class="customized pointer flex align-center" @click="addCustomized()">
                    <img src="../../../assets/images/AI/Group <EMAIL>" alt="">
                    <div>
                      加入定制
                    </div>
                  </div>
                  <!-- <div class="customized pointer flex align-center margin_l_40" @click="addCustomized()">
                    <img src="../../../assets/images/AI/Group <EMAIL>" alt="">
                    <div>
                      重新推荐
                    </div>
                  </div> -->
                </div>

              </div>
              <!-- 场景 -->
              <div v-if="dataType == '5'" class="card flex left" v-for="(item, key) in modelList"
                @click="jump(item.id, 'productBag')" :key="key">
                <img v-if="item.mainImg == ''"
                  src="https://smart.jsisi.cn:8099/portal/resource/2024/09/22/6e4d3993-d2dc-4e5a-bb9f-2ce05edffae2.png"
                  alt="" />
                <img v-else :src="`${item.mainImg}`" alt="" />
                <div style="flex: 1" class="right">
                  <a-popover>
                    <template #content>
                      <span>{{
                        item.name
                      }}</span>
                    </template>
                    <div class="name desc pointer" style="margin-bottom: 8rpx">{{
                      item.name
                    }}</div>
                  </a-popover>

                  <a-button size="small" type="link" class="joinBtn" @click.stop="jionProductBag(item.id)"
                    :disabled="item.btnText == '已加入' ? true : false">
                    <img class="add" v-if="item.btnText == '加入预选'" src=" @/assets/images/AI/isadded.png" />{{
                      item.btnText }}</a-button>
                </div>
              </div>
              <!-- 产品 -->
              <div v-if="dataType == '6'" class="card flex left" v-for="(item, key) in modelList"
                @click="jump(item.id, 'product')" :key="key">
                <img v-if="item.image == ''"
                  src="https://smart.jsisi.cn:8099/portal/resource/2024/09/22/6e4d3993-d2dc-4e5a-bb9f-2ce05edffae2.png"
                  alt="" />
                <img v-else :src="`${item.image}`" alt="" />
                <div style="flex: 1" class="right">
                  <a-popover>
                    <template #content>
                      <span>{{
                        item.name
                      }}</span>
                    </template>
                    <div class="name desc pointer" style="margin-bottom: 8rpx">{{
                      item.name
                    }}</div>
                  </a-popover>

                  <a-button size="small" type="link" class="joinBtn" @click.stop="jionProduct(item.id)"
                    :disabled="item.btnText == '已加入' ? true : false">
                    <img class="add" v-if="item.btnText == '加入预选'" src=" @/assets/images/AI/isadded.png" />{{
                      item.btnText }}</a-button>
                </div>
                <div class="identification">
                  <img v-if="item.specialTitle == '产品'" src="../../../assets/images/AI/productTip.png" alt="">
                </div>
              </div>
              <!-- 产品+场景 -->
              <div v-if="dataType == '7'">
                <div v-if="modelList.length !== 0" class="margin_b_10">{{ productBagName }}</div>
                <div class="card flex left" v-for="(item, key) in modelList" @click="jump(item.id, 'product')"
                  :key="key">
                  <img v-if="item.image == ''"
                    src="https://smart.jsisi.cn:8099/portal/resource/2024/09/22/6e4d3993-d2dc-4e5a-bb9f-2ce05edffae2.png"
                    alt="" />
                  <img v-else :src="`${item.image}`" alt="" />
                  <div style="flex: 1" class="right">
                    <a-popover>
                      <template #content>
                        <span>{{
                          item.name
                        }}</span>
                      </template>
                      <div class="name desc pointer" style="margin-bottom: 8rpx">{{
                        item.name
                      }}</div>
                    </a-popover>

                    <a-button size="small" type="link" class="joinBtn" @click.stop="jionProduct(item.id)"
                      :disabled="item.btnText == '已加入' ? true : false">
                      <img class="add" v-if="item.btnText == '加入预选'" src=" @/assets/images/AI/isadded.png" />{{
                        item.btnText }}</a-button>
                  </div>
                  <div class="identification">
                    <img v-if="item.specialTitle == '产品'" src="../../../assets/images/AI/productTip.png" alt="">
                  </div>
                </div>
                <div class="maxWidth flex" v-if="modelList.length > 0">
                  <div class="customized pointer flex align-center" @click="addProduct()">
                    <img class="margin_r_12" src="../../../assets/images/AI/Group <EMAIL>" alt="">
                    <div>
                      加入定制
                    </div>
                  </div>
                  <!-- <div class="customized pointer flex align-center margin_l_40" @click="addCustomized()">
                    <img src="../../../assets/images/AI/Group <EMAIL>" alt="">
                    <div>
                      重新推荐
                    </div>
                  </div> -->
                </div>
              </div>


            </div>
          </div>
        </div>
      </div>
      </p>
      <!-- 显示完成的消息 -->
      <!-- <div v-else class="loading-spinner"></div> -->
      <!-- 显示加载动画 -->
    </div>
    <a-modal v-model:visible="visible" title="提示" @ok="handleOk">
      <p>已存在定制组合，请确认是否替换已有组合?</p>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, watch } from "vue";
import { useRouter } from "vue-router";
import eventBus from "@/utils/eventBus";
import { addShoppingCart } from "@/api/combine/shoppingCart.js";
import { toCombinePage } from "@/api/combine/shoppingCart.js";
import { myCombineList } from "@/api/combine/combine.js";
import { addShop } from "@/api/buyList/index.js";
import { fa, tr } from "element-plus/es/locale/index.mjs";
import { dataType } from "element-plus/es/components/table-v2/src/common.mjs";
import { toShopList } from "@/api/buyList/index";
export default defineComponent({
  emits: ['duplicated', 'productDuplicated'],
  props: {
    text: {
      type: String,
      required: true,
    },
    isUser: {
      type: Boolean,
      required: true,
    },
    isTyping: {
      type: Boolean,
      default: false,
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
    modelList: {
      type: Array,
      default: [],
    },
    dataType: {
      type: String,
      default: "",
    },
    tips: {
      type: String,
      default: "",
    },
    messageTips: {
      type: String,
      default: ''
    },
    productBagName: {
      type: String,
      default: ''
    },
    isShowTitle: {
    	type: Boolean,
      default: false,
    }
  },
  setup(props, { emit }) {
    const router = useRouter();
    const data = {
      tips: props.tips,
      modelList: props.modelList,
      aiAvatar: null,
      userAvatar: null,
      customizeArr: [],
      addProductArr: [],
      productBagName: props.productBagName,
      solutionList: [],
      sceneList: [],
      moduleList: [],
      productList: [],
    };
    //点击按钮， 将方案加入购物车
    const jionProject = (id) => {
      // data.modelList.forEach((el) => {
      //   el.btnText = "加入预选";
      // });
      data.modelList.find((item) => item.id == id).btnText = "已加入";
      addShoppingCart({
        schemeId: id,
        type: 1,
      }).then((res) => {
        eventBus.emit("cartRefresh");
      });
    };

    //点击按钮  将模块加入购物车
    const jionModel = (id, key, specialTitle) => {
      data.modelList.find((item) => item.id == id).btnText = "已加入";
      addShoppingCart({
        schemeId: id,
        type: 2,
      }).then((res) => {
        eventBus.emit("cartRefresh");
      });
    };
    //点击按钮  将产品加入购物车
    const jionProduct = (id) => {
      data.modelList.find((item) => item.id == id).btnText = "已加入";
      console.log('id', id)
      addShop({
        productId: id,
        type: "2"
      }).then((res) => {
        eventBus.emit("cartRefresh");
      })
    }
    //点击按钮  将场景加入购物车
    const jionProductBag = (id) => {
      data.modelList.find((item) => item.id == id).btnText = "已加入";
      console.log('id', id)
      addShop({
        productId: id,
        type: "1"
      }).then((res) => {
        eventBus.emit("cartRefresh");
      })
    }
    const jionGroup = (id, key, specialTitle) => {
      console.log('specialTitle', specialTitle)
      for (let i of data.modelList[key].moduleResVos) {
        if (i.id == id) {
          i.btnText = '已加入'
        }
      }
      if (specialTitle == '方案') {
        addShoppingCart({
          schemeId: id,
          type: 1,
        }).then((res) => {
          eventBus.emit("cartRefresh");
        });
      } else if (specialTitle == '能力') {
        addShoppingCart({
          schemeId: id,
          type: 2,
        }).then((res) => {
          eventBus.emit("cartRefresh");
        });
      } else if (specialTitle == '产品') {
        addShop({
          productId: id,
          type: "2"
        }).then((res) => {
          eventBus.emit("cartRefresh");
        })
      } else if (specialTitle == '场景') {
        addShop({
          productId: id,
          type: "1"
        }).then((res) => {
          eventBus.emit("cartRefresh");
        })
      }
    }

    const jump = (id, type) => {
      if (type === "project") {
        //跳转到方案详情
        router.push({ name: "solveDetailNew", query: { id: id } });
      } else if (type === "model") {
        //跳转到模块详情
        router.push({ name: "modulelNew", query: { id: id } });
      } else if (type === "productBag") {
        //跳转到场景详情
        router.push({ name: "scenarioDetail", query: { id: id } });
      } else if (type === "product") {
        //跳转到产品详情
        router.push({ name: "productDetail", query: { id: id } });
      }
    };
    const groupJump = (id, type, specialTitle) => {
      if (specialTitle === "方案") {
        //跳转到方案详情
        router.push({ name: "solveDetailNew", query: { id: id } });
      } else if (specialTitle === "能力") {
        //跳转到模块详情
        router.push({ name: "modulelNew", query: { id: id } });
      } else if (specialTitle === "场景") {
        //跳转到场景详情
        router.push({ name: "scenarioDetail", query: { id: id } });
      } else if (specialTitle === "产品") {
        //跳转到产品详情
        router.push({ name: "productDetail", query: { id: id } });
      } else if (specialTitle === "应用场景") {
        //跳转到产品详情
        // router.push({ name: "productDetail", query: { id: id } });
      }
    }
    // 删除组合里的当前卡片
    const deleteThisCard = (key, lineKey) => {
      data.modelList[key].moduleResVos.splice(lineKey, 1)
    }
    // 方案能力加入定制
    const addCustomized = () => {
      // console.log('2222222222222', data.modelList)
      data.customizeArr = []
      for (let i of data.modelList) {
        if (i.title == '标准方案' && i.moduleResVos) {
          for (let j of i.moduleResVos) {
            data.customizeArr.push({
              schemeId: j.id,
              type: 1
            })
            for (let z of j.moduleBody) {
              if (z.type !== 5) {
                for (let y of z.moduleList) {
                  data.customizeArr.push({
                    schemeId: y.id,
                    type: 3
                  })
                }
              }
            }
          }
        }
        if (i.title == '应用场景' && i.moduleResVos) {
          for (let j of i.moduleResVos) {
            if (j.specialTitle == '能力') {
              data.customizeArr.push({
                schemeId: j.id,
                type: 2
              })
            }
            if (j.specialTitle == '应用场景') {
              data.customizeArr.push({
                schemeId: j.id,
                type: 3
              })
            }
            // if (j.specialTitle == '产品') {
            //   data.customizeArr.push({
            //     schemeId: j.id,
            //     type: 4
            //   })
            // }
          }
        }
      }
      console.log('data.customizeArr', data.customizeArr)
      if (data.customizeArr.length != 0) {
        myCombineList()
          .then((res) => {
            // debugger
            // 提示
            if (
              res.data.list.some((item) => item.list && item.list.length > 0)
            ) {
              console.log("fndifsn")
              emit("duplicated", data.customizeArr)
            } else {
              toCombinePage({ list: data.customizeArr, source: '3' }).then((res) => {
                data.visible = false;
                if(!props.isShowTitle){
                	router.push({
	                  // name: "customizedList",
	                  path: "/customized/customizedList?",
	                  query: { active: "定制" },
	                });
	                eventBus.emit("urlRefresh");
                } else {
                	emit("close");
                }
              })
            }
          })
          .catch((error) => { });
      } else {
        message.warning("请选择数据进行组合,方案必选一个");
        return;
      }

    }
    // 场景加入定制
    const addProduct = () => {
      data.addProductArr = []
      for (let i of data.modelList) {
        data.addProductArr.push({
          productId: i.id,
          type: 2
        })
      }
      console.log('data.addProductArr', data.addProductArr)
      emit("productDuplicated", data.addProductArr)
    }
    // 监听 modelList 的变化，打印或处理变化
    watch(
      () => props.modelList,
      (newVal, oldVal) => {
        // 这里可以做一些其他的处理，例如重新渲染或处理逻辑
        data.modelList = newVal;
        console.log('modelList', data.modelList)

        props.modelList.map((item) => {
          return { ...item, btnText: item.addCart ? "已加入" : "加入预选" };
        });
        // props.modelList.map((item) => {
        //   return { ...item, btnText: item.addOrder == 1 ? "已加入" : "加入预选" };
        // });
        console.log('props.modelList', props.modelList)

      },
      { immediate: true, deep: true }
    );
    watch(
      () => props.tips,
      (newV) => {

        data.tips = newV
        // console.log('data.tips', data.tips);

      }
    )
    const canDelete = (item, lineItem) => {
      data.solutionList = []
      data.sceneList = []
      data.moduleList = []
      data.productList = []
      // let productBagList = []
      for (let i of data.modelList) {
        if (i.title == '标准方案') {
          data.solutionList = i.moduleResVos ? i.moduleResVos : []
        }
        if (i.title == '应用场景') {
          for (let j of i.moduleResVos) {
            if (j.specialTitle == '应用场景') {
              data.sceneList.push(j)
            } else if (j.specialTitle == '能力') {
              data.moduleList.push(j)
            } else if (j.specialTitle == '产品') {
              data.productList.push(j)
            }
          }
        }
      }
      if (lineItem.specialTitle == '方案') {
        if (data.solutionList.length > 1) {
          return true
        } else {
          return false
        }
      }
      if (lineItem.specialTitle == '应用场景') {
        console.log('222222222222222', data.sceneList)

        if (data.sceneList.length > 1) {
          return true
        } else {
          return false
        }
      }
      if (lineItem.specialTitle == '能力') {

        for (let i of data.solutionList) {
          if (i.id == lineItem.solutionId) {
            return false

          } else {
            return true
          }
        }
      }
      if (lineItem.specialTitle == '产品') {
        if (data.productList.length > 1) {
          return true

        } else {
          return false
        }
      }


      // if (lineItem.specialTtile == '场景') {
      //   if (productBagList.length > 1) {
      //     return true
      //   } else {
      //     return false
      //   }
      // }
      // console.log('111111111111111', solutionList)
      // console.log('222222222222222', sceneList)
      // console.log('333333333333333', moduleList)

    }
    const canTitleShow = (item) => {
      if (item.moduleResVos) {
        if (item.moduleResVos.length == 0) {
          return false
        } else {
          return true
        }
      } else {
        return false
      }
    }
    const showAdd = () => {
      let arr = []
      for (let i of data.modelList) {
        if (i.title == '应用场景') {
          for (let j of i.moduleResVos) {
            if (j.specialTitle == '产品') {
              arr.push(j)
            }
          }
        }
      }
      console.log('arrrrrrrrrrrrr', arr)
      if (arr.length === 0) {
        return true
      } else {
        return false
      }
    }
    return {
      ...toRefs(data),
      jump,
      jionProject,
      router,
      jionModel,
      deleteThisCard,
      addCustomized,
      canDelete,
      canTitleShow,
      jionProduct,
      jionProductBag,
      jionGroup,
      groupJump,
      addProduct,
      showAdd,
    };
  },
});
</script>

<style lang="scss" scoped>
.message-container {
  display: flex;
  align-items: center;
  margin: 10px 0;
}

/* 用户消息的样式，头像在右边 */
.user-message {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;

  p {
    margin-right: 10px;
    background-color: #EBF0FF;
    box-shadow: 0px 8px 24px 0px rgba(116, 157, 219, 0.24);
    display: flex;
    align-items: center;

    padding: 24px span {
      align-items: center;
      height: 100%;
    }
  }
}


.user-message p {
  color: rgba(0, 0, 0, 0.85);
  padding: 20px 20px 20px 40px;
  border-radius: 10px;
  max-width: 75%;
  margin-left: 10px;
}

.right {
  position: relative;

  span {
    position: absolute;
    bottom: 0;
  }
}

/* AI消息的样式，头像在左边 */
.ai-message {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;

  p {
    display: flex;
    // align-items: center;
    padding: 24px;
  }

  .avatar {
    margin-right: 8px;
  }
}

.ai-message p {
  background-color: #fff;
  color: black;
  padding: 24px;
  border-radius: 10px;
  max-width: 75%;
  margin-right: 10px;
  border-radius: 0px 30px 30px 30px;
  box-shadow: 0px 8px 24px 0px rgba(116, 157, 219, 0.24);
}

/* 头像样式 */
.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.avatar {
  font-size: 20px;
  color: #007bff;
  text-align: center;
}

/* 打字状态的样式 */
.typing {
  font-style: italic;
  color: #aaa;
}

/* 打字状态的样式 */
.typing {
  font-style: italic;
  color: #aaa;
}

/* 加载动画 */
.loading-spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: #007bff;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
  margin-top: -20px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.projectConten {
  display: flex;
  /*max-width: 930px;*/

  .card {
    display: flex;
    position: relative;
    padding: 24px;
    // background-color: #fff;
    background-image: url(../../../assets/images/AI/groupBg.png);
    background-repeat: no-repeat;
    background-size: cover;
    margin-right: 10px;
    width: 288px;
    max-height: 120px;
    // overflow: hidden;
    margin-right: 16px;
    margin-bottom: 16px;

    img {
      width: 88px;
      height: 72px;
      margin-right: 12px;
    }

    .desc {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      word-break: break-all;
    }

    .name {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 1;
      line-clamp: 2;
      word-break: break-all;
    }

    .add {
      width: 16px;
      height: 16px;
      margin-bottom: 3px;
      margin-right: 6px;
    }

    .joinBtn {
      position: absolute;
      padding: 0;
      bottom: 0;
      // left: 114px;
    }

    .identification {
      width: 56px;
      height: 30px;
      position: absolute;
      top: 6px;
      left: -8px;
      // background-image: url(../../../assets/images/AI/blueTip.png);

      img {
        width: 100%;
        height: 100%;
      }
    }
  }

  .newCard {
    // display: flex;
    // position: relative;
    // padding: 24px;
    // background-color: #fff;
    margin-right: 10px;
    margin-top: 24px;
    max-width: 930px;
    min-height: 140px;
    // max-height: 120px;

    .customized {
      clear: both;
      font-weight: bold;
      color: #007bff;

      img {
        width: 14px;
        height: 14px;
      }
    }


    .eachNewCard {
      width: 288px;
      height: 110px;
      margin-right: 16px;
      margin-bottom: 30px;
      position: relative;

      .eachContent {
        padding: 24px 24px 24px 24px;
        // box-shadow: 0px 0px 24px 0px rgba(116, 157, 219, 0.3);
        background-image: url(../../../assets/images/AI/groupBg.png);
        background-repeat: no-repeat;
        background-size: cover;
        border-radius: 10px;

        .imgContainer {
          width: 83px;
          height: 72px;
          margin-right: 8px;

          .imgItem {
            width: 100%;
            height: 100%;
          }
        }
      }

      .identification {
        width: 56px;
        height: 30px;
        position: absolute;
        top: 6px;
        left: -8px;
        // background-image: url(../../../assets/images/AI/blueTip.png);

        img {
          width: 100%;
          height: 100%;
        }
      }
    }

    img {
      width: 88px;
      height: 72px;
      margin-right: 12px;
    }

    .desc {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      word-break: break-all;
    }

    .name {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 1;
      line-clamp: 2;
      word-break: break-all;
    }

    .add {
      width: 16px;
      height: 16px;
      margin-bottom: 3px;
      margin-right: 6px;
    }

    .joinBtn {
      // position: absolute;
      // padding: 0;
      // bottom: 0;
      // left: 114px;
    }
  }
}

.joined {
  opacity: 0.3 !important;
}

.bg_FFF {
  background-color: #fff;
}

.customized {
  clear: both;
  font-weight: bold;
  color: #007bff;

  img {
    width: 14px;
    height: 14px;
  }
}

.ellipsis-text {
  width: 160px;
  font-size: 12px;
  /* 设置容器宽度 */
  white-space: nowrap;
  /* 防止文字换行 */
  overflow: hidden;
  /* 超出部分隐藏 */
  text-overflow: ellipsis;
  /* 超出部分显示省略号 */
}

.deleteBtn {
  position: absolute;
  right: 0;
  bottom: 0;
}
</style>