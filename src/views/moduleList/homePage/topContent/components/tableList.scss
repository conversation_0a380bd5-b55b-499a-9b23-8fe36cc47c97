.AIBg {
    background-image: url("@/assets/images/AI/newAIbg.png");
}

.AIBgShengTai {
    // background: linear-gradient(135deg, #ffefef 0%, rgb(0, 179, 185) 30%, rgba(0, 179, 185, 0.596) 30%, rgba(0, 179, 185, 0.555) 100%);
    background-image: url("@/assets/images/home/<USER>");
}

.commonBg {
    background-image: url("@/assets/images/home/<USER>");
}

.searchInfo {
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 1200px;
    margin: 0 auto;
    padding: 24px 40px 24px 40px;
    transition: 1s;
    border-radius: 10px;

    .switch {
        margin-right: 16px;
        transition: 1s;

        .AIlogo {
            width: 48px;
            height: 18px;
            background-image: url(../../../../../assets/images/AI/AILogo.png);
            background-size: 100% 100%;
            margin-bottom: 5px;
        }
    }

    .vocationPull {
        display: flex;
        align-items: center;
        border-top-left-radius: 5px;
        border-bottom-left-radius: 5px;
        border-top-right-radius: 5px;
        border-bottom-right-radius: 5px;
    }

    .inputClass {
        border: none;
        width: 100%;
        height: 100%;
        box-shadow: none !important;
    }

    .line {
        width: 1px;
        height: 26px;
        background: #EFF0F4;
    }

    .lines {
        width: 1px;
        height: 56px;
        background: #EFF0F4;
    }

    .AIbtn {
        // background: linear-gradient(108deg, #4446FF 0%, #4173FF 50%, #3EB8FF 100%);
        background-image: url("@/assets/images/home/<USER>");

    }

    .AIbtnShengTai {
        background-image: url("@/assets/images/home/<USER>");
        // background: linear-gradient(135deg, rgba(0, 179, 185, 0.13) 0%, rgb(0, 179, 185) 30%, rgb(0, 182, 189) 60%, rgb(0, 179, 185) 100%);
    }

    .commonBtn {
        // background-color: #0C70EB;
        background-image: url("@/assets/images/home/<USER>");

    }

    .seekInfo {
        font-weight: bold;
        font-size: 20px;
        color: #FFFFFF;
        display: flex;
        align-items: center;
        width: 160px;
        height: 56px;
        cursor: pointer;
        transition: all 1s;

        img {
            width: 24px;
            height: 24px;
            margin-left: 20px;
            margin-right: 8px;
        }
    }

    .border_top_right_5 {
        border-top-right-radius: 5px;
    }

    .border_bottom_right_0 {
        border-bottom-right-radius: 0px;
    }

    .border_bottom_right_5 {
        border-bottom-right-radius: 5px;
    }

    .border_bottom_left_5 {
        border-bottom-left-radius: 5px;
    }

    .stage {
        margin-left: 44px;
        margin-top: 16px;
    }
}

.tabContent {
    width: 1200px;
    margin: 40px auto;
    background: #F5F7FC;
    border-radius: 10px;
    display: flex;
    align-items: flex-start;
    position: relative;
    margin-top: -11px;
    border-top-left-radius: 0;
    border-top-right-radius: 0;

    .AITips {
        width: 100%;
        height: 40px;
        margin-top: 0;
        border-radius: 40px;
        background: linear-gradient(90deg, rgba(145, 141, 254, 0.3) 0%, rgba(73, 151, 254, 0.3) 54%, rgba(73, 194, 254, 0.3) 100%);

        .words {
            font-size: 16px;
            font-weight: 500;
            background: linear-gradient(90deg, #4740FF 0%, #247EFD 55%, #40A2FF 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
    }

    .loading {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
    }

    .tabModel {
        border-right: 1px solid #DAE2F5;
        height: 850px;
        overflow: hidden auto;
        flex: none
    }

    .tabModel::-webkit-scrollbar {
        display: none;
    }

    .cardContent {
        overflow: hidden auto;
        position: relative;

    }

    .cardContent::-webkit-scrollbar {
        display: none;
    }

    .tab_text {
        font-size: 18px;
        color: #262626;
        cursor: pointer;
        height: 40px;
    }

    .activeTab {
        font-weight: bold;
        font-size: 18px;
        color: #FFFFFF;
        background-image: url("@/assets/images/home/<USER>");
        background-repeat: no-repeat;
        background-size: contain;
        width: 226px;
        height: 104px;
        line-height: 40px;
        padding-top: 32px;
    }

    .topTab {
        margin-top: 0;
        background-image: url("@/assets/images/home/<USER>");
        font-weight: bold;
        font-size: 18px;
        color: #FFFFFF;
        background-repeat: no-repeat;
        background-size: contain;
        width: 226px;
        height: 104px;
        line-height: 40px;
    }

    :deep(.ant-tag) {
        font-weight: 500;
        font-size: 12px;
        color: #2E7FFF;
        height: 20px;
        display: flex;
        align-items: center;
    }

    .card_total {
        display: flex;
        flex-wrap: wrap;
        align-items: flex-start;
        border-bottom: 1px solid #DAE2F5;
    }

    .emptyPhoto {
        margin: auto;

        img {
            width: 240px;
            height: 248px;
        }
    }

    .card_content {
        position: relative;
        width: 50%;
        height: 163px;
        border-right: 1px solid #DAE2F5;
        border-bottom: 1px solid #DAE2F5;
        cursor: pointer;

        .listTag {
            position: absolute;
            top: 10px;
            left: 14px;
            z-index: 1000;
        }
    }

    .cart-button {
        border: none;
        background-color: #f5f7fc;
        color: #40a9ff;
    }

    .cart-button[disabled] {
        cursor: not-allowed;
        opacity: 0.5;
    }

    .rightActive {
        border-right: none;
    }

    .cardActive {
        background-color: #FFFFFF;
        transition: all 0.2s;
        box-shadow: 0 0 10px #DAE2F5;
    }

    .bottomLine {
        border-bottom: none;
    }

    .cardObvious {
        border-bottom: none;
    }

    .card_center {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 117px;
        margin-left: 12px;
        flex: 1;
    }

    .card_text {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .card_tag {
            display: flex;
            align-items: center;
        }

        .card_title {
            font-weight: bold;
            font-size: 16px;
            color: rgba(0, 0, 0, 0.85);
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
        }

        :deep(.cityStyle) {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.45);
            background-color: transparent;
        }
    }

    .card_des {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-right: 4px;
        word-break: break-all;
    }

    .layPage {
        width: 100%;
        text-align: center;
        margin: 15px 0;
    }
}

.add-icon {
    width: 16px;
}

:deep(.ant-image-img) {
    height: 117px !important;
}

.selectData {
    width: 1200px;
    margin: 16px auto 26px;
    position: relative;
    margin-bottom: 0;

    .showMore {
        max-height: 400px !important;
    }

    .showHidden {
        overflow: auto;
    }

    .selcet_box {
        border-top: 1px solid #DAE2F5;
        border-bottom: 1px solid #DAE2F5;
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        line-height: 16px;
        display: flex;
        max-height: 49px;
        overflow: hidden;
        position: relative;
        line-height: 1.5em;
        transition: max-height 0.3s ease-out;

        /* 动画效果 */
        .left_select {
            font-weight: 500;
            color: rgba(0, 0, 0, 0.85);
            background: #F5F7FC;
            padding: 11px 16px;
            min-width: 120px;
        }

        .right_select {
            // padding: 11px 16px;
            display: flex;
            flex-wrap: wrap;
            align-items: flex-start;
            background-color: #FFFFFF;
            padding-left: 12px;
            width: 100%;

            .title {
                width: 135px;
                line-height: 49px;
                cursor: pointer;
                text-align: center;
                overflow: hidden;
                /* 确保超出容器的内容被裁剪 */
                white-space: nowrap;
                /* 确保文本在一行内显示 */
                text-overflow: ellipsis;
            }

            span {
                cursor: pointer;
                text-align: center;
            }

            .activeBtn {
                background: #F3F8FF;
                font-weight: 500;
                font-size: 14px;
                color: #2E7FFF;
                height: 49px;
            }
        }
    }

    .more {
        background: #ECF4FE;
        padding: 5px 12px;
        // font-weight: 400;
        // font-size: 14px;
        color: #0C70EB;
        line-height: 22px;
        width: 85px;
        align-items: center;
        cursor: pointer;

        img {
            width: 8px;
            height: 4px;
            margin-left: 4px;
        }
    }

    .select_boot {
        margin: 11px 0;
        padding-left: 12px;
        justify-content: space-between;
        padding: 5px;
        background-color: #FFFFFF;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        padding-left: 20px;
        padding-right: 20px;

        .right_con {
            color: #9A9A9A;

            span {
                color: #333333;
                margin: 0 6px;
                font-weight: 500;
            }
        }
    }

    .label {
        margin-left: 24px;
    }

    .right_btn {
        width: 70px;
        height: 32px;
        justify-content: center;
        align-items: center;
        background: rgba(12, 112, 235, 0.08);
        font-weight: 500;
        font-size: 16px;
        color: #0C70EB;
        line-height: 22px;
        margin-top: 6px;
        cursor: pointer;
    }

    .last_data {
        display: flex;
        min-width: 905px;
        color: rgba(0, 0, 0, 0.65);
        margin-top: 14px;
        background-color: #F3F8FF;
    }

    .ant-input {
        width: 100%;
        height: 100%;
        border: none;
    }

    .ant-input:focus {
        border: none;
        box-shadow: none;
    }

    .ant-input-affix-wrapper {
        width: 100%;
        height: 100%;
        border: none;
        text-align: left !important;
    }

    .ant-input-affix-wrapper-focused {
        border: none;
        box-shadow: none;
    }

    .searchList {
        color: rgba(0, 0, 0, 0.85);
        position: absolute;
        width: 1080px;
        max-height: 160px;
        background: #fff;
        top: 44px;
        left: 120px;
        padding-bottom: 1px;
        padding-left: 24px;
        border-radius: 0 0 5px 5px;
        box-shadow: 0px 4px 8px 0px rgba(116, 157, 219, 0.3);
        z-index: 2;
        overflow: auto;

        p {
            padding: 4px 0;
            margin-bottom: 0;
            cursor: pointer;
        }

        :hover {
            color: #40a9ff;
        }

        .clear {
            color: #40a9ff;
        }
    }
}

.second_line {
    text-align: center;
    font-weight: 500;
    font-size: 16px;
    color: #0C70EB;
    line-height: 22px;
    margin-top: 11px;
    display: flex;
    justify-content: center;

    span {
        cursor: pointer;
    }

    .img_box {
        padding-top: 8px;
        margin-left: 4px;

        img {
            width: 8px;
            height: 4px;
            display: block;
        }
    }
}