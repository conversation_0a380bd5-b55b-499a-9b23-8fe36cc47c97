<template>
  <div class="con">
    <div class="top flex">
      <img
        class="left_img"
        src="../../assets/images/buyList/leftTit.png"
        alt=""
      />
      <img
        class="right_img"
        src="../../assets/images/buyList/china.png"
        alt=""
      />
    </div>
    <div class="title">标准产品封装包订购单</div>
    <div class="list">
      <div class="list_tit">基础信息</div>
      <div class="box">
        <div class="cell">
          <span class="cell_top">
            <img
              src="../../assets/images/buyList/logo.png"
              alt=""
              class="logo"
            />
            客户名称
          </span>
          <span style="display: block; margin-left: 24px">{{
            buyDetail.customerName
          }}</span>
        </div>
        <div class="cell">
          <span class="cell_top">
            <img
              src="../../assets/images/buyList/logo.png"
              alt=""
              class="logo"
            />
            联系电话
          </span>
          <span style="display: block; margin-left: 24px">{{
            buyDetail.contactNumber
          }}</span>
        </div>
        <div class="cell">
          <span class="cell_top">
            <img
              src="../../assets/images/buyList/logo.png"
              alt=""
              class="logo"
            />
            联系地址
          </span>
          <span style="display: block; margin-left: 24px">{{
            dealData(buyDetail.address, "3")
          }}</span>
        </div>
        <div class="cell">
          <span class="cell_top">
            <img
              src="../../assets/images/buyList/logo.png"
              alt=""
              class="logo"
            />
            所属行业
          </span>
          <span style="display: block; margin-left: 24px">{{
            dealData(buyDetail.industry, "3")
          }}</span>
        </div>
        <div class="cell">
          <span class="cell_top">
            <img
              src="../../assets/images/buyList/logo.png"
              alt=""
              class="logo"
            />
            企业名称
          </span>
          <span style="display: block; margin-left: 24px">{{
            buyDetail.enterpriseName
          }}</span>
        </div>
        <div class="cell">
          <span class="cell_top">
            <img
              src="../../assets/images/buyList/logo.png"
              alt=""
              class="logo"
            />
            企业规模
          </span>
          <span style="display: block; margin-left: 24px">{{
            dealData(buyDetail.enterpriseScale, "3")
          }}</span>
        </div>
      </div>
    </div>
    <div class="list">
      <div class="list_tit">产品列表</div>
      <div class="box">
        <a-table
          :dataSource="buyDetail.productPackageLists"
          :columns="columns"
          bordered
        >
          <template #productName="{ record, text }">
            <span class="name">{{ text }}</span>
          </template>
          <template #productType_specification="{ record, text }">
            <span class="name">{{ dealData(text, "1") }}</span>
          </template>
          <template #price="{ record, text }">
            <span class="name">{{ dealData(text, "2") }}</span>
          </template>
        </a-table>
      </div>
    </div>
    <div class="boot">
      <div class="box">
        <span class="left_tit">客户经理</span>
        <span class="left_con" v-if="buyDetail.customerManager != ''">{{
          buyDetail.customerManager
        }}</span>
        <span class="left_con" v-else>-</span>
      </div>
      <div class="box">
        <span class="left_tit">联系电话</span>
        <span class="left_con" v-if="buyDetail.managerNumber != ''">{{
          buyDetail.managerNumber
        }}</span>
        <span class="left_con" v-else>-</span>
      </div>
      <div class="box">
        <span class="left_tit">生成日期</span>
        <span class="left_con" v-if="buyDetail.contractDate != ''">{{
          buyDetail.contractDate
        }}</span>
        <span class="left_con" v-else>-</span>
      </div>
    </div>
  </div>
</template>
<script>
import { defineComponent, reactive, toRefs, ref } from "vue";
import { useHomeStore } from "@/store";
export default defineComponent({
  setup() {
    const homeStore = useHomeStore();
    const data = reactive({
      buyDetail: homeStore.buyListStroe,
      columns: [
        {
          title: "产品名称",
          dataIndex: "productName",
          key: "productName",
          align: "center",
          slots: { customRender: "productName" },
          width: "15%",
        },
        {
          title: "产品介绍",
          dataIndex: "productDesc",
          key: "productDesc",
          align: "left",
          ellipsis: false,
          slots: { customRender: "productDesc" },
        },
        // {
        //   title: "产品规格",
        //   dataIndex: "productType_specification",
        //   key: "productType_specification",
        //   align: "left",
        //   ellipsis: false,
        //   slots: { customRender: "productType_specification" },
        //   width: "20%",
        // },
        // {
        //   title: "标准价格",
        //   dataIndex: "price",
        //   key: "price",
        //   align: "center",
        //   ellipsis: false,
        //   slots: { customRender: "price" },
        //   width: "20%",
        // },
        {
          title: "产品数量",
          dataIndex: "productQuantity",
          key: "productQuantity",
          align: "center",
          ellipsis: true,
          slots: { customRender: "productQuantity" },
          width: "10%",
        },
      ],
    });
    window.addEventListener("beforeunload", function (e) {
      // 在这里执行你的代码
      homeStore.buyListStroe = {};
    });
    const dealData = (e, type) => {
      if (e === undefined || e === null || e === "" || e === "-" || e >= 8888) {
        if (type == 1) return "以具体业务规格为准";
        if (type == 2) return "以具体业务定价为准";
        if (type == 3) return "-";
      } else {
        return e;
      }
    };
    return {
      ...toRefs(data),
      homeStore,
      dealData,
    };
  },
});
</script>
<style lang="scss" scoped>
:deep(.ant-table-pagination.ant-pagination) {
  display: none !important;
}
@import "./index.scss";
</style>