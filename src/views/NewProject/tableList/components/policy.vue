<template>
  <div class="tabContent">
    <div v-if="tableList && tableList.length > 0" style="width: 100%">
      <div class="cardContent">
        <div class="card_total flex-1">
          <template v-for="(item, index) in tableList" :key="index">
            <div
              v-if="index < clickIndex * 4"
              :class="[
                'card_content',
                {
                  cardActive: cardActive == index,
                  rightActive: index % 2 != 0,
                  cardObvious: index < 2 && tableList.length < 3,
                  bottomLine:
                    (index == tableList.length - 1 ||
                      index == tableList.length - 2) &&
                    index > 1,
                },
              ]"
              @mouseenter="contentColor(index)"
              @mouseleave="contentLeave"
              @click="proDetail(item)"
            >
            	<div class="listTag">
								<img src="@/assets/images/newProject/classify5.png" alt="" />
							</div>
              <div style="display: flex; margin: 24px">
                <div>
                  <a-image
                    :width="168"
                    :height="105"
                    :preview="false"
                    v-if="item.mainImg"
                    :src="`${item.mainImg}`"
                    style="width: 168px; height: 105px"
                  />
                  <div
                    v-else
                    style="
                      width: 168px;
                      height: 105px;
                      text-align: center;
                      position: relative;
                    "
                    :style="backgroundStyles()"
                  >
                    <p
                      style="
                        font-weight: 700;
                        display: block;
                        color: #0a7aee;
                        position: absolute;
                        left: 50%;
                        top: 50%;
                        transform: translate(-50%, -50%);
                      "
                    >
                      {{ item.name }}
                    </p>
                  </div>
                </div>
                <div class="card_center">
                  <div class="card_text">
                    <div class="card_tag">
                      <a-tag color="#D7E6FF" v-if="item.classifyName">
                        {{ item.classifyName }}
                      </a-tag>
                      <div class="card_title">{{ item.name }}</div>
                    </div>
                  </div>
                  <div class="card_des">
                    {{ item.introduce }}
                  </div>
                  <div class="flex" style="justify-content: space-between">
                    <div class="flex">
                      <a-tag
                        color="#D7E6FF"
                        v-if="item.labelName && item.labelName[0]"
                        style="
                          display: block;
                          color: rgba(0, 0, 0, 0.45);
                          background-color: transparent;
                          border: 1px solid #d9d9d9;
                          line-height: 17px;
                        "
                        >{{ item.labelName[0] }}</a-tag
                      >
                      <a-tag
                        color="#D7E6FF"
                        v-if="item.labelName && item.labelName[1]"
                        style="
                          display: block;
                          color: rgba(0, 0, 0, 0.45);
                          background-color: transparent;
                          border: 1px solid #d9d9d9;
                          line-height: 17px;
                        "
                        >{{ item.labelName[1] }}</a-tag
                      >
                    </div>
                  </div>
                  <div
                    style="
                      display: flex;
                      align-items: center;
                      justify-content: space-between;
                    "
                  >
                    <div
                      style="
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        margin-right: 5px;
                      "
                    >
                      <div
                        style="
                          display: flex;
                          align-items: center;
                          margin-right: 18px;
                        "
                      >
                        <img
                          src="@/assets/images/home/<USER>"
                          style="width: 16px; height: 16px"
                          alt=""
                        />
                        <span
                          style="font-size: 12px; color: rgba(0, 0, 0, 0.45)"
                          v-if="item.viewCount"
                          >{{ item.viewCount }}</span
                        >
                        <span v-else>-</span>
                      </div>
                      <!--<div style="display: flex; align-items: center">
                        <img
                          src="@/assets/images/home/<USER>"
                          style="width: 16px; height: 16px"
                        />
                        <span
                          style="font-size: 12px; color: rgba(0, 0, 0, 0.45)"
                          v-if="item.downloadCount"
                          >{{ item.downloadCount }}</span
                        >
                        <span v-else>-</span>
                      </div>-->
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
      <div class="layPage" v-if="showPagination">
        <div
          class="showMore"
          @click="showMoreList"
          v-if="clickIndex * 4 < tableList.length"
        >
          <div>加载更多</div>
          <img src="@/assets/images/newProject/downArrow.png" alt=""/>
        </div>
        <div class="showMore" @click="retractList" v-else>
          <div>收起</div>
          <img src="@/assets/images/newProject/upArrow.png" alt=""/>
        </div>
        <!-- <a-pagination v-model:pageSize="pageItemSize" v-model:current="currentPage"
                    :pageSizeOptions="pageSizeOptions" show-quick-jumper show-size-changer :total="totalItemCount"
                    @change="pageChange" @showSizeChange="sizeChange" class="mypage" /> -->
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, onMounted, reactive, toRefs, watch } from "vue";
import { toCombinePage } from "@/api/combine/shoppingCart.js";
import eventBus from "@/utils/eventBus";
import { getsolutionList } from "@/api/solutionNew/home";
import bac from "@/assets/images/noDataBac.png";
import { useRouter, useRoute } from "vue-router";
import { AISearch } from "@/api/AI/ai.js";

export default defineComponent({
  props: {
    list: {
      type: Array,
      default: [],
    },
  },
  setup(props) {
    const router = useRouter();
    const route = useRoute();
    const data = reactive({
      tableList: [],
      loadingShow: false,
      pageSizeOptions: ["4", "10", "20", "50"],
      totalItemCount: 0,
      currentPage: 1,
      pageItemSize: 4,
      showPagination: true,
      cardActive: "-1",
      backgroundImage: bac,
      AISearchType: 2,
      name: "",
      clickIndex: 1,
    });
    watch(
      () => props.list,
      (val) => {
        setData();
      }
    );
    onMounted(()=>{
			setData();
		})

    const showMoreList = () => {
      data.clickIndex += 1;
    };

    const setData = () => {
      data.showPagination = true;
      data.tableList = props.list;
      if (data.tableList.length <= 4) {
        data.showPagination = false;
      }
      data.totalItemCount = props.list.length;
      data.tableList.forEach((item) => {
        item.labelName = item.labelName ? item.labelName.split(",") : [];
        item.provider = item.provider ? item.provider.split("/")[0] : "";
      });
    };

    const getList = (resetPage = false) => {
      if (resetPage) {
        data.currentPage = 1;
      }
      let pageParams = {
        pageNo: data.currentPage,
        pageSize: data.pageItemSize,
        keyword: data.name,
        zoneId: route.query.zoneId,
      };
      data.loadingShow = true;
      getsolutionList(pageParams)
        .then((res) => {
          data.showPagination = true;
          data.loadingShow = false;
          data.tableList = [];
          data.tableList = res.data.rows;
          if (data.tableList.length <= 4) {
            data.showPagination = false;
          }
          data.totalItemCount = res.data.totalRows;
        })
        .catch((error) => {
          data.loadingShow = false;
        });
    };

    const sizeChange = (current, size) => {
      data.pageItemSize = size;
      getList();
    };
    const add = (item) => {
      let addParams = {
        schemeId: item.id,
        type: "1",
      };
      localStorage.removeItem("seekName");
      toCombinePage({
        conclusion: "谢谢聆听",
        cover: item.name,
        list: [addParams],
        source: "2",
      }).then(() => {
        eventBus.emit("changeResultType", 2);
      });
    };
    const pageChange = (page, pageSize) => {
      data.currentPage = page;
      getList();
    };
    const backgroundStyles = () => {
      return {
        backgroundImage: `url(${data.backgroundImage})`, // 使用模板字符串来插入变量
        backgroundRepeat: "no-repeat",
        backgroundSize: "cover",
      };
    };
    const contentColor = (index) => {
      data.cardActive = index;
    };
    const contentLeave = () => {
      data.cardActive = "-1";
    };
    const proDetail = (val) => {
      router.push({
        query: {
          id: val.id,
        },
        name: "scenarioDetail",
      });
    };
    const retractList = () => {
      data.clickIndex = 1;
    };
    return {
      ...toRefs(data),
      sizeChange,
      retractList,
      proDetail,
      backgroundStyles,
      pageChange,
      contentLeave,
      contentColor,
      add,
      setData,
      showMoreList,
    };
  },
});
</script>

<style lang="scss" scoped>
.tabContent {
  width: 1200px;
  margin: 24px auto;
  background: #f5f7fc;
  border-radius: 10px;
  display: flex;
  align-items: flex-start;
  position: relative;

  .AITips {
    width: 100%;
    height: 40px;
    margin-top: 0;
    border-radius: 40px;
    background: linear-gradient(
      90deg,
      rgba(145, 141, 254, 0.3) 0%,
      rgba(73, 151, 254, 0.3) 54%,
      rgba(73, 194, 254, 0.3) 100%
    );

    .words {
      font-size: 16px;
      font-weight: 500;
      background: linear-gradient(90deg, #4740ff 0%, #247efd 55%, #40a2ff 100%);
      -webkit-background-clip: text;
      /* Safari */
      -webkit-text-fill-color: transparent;
      /* 让文字透明 */
    }
  }

  .loading {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }

  .tabModel {
    border-right: 1px solid #dae2f5;
    height: 850px;
    overflow: hidden auto;
    flex: none;
  }

  .tabModel::-webkit-scrollbar {
    display: none;
  }

  .cardContent {
    // height: 780px;
    overflow: hidden auto;
  }

  .cart-button {
    // position: absolute;
    // right: 20px;
    // bottom: 6px;
    border: none;
    background-color: #f5f7fc;
    color: #40a9ff;
  }

  .cardContent::-webkit-scrollbar {
    display: none;
  }

  .tab_text {
    font-size: 18px;
    color: #262626;
    cursor: pointer;
    height: 40px;
  }

  .cart-button[disabled] {
    cursor: not-allowed;
    opacity: 0.5;
  }

  .activeTab {
    font-weight: bold;
    font-size: 18px;
    color: #ffffff;
    background-image: url("@/assets/images/home/<USER>");
    background-repeat: no-repeat;
    background-size: contain;
    width: 226px;
    height: 104px;
    line-height: 40px;
    padding-top: 32px;
  }

  .topTab {
    margin-top: 0;
    background-image: url("@/assets/images/home/<USER>");
    font-weight: bold;
    font-size: 18px;
    color: #ffffff;
    background-repeat: no-repeat;
    background-size: contain;
    width: 226px;
    height: 104px;
    line-height: 40px;
  }

  :deep(.ant-tag) {
    font-weight: 500;
    font-size: 12px;
    color: #2e7fff;
    height: 20px;
    display: flex;
    align-items: center;
  }

  .card_total {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    border-bottom: 1px solid #dae2f5;
  }

  .emptyPhoto {
    margin: auto;

    img {
      width: 240px;
      height: 248px;
    }
  }

  .card_content {
    position: relative;
    width: 50%;
    height: 163px;
    border-right: 1px solid #dae2f5;
    border-bottom: 1px solid #dae2f5;
    cursor: pointer;

    .listTag {
      position: absolute;
      top: 10px;
      left: 14px;
      z-index: 1000;
    }
  }

  .rightActive {
    border-right: none;
  }

  .cardActive {
    background-color: #ffffff;
    transition: all 0.2s;
    box-shadow: 0 0 10px #dae2f5;
  }

  .bottomLine {
    border-bottom: none;
  }

  .cardObvious {
    border-bottom: none;
  }

  .card_center {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    // height: 105px;
    margin-left: 12px;
    // min-width: 66%;
    flex: 1;
  }

  .card_text {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .card_tag {
      display: flex;
      align-items: center;
    }

    .card_title {
      font-weight: bold;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
    }

    :deep(.cityStyle) {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.45);
      background-color: transparent;
      // border: none;
      // margin-right: -6px;
    }
  }

  .card_des {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    /* 控制显示的行数 */
    overflow: hidden;
    text-overflow: ellipsis;
    margin-right: 4px;
    word-break: break-all;
  }

  .layPage {
    width: 100%;
    text-align: center;
    display: flex;
    align-content: center;
    justify-content: center;

    .showMore {
      display: flex;
      align-items: center;
      cursor: pointer;
      padding: 2px 10px;
      margin: 15px 0;
      background-color: rgba(12, 112, 235, 0.08);
      border-radius: 4px;
      > div:first-child {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 16px;
        color: #0c70eb;
        margin-right: 6px;
      }

      > img {
        width: 10px;
        height: 5px;
      }
    }
  }
}

.add-icon {
  width: 16px;
}
</style>

<style lang="scss">
.mypage {
  .ant-pagination {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-select-selector {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-pagination-item-active {
    background: #007eff;
  }

  .ant-pagination-item-active a {
    color: #ffffff;
  }

  .ant-pagination-item-active:focus a,
  .ant-pagination-item-active:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-active a:focus,
  .ant-pagination-item-active a:hover {
    color: #ffffff;
  }

  .ant-pagination-item:focus,
  .ant-pagination-item:hover {
    background: #007eff;
  }

  .ant-pagination-item:focus a,
  .ant-pagination-item:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-disabled:hover,
  .ant-pagination-item-disabled:focus {
    background-color: #ffffff;
    border-color: #d9d9d9;

    a {
      color: rgba(0, 0, 0, 0.25);
    }
  }

  .ant-pagination-options-size-changer.ant-select {
    margin-right: 0;
  }

  .ant-pagination-options-quick-jumper {
    margin-left: 8px;
  }
}
</style>