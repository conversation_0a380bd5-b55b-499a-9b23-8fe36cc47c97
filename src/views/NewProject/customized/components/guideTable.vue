<template>
  <!-- zdl标记 -->
  <div>
    <div v-if="status === 1">
      <div class="orderHeard">
        <div class="orderHeard-first">
          <div class="orderHeard-first-order">1</div>
          <div class="orderHeard-first-name">选择方案</div>
        </div>
        <div class="orderHeard-line"></div>
        <div class="orderHeard-second">
          <div class="orderHeard-second-order">2</div>
          <div class="orderHeard-second-name">补充场景</div>
        </div>
        <div class="orderHeard-line"></div>
        <div class="orderHeard-second">
          <div class="orderHeard-second-order">3</div>
          <div class="orderHeard-second-name">预选组合</div>
        </div>
        <div class="orderHeard-line"></div>
        <div class="orderHeard-second">
          <div class="orderHeard-second-order">4</div>
          <div class="orderHeard-second-name">制定封面和结束语</div>
        </div>
      </div>
      <div class="searchInfo">
        <img style="width: 54px;height: 54px; position: absolute;z-index: 100;top: 14px;left: 18px;"
          src="../../../../assets/images/AI/ai.png" alt="">
        <div class="vocationPull">
          <a-config-provider :locale="zhCN" :getPopupContainer="(triggerNode) => triggerNode.parentNode">
            <a-input-search v-model:value="name" placeholder="请输入方案名称、场景、标签等关键字检索需求描述" @search="seekContent">
              <template #enterButton>
                <div class="flex just-center align-center">
                  <img style="width: 20px;height: 20px;" class="margin_r_16"
                    src="../../../../assets/images/AI/search.png" alt="">
                  <div>
                    搜索
                  </div>
                </div>
              </template>
            </a-input-search>
          </a-config-provider>
        </div>
      </div>
      <div class="tabContent">
        <div v-if="tableList && tableList.length > 0" style="width: 100%">
          <div class="cardContent">
            <div class="card_total">
              <template v-for="(item, index) in tableList" :key="index">
                <div :class="[
                  'card_content',
                  {
                    cardActive: cardActive == index,
                    rightActive: index % 2 != 0,
                    cardObvious: index < 2 && tableList.length < 3,
                    bottomLine:
                      (index == tableList.length - 1 ||
                        index == tableList.length - 2) &&
                      index > 1,
                    selectBorder: schemeSelectIds.includes(item.id),
                  },
                ]" @mouseenter="contentColor(index)" @mouseleave="contentLeave" @click="proDetail(item)">
                  <button class="cart-button pointer" @click.stop="add(item)" v-if="!schemeSelectIds.includes(item.id)">
                    <img class="add-icon" src=" @/assets/images/AI/isadded.png" />
                    <span class="add"> &nbsp;加入预选</span>
                  </button>
                  <button class="cart-button pointer" @click.stop="outScheme(item)" v-else>
                    <img class="add-icon" src=" @/assets/images/AI/shiftOut.png" />
                    <span class="shiftOut"> &nbsp;移出预选</span>
                  </button>
                  <div style="display: flex; margin: 24px">
                    <div>
                      <a-image :width="168" :height="105" :preview="false" v-if="item.logo" :src="`${item.logo}`" />
                      <img src="@/assets/images/home/<USER>" style="width: 168px; height: 105px" v-else />
                    </div>
                    <div class="card_center">
                      <div class="card_text">
                        <div class="card_tag">
                          <div class="card_title">{{ item.name }}</div>
                          <span class="cardTag" style="background-color: #d7e6ff; color: #2e7fff">{{ item.industryName
                            || item.categoryName }}</span>
                          <span class="cityStyle" v-if="item.provider">{{
                            item.provider
                          }}</span>
                        </div>
                      </div>
                      <div class="card_des">
                        {{ item.abilityIntro || item.description }}
                      </div>
                      <div style="
                          display: flex;
                          align-items: center;
                          justify-content: space-between;
                        ">
                        <div style="
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                          "></div>
                      </div>
                      <div style="
                          display: flex;
                          align-items: center;
                          justify-content: space-between;
                        ">
                        <div>
                          <!--<img style="width: 112px; height: 22px" src="@/assets/images/home/<USER>" />-->
                        </div>
                        <div style="display: flex; align-items: center">
                          <img src="@/assets/images/home/<USER>" style="width: 16px; height: 16px" />
                          <span style="font-size: 12px; color: rgba(0, 0, 0, 0.45)" v-if="item.viewCount">{{
                            item.viewCount }}</span>
                          <span v-else>-</span>
                          <img src="@/assets/images/home/<USER>"
                            style="width: 16px; height: 16px; margin-left: 18px" />
                          <span style="font-size: 12px; color: rgba(0, 0, 0, 0.45)" v-if="item.downloadCount">{{
                            item.downloadCount }}</span>
                          <span v-else>-</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </div>
          <div class="layPage">
            <a-pagination v-model:pageSize="pageItemSize" v-model:current="currentPage"
              :pageSizeOptions="pageSizeOptions" show-quick-jumper show-size-changer :total="totalItemCount"
              @change="pageChange" @showSizeChange="sizeChange" class="mypage" />
          </div>
          <div class="btn_box">
            <span></span>
            <div>
              <span class="refuse" @click="refuse">取消</span>
              <span class="submit" @click="submit">下一步（已选择{{ schemeSelectIds.length }}个）</span>
            </div>
          </div>
        </div>
        <div v-if="tableList && tableList.length == 0" class="emptyPhoto">
          <img src="@/assets/images/home/<USER>" />
        </div>
        <div class="loading" v-show="loadingShow">
          <a-spin />
        </div>
      </div>
    </div>
    <div v-if="status === 2">
      <div class="orderHeard">
        <div class="orderHeard-third">
          <div class="orderHeard-third-order">
            <img src="@/assets/images/AI/achieve.png" style="width: 32px; height: 32px" alt="" />
          </div>
          <div class="orderHeard-third-name">选择方案</div>
        </div>
        <div class="orderHeard-line"></div>
        <div class="orderHeard-first">
          <div class="orderHeard-first-order">2</div>
          <div class="orderHeard-first-name">补充场景</div>
        </div>
        <div class="orderHeard-line"></div>
        <div class="orderHeard-second">
          <div class="orderHeard-second-order">3</div>
          <div class="orderHeard-second-name">预选组合</div>
        </div>
        <div class="orderHeard-line"></div>
        <div class="orderHeard-second">
          <div class="orderHeard-second-order">4</div>
          <div class="orderHeard-second-name">制定封面和结束语</div>
        </div>
      </div>
      <div class="searchInfo">
        <img style="width: 54px;height: 54px; position: absolute;z-index: 100;top: 14px;left: 18px;"
          src="../../../../assets/images/AI/ai.png" alt="">
        <!-- <div class="flex just-center margin_b_20">
          <div class="tab flex">
            <div :class="['ability', { active: activeTab == '3' }]" @click="changeTab(3)">自有能力</div>
            <div :class="['ability', { active: activeTab == '8' }]" @click="changeTab(8)">方案能力</div>
            <div :class="['ability', { active: activeTab == '6' }]" @click="changeTab(6)">标准产品</div>
          </div>
        </div> -->
        <div class="vocationPull">
          <a-config-provider :locale="zhCN" :getPopupContainer="(triggerNode) => triggerNode.parentNode">
            <a-input-search v-model:value="name" placeholder="请输入方案名称、场景、标签等关键字检索需求描述" @search="seekContent">
              <template #enterButton>
                <div class="flex just-center align-center">
                  <img style="width: 20px;height: 20px;" class="margin_r_16"
                    src="../../../../assets/images/AI/search.png" alt="">
                  <div>
                    搜索
                  </div>
                </div>
              </template>
            </a-input-search>
          </a-config-provider>
        </div>
      </div>
      <div class="tabContent">
        <div v-if="tableList && tableList.length > 0" style="width: 100%">
          <div class="cardContent">
            <div class="card_total">
              <template v-for="(item, index) in tableList" :key="index">
                <div :class="[
                  'card_content',
                  {
                    cardActive: cardActive == index,
                    rightActive: index % 2 != 0,
                    cardObvious: index < 2 && tableList.length < 3,
                    bottomLine:
                      (index == tableList.length - 1 ||
                        index == tableList.length - 2) &&
                      index > 1,
                    selectBorder: solveSchemeSelectedFlag.includes(
                      item.idFlag
                    ),
                  },
                ]" @mouseenter="contentColor(index)" @mouseleave="contentLeave" @click="proDetail(item)">
                  <button class="cart-button pointer" @click.stop="add(item)"
                    v-if="!solveSchemeSelectedFlag.includes(item.idFlag)">
                    <img class="add-icon" src=" @/assets/images/AI/isadded.png" />
                    <span class="add"> &nbsp;加入预选</span>
                  </button>
                  <button class="cart-button pointer" @click.stop="outSolveScene(item)" v-else>
                    <img class="add-icon" src=" @/assets/images/AI/shiftOut.png" />
                    <span class="shiftOut"> &nbsp;移出预选</span>
                  </button>
                  <div style="display: flex; margin: 24px">
                    <div style="position: relative;">
                      <div style="position: absolute;top: -10px;left: -10px;z-index: 1000;">
                        <img v-if="item.classify == 2" src="@/assets/images/newProject/classify1.png" alt="" />
                        <img v-if="item.classify == 1" src="@/assets/images/newProject/classify2.png" alt="" />
                        <img v-if="item.classify == 0" style="width: 60px;height: 32px;"
                          src="@/assets/images/AI/productTip.png" alt="" />
                      </div>
                      <a-image :width="168" :height="105" :preview="false" v-if="item.image" :src="`${item.image}`" />
                      <img src="@/assets/images/home/<USER>" style="width: 168px; height: 105px" v-else />
                    </div>
                    <div class="card_center">
                      <div class="card_text">
                        <div class="card_tag">
                          <div class="card_title">{{ item.name }}</div>
                          <span class="cityStyle" v-if="item.provider">{{
                            item.provider
                          }}</span>
                        </div>
                      </div>
                      <div class="card_des">
                        {{ item.introduce }}
                      </div>
                      <div style="
                          display: flex;
                          align-items: center;
                          justify-content: space-between;
                        ">
                        <div style="
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                          "></div>
                      </div>
                      <div style="
                          display: flex;
                          align-items: center;
                          justify-content: space-between;
                        ">
                        <div>
                          <!--<img style="width: 112px; height: 22px" src="@/assets/images/home/<USER>" />-->
                        </div>
                        <div style="display: flex; align-items: center">
                          <img src="@/assets/images/home/<USER>" style="width: 16px; height: 16px" />
                          <span style="font-size: 12px; color: rgba(0, 0, 0, 0.45)" v-if="item.viewCount">{{
                            item.viewCount }}</span>
                          <span style="font-size: 12px; color: rgba(0, 0, 0, 0.45)" v-else>0</span>
                          <img src="@/assets/images/home/<USER>"
                            style="width: 16px; height: 16px; margin-left: 18px" />
                          <span style="font-size: 12px; color: rgba(0, 0, 0, 0.45)" v-if="item.downloadCount">{{
                            item.downloadCount }}</span>
                          <span style="font-size: 12px; color: rgba(0, 0, 0, 0.45)" v-else>0</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </div>
          <div class="layPage">
            <a-pagination v-model:pageSize="pageItemSize" v-model:current="currentPage"
              :pageSizeOptions="pageSizeOptions" show-quick-jumper show-size-changer :total="totalItemCount"
              @change="pageChange" @showSizeChange="sizeChange" class="mypage" />
          </div>
        </div>
        <div v-if="tableList && tableList.length == 0" class="emptyPhoto">
          <img src="@/assets/images/home/<USER>" />
        </div>
        <div class="loading" v-show="loadingShow">
          <a-spin />
        </div>
      </div>
      <div class="btn_box">
        <span></span>
        <div>
          <span class="refuse" @click="reLast">上一步</span>
          <span class="submit" @click="submit">下一步（已选择{{ solveSchemeSelectIds.length }}个）</span>
        </div>
      </div>
    </div>

    <div v-if="status === 3">
      <div class="orderHeard">
        <div class="orderHeard-third">
          <div class="orderHeard-third-order">
            <img src="@/assets/images/AI/achieve.png" style="width: 32px; height: 32px" alt="" />
          </div>
          <div class="orderHeard-third-name">选择方案</div>
        </div>
        <div class="orderHeard-line"></div>
        <div class="orderHeard-third">
          <div class="orderHeard-third-order">
            <img src="@/assets/images/AI/achieve.png" style="width: 32px; height: 32px" alt="" />
          </div>
          <div class="orderHeard-third-name">补充场景</div>
        </div>
        <div class="orderHeard-line"></div>
        <div class="orderHeard-first">
          <div class="orderHeard-first-order">3</div>
          <div class="orderHeard-first-name">预选组合</div>
        </div>
        <div class="orderHeard-line"></div>
        <div class="orderHeard-second">
          <div class="orderHeard-second-order">4</div>
          <div class="orderHeard-second-name">制定封面和结束语</div>
        </div>
      </div>
      <div class="tabContent">
        <div style="width: 100%">
          <div style="height: 500px; overflow: hidden auto">
            <div>
              <div style="margin-bottom: 10px">
                <span class="title">解决方案</span>
                <span class="substep">（{{ selectId ? "1" : "0" }}/{{
                  schemeSelectIds.length
                }}）</span>
                <span class="hint">成品解决方案限制1个</span>
              </div>
              <div class="cardContent">
                <div class="card_total" style="height: auto">
                  <template v-for="(item, index) in schemeSelectItem" :key="index">
                    <div :class="[
                      'card_content',
                      {
                        cardActive: cardActive == index,
                        rightActive: index % 2 != 0,
                        cardObvious: index < 2 && schemeSelectItem.length < 3,
                        bottomLine:
                          (index == schemeSelectItem.length - 1 ||
                            index == schemeSelectItem.length - 2) &&
                          index > 1,
                        selectBorder: selectId == item.id,
                      },
                    ]" @mouseenter="contentColor(index)" @mouseleave="contentLeave" @click="getId(item)">
                      <!--<button class="cart-button pointer" @click.stop="outScheme(item)">
				                <img class="add-icon" src=" @/assets/images/AI/cancelAdd.png"/>
				                <span class="add"> &nbsp;移出预选</span>
				              </button>-->
                      <div style="display: flex; margin: 24px">
                        <a-radio-group :value="selectId">
                          <a-radio :value="item.id"> </a-radio>
                        </a-radio-group>
                        <div>
                          <a-image :width="168" :height="105" :preview="false" v-if="item.abilityPicture"
                            :src="`${item.abilityPicture}`" />
                          <img src="@/assets/images/home/<USER>" style="width: 168px; height: 105px" v-else />
                        </div>
                        <div class="card_center">
                          <div class="card_text">
                            <div class="card_tag">
                              <div class="card_title">{{ item.name }}</div>
                              <span class="cardTag" style="
                                  background-color: #d7e6ff;
                                  color: #2e7fff;
                                ">{{
                                  item.industryName || item.categoryName
                                }}</span>
                              <span class="cityStyle" v-if="item.provider">{{
                                item.provider
                              }}</span>
                            </div>
                          </div>
                          <div class="card_des">
                            {{ item.abilityIntro || item.description }}
                          </div>
                          <div style="
                              display: flex;
                              align-items: center;
                              justify-content: space-between;
                            ">
                            <div style="
                                display: flex;
                                align-items: center;
                                justify-content: space-between;
                              "></div>
                          </div>
                          <div style="
                              display: flex;
                              align-items: center;
                              justify-content: space-between;
                            ">
                            <div>
                              <!--<img style="width: 112px; height: 22px" src="@/assets/images/home/<USER>" />-->
                            </div>
                            <div style="display: flex; align-items: center">
                              <img src="@/assets/images/home/<USER>" style="width: 16px; height: 16px" />
                              <span style="
                                  font-size: 12px;
                                  color: rgba(0, 0, 0, 0.45);
                                " v-if="item.viewCount">{{ item.viewCount }}</span>
                              <span style="
                                  font-size: 12px;
                                  color: rgba(0, 0, 0, 0.45);
                                " v-else>0</span>
                              <img src="@/assets/images/home/<USER>" style="
                                  width: 16px;
                                  height: 16px;
                                  margin-left: 18px;
                                " />
                              <span style="
                                  font-size: 12px;
                                  color: rgba(0, 0, 0, 0.45);
                                " v-if="item.downloadCount">{{ item.downloadCount }}</span>
                              <span style="
                                  font-size: 12px;
                                  color: rgba(0, 0, 0, 0.45);
                                " v-else>0</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </template>
                </div>
              </div>
            </div>
            <div>
              <div style="margin-bottom: 10px">
                <span class="title">补充场景</span>
                <span class="substep">（{{ solveSelectIds.length }}/{{
                  solveSchemeSelectedFlag.length
                }}）</span>
              </div>
              <div class="cardContent">
                <div class="card_total" style="height: auto">
                  <template v-for="(item, index) in solveSchemeSelectItem" :key="index">
                    <div :class="[
                      'card_content',
                      {
                        cardActive: cardActive == index,
                        rightActive: index % 2 != 0,
                        cardObvious:
                          index < 2 && solveSchemeSelectItem.length < 3,
                        bottomLine:
                          (index == solveSchemeSelectItem.length - 1 ||
                            index == solveSchemeSelectItem.length - 2) &&
                          index > 1,
                        selectBorder: solveSelectIds.includes(item.idFlag),
                      },
                    ]" @mouseenter="contentColor(index)" @mouseleave="contentLeave" @click.prevent="getSceneId(item)">
                      <div style="display: flex; margin: 24px">
                        <a-checkbox-group :value="solveSelectIds">
                          <a-checkbox :value="item.idFlag" @change="getSceneId">
                          </a-checkbox>
                        </a-checkbox-group>
                        <div>
                          <a-image :width="168" :height="105" :preview="false" v-if="item.image"
                            :src="`${item.image}`" />
                          <img src="@/assets/images/home/<USER>" style="width: 168px; height: 105px" v-else />
                        </div>
                        <div class="card_center">
                          <div class="card_text">
                            <div class="card_tag">
                              <div class="card_title">{{ item.name }}</div>

                              <span class="cityStyle" v-if="item.provider">{{
                                item.provider
                              }}</span>
                            </div>
                          </div>
                          <div class="card_des">
                            {{ item.introduce }}
                          </div>
                          <div style="
                              display: flex;
                              align-items: center;
                              justify-content: space-between;
                            ">
                            <div style="
                                display: flex;
                                align-items: center;
                                justify-content: space-between;
                              "></div>
                          </div>
                          <div style="
                              display: flex;
                              align-items: center;
                              justify-content: space-between;
                            ">
                            <div>
                              <!--<img style="width: 112px; height: 22px" src="@/assets/images/home/<USER>" />-->
                            </div>
                            <div style="display: flex; align-items: center">
                              <img src="@/assets/images/home/<USER>" style="width: 16px; height: 16px" />
                              <span style="
                                  font-size: 12px;
                                  color: rgba(0, 0, 0, 0.45);
                                " v-if="item.viewCount">{{ item.viewCount }}</span>
                              <span style="
                                  font-size: 12px;
                                  color: rgba(0, 0, 0, 0.45);
                                " v-else>0</span>
                              <img src="@/assets/images/home/<USER>" style="
                                  width: 16px;
                                  height: 16px;
                                  margin-left: 18px;
                                " />
                              <span style="
                                  font-size: 12px;
                                  color: rgba(0, 0, 0, 0.45);
                                " v-if="item.downloadCount">{{ item.downloadCount }}</span>
                              <span style="
                                  font-size: 12px;
                                  color: rgba(0, 0, 0, 0.45);
                                " v-else>0</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </template>
                </div>
              </div>
            </div>
          </div>
          <div class="btn_box">
            <span></span>
            <div>
              <span class="refuse" @click="reLast">上一步</span>
              <span class="submit" @click="submit">下一步</span>
            </div>
          </div>
        </div>
        <!-- <div v-if="tableList && tableList.length == 0" class="emptyPhoto">
          <img src="@/assets/images/home/<USER>" />
        </div> -->
        <div class="loading" v-show="loadingShow">
          <a-spin />
        </div>
      </div>
    </div>
    <div v-if="status === 4">
      <div class="orderHeard">
        <div class="orderHeard-third">
          <div class="orderHeard-third-order">
            <img src="@/assets/images/AI/achieve.png" style="width: 32px; height: 32px" alt="" />
          </div>
          <div class="orderHeard-third-name">选择方案</div>
        </div>
        <div class="orderHeard-line"></div>
        <div class="orderHeard-third">
          <div class="orderHeard-third-order">
            <img src="@/assets/images/AI/achieve.png" style="width: 32px; height: 32px" alt="" />
          </div>
          <div class="orderHeard-third-name">补充场景</div>
        </div>
        <div class="orderHeard-line"></div>
        <div class="orderHeard-third">
          <div class="orderHeard-third-order">
            <img src="@/assets/images/AI/achieve.png" style="width: 32px; height: 32px" alt="" />
          </div>
          <div class="orderHeard-third-name">预选组合</div>
        </div>
        <div class="orderHeard-line"></div>
        <div class="orderHeard-first">
          <div class="orderHeard-first-order">4</div>
          <div class="orderHeard-first-name">制定封面和结束语</div>
        </div>
      </div>
      <div class="tabContent">
        <div style="width: 100%">
          <div style="height: 500px; overflow: hidden auto">
            <div>
              <div style="margin-bottom: 10px">
                <span class="title">封面</span>
              </div>
              <div class="cardContent">
                <div class="card_total" style="height: auto">
                  <a-form :model="formStateFront" style="width: 98%; margin-left: 2%" id="form1" ref="formRefFront">
                    <a-form-item label="封面标题" name="detail" :rules="[
                      { required: true, message: '请输入封面标题' },
                      { trigger: 'blur' },
                    ]">
                      <a-input v-model:value="formStateFront.detail" placeholder="请输入封面标题" />
                    </a-form-item>
                  </a-form>
                </div>
              </div>
            </div>
            <div>
              <div style="margin-bottom: 10px">
                <span class="title">结束语</span>
              </div>
              <div class="cardContent">
                <div class="card_total" style="height: auto">
                  <a-form :model="formStateFinal" style="width: 98%; margin-left: 2%" id="form2" ref="formRefFinal">
                    <a-form-item label="结束语" name="detail" :rules="[
                      { required: true, message: '请输入结束语' },
                      { trigger: 'blur' },
                    ]">
                      <a-input v-model:value="formStateFinal.detail" placeholder="请输入结束语" />
                    </a-form-item>
                  </a-form>
                </div>
              </div>
            </div>
          </div>
          <div class="btn_box">
            <span></span>
            <div>
              <span class="refuse" @click="reLast">上一步</span>
              <span class="submit" @click="putIn">提交</span>
            </div>
          </div>
        </div>
        <!-- <div v-if="tableList && tableList.length == 0" class="emptyPhoto">
          <img src="@/assets/images/home/<USER>" />
        </div> -->
        <div class="loading" v-show="loadingShow">
          <a-spin />
        </div>
      </div>
    </div>
    <a-modal v-model:visible="visible" title="提示" @ok="handleOk">
      <p>已存在定制组合，请确认是否替换已有组合?</p>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, ref, watch } from "vue";
import { useRouter } from "vue-router";
import { message } from "ant-design-vue";
import { getProgrammeList } from "@/api/solutionNew/home";
import { guideList } from "@/api/moduleList/home";
import { myCombineList } from "@/api/combine/combine.js";
import zhCN from "ant-design-vue/es/locale/zh_CN";
import { toCombinePage } from "@/api/combine/shoppingCart.js";
import eventBus from "@/utils/eventBus";
import { getSeparateAIList, AISearch } from "@/api/AI/ai.js"
export default defineComponent({
  components: {},
  props: {},
  setup(props, { emit }) {
    const vocation = ref("");
    const region = ref("");
    const formRefFront = ref();
    const formRefFinal = ref();
    const data = reactive({
      // 区分点击加入预选类型
      currentSceneClassify: 0,
      // 保存上一次方案选中数据
      oldSchemeSelectItem: {},
      name: "",
      moment: "",
      visible: false,
      loadingShow: true,
      activeKey: "",
      cardActive: "-1",
      pageSizeOptions: ["10", "20", "30", "50"],
      totalItemCount: 0,
      tableList: [],
      totalNum: 0,
      totalItemCount1: 0,
      currentPage: 1,
      pageItemSize: 10,
      status: 1,
      schemeSelectIds: [],
      schemeSelectItem: [],
      // programmeSelectIds: [],
      // programmeSelectItem: [],
      solveSchemeSelectIds: [],
      // 判断是否加入预选标志
      solveSchemeSelectedFlag: [],
      solveSchemes: [],
      solveSchemeSelectItem: [],
      // 判断产品是否加入预选标志
      selectId: null,
      solveId: {},
      selectIds: [],
      solveSelectIds: [],
      selectLists: [],
      formStateFront: {
        detail: null,
      },
      formStateFinal: {
        detail: "谢谢聆听",
      },
      activeTab: 3,
      searchType: 3,
      AISearchList: [],
      isAISearch: false,
    });
    const getList = () => {
      let pageParams = {
        pageNo: data.currentPage,
        pageSize: data.pageItemSize,
        keyword: data.name,
      };
      if (data.status === 1) {
        if (data.name) {
          data.isAISearch = true
          AISearch({
            question: data.name,
            type: 2
          }).then((res) => {
            console.log('111111111111111')
            data.loadingShow = false;
            data.AISearchList = [];
            data.AISearchList = res.data
            data.totalItemCount = res.data.length;
            // data.AISearchList.map((item) => {
            //   item.label = item.label.split(",");
            // });
            data.tableList = data.AISearchList.slice(0, 10)
            if (data.activeKey == "") {
              data.totalItemCount1 = res.data.totalRows;
            }
          })
        } else {
          pageParams.isSensitive = 0;
          data.isAISearch = false
          getProgrammeList(pageParams)
            .then((res) => {
              data.loadingShow = false;
              data.tableList = [];
              data.tableList = res.data.rows;

              data.totalItemCount = res.data.totalRows;
              data.tableList.map((item) => {
                item.label = item.label.split(",");
              });
              if (data.activeKey == "") {
                data.totalItemCount1 = res.data.totalRows;
              }
            })
            .catch((error) => {
              data.loadingShow = false;
            });
        }

      } else if (data.status === 2) {
        // 场景能力合并
        let params = {
          question: data.name,
          busiId: localStorage.getItem("AInowCustomizeSolutionId"),
          resultType: 4,
        }
        // guideList(pageParams)
        //   .then((res) => {
        //     data.loadingShow = false;
        //     data.tableList = [];
        //     data.tableList = res.data.rows;
        //     data.tableList.forEach((item) => {
        //       item.idFlag = item.id + "," + item.classify;
        //       item.image = item.picture
        //     });
        //     data.totalItemCount = res.data.totalRows;

        //     if (data.activeKey == "") {
        //       data.totalItemCount1 = res.data.totalRows;
        //     }
        //   })
        //   .catch((error) => {
        //     data.loadingShow = false;
        //   });
        if (data.name) {
          data.isAISearch = true
          getSeparateAIList(params).then((res) => {
            console.log('res', res)
            data.loadingShow = false;
            data.AISearchList = [];
            data.AISearchList = res.data;
            data.AISearchList.forEach((item) => {
              item.idFlag = item.id + "," + item.classify;
              item.image = item.picture
            });
            data.totalItemCount = res.data.length;
            data.tableList = data.AISearchList.slice(0, 10)
            if (data.activeKey == "") {
              data.totalItemCount1 = res.data.totalRows;
            }
          })
        } else {
          data.isAISearch = false
          guideList(pageParams)
            .then((res) => {
              data.loadingShow = false;
              data.tableList = [];
              data.tableList = res.data.rows;
              data.tableList.forEach((item) => {
                item.idFlag = item.id + "," + item.classify;
                item.image = item.picture
              });
              data.totalItemCount = res.data.totalRows;

              if (data.activeKey == "") {
                data.totalItemCount1 = res.data.totalRows;
              }
            })
            .catch((error) => {
              data.loadingShow = false;
            });
        }
      }
      data.loadingShow = true;
    };
    getList();
    eventBus.on("moduleRefresh", getList);

    const seekContent = () => {
      data.currentPage = 1;
      getList();
    };

    const contentColor = (index) => {
      data.cardActive = index;
    };
    const router = useRouter();
    const proDetail = (item) => {
      let isHave = false;
      if (data.status == 1) {
        for (let i in data.schemeSelectIds) {
          if (data.schemeSelectIds[i] == item.id) {
            isHave = true;
            break;
          }
        }
        if (!isHave) {
          add(item);
        } else {
          outScheme(item);
        }
      } else if (data.status == 2) {
        for (let i in data.solveSchemeSelectIds) {
          if (data.solveSchemeSelectIds[i] == item.idFlag) {
            isHave = true;
            break;
          }
        }
        if (!isHave) {
          add(item);
        } else {
          outSolveScene(item);
        }
      }
    };
    const contentLeave = () => {
      data.cardActive = "-1";
    };

    const add = (item) => {
      if (data.status === 1) {
        // 加入方案预选前删除已选择的预选
        outScheme();
        data.schemeSelectIds = [item.id];
        data.oldSchemeSelectItem = item;
        data.schemeSelectItem = [item];
        localStorage.setItem("AInowCustomizeSolutionId", item.id);
      }
      // 场景和能力加入预选
      if (data.status === 2) {
        data.currentSceneClassify = item.classify;
        data.solveSchemeSelectIds.push(item.idFlag);
        data.solveSchemeSelectedFlag.push(item.idFlag);
        data.solveSchemeSelectItem.push(item);
        console.log(data.solveSchemeSelectedFlag);
      }
    };

    // 方案删除
    const outScheme = (item) => {
      if (item && item.id && item.id == data.selectId) {
        message.warning("当前方案已在第三步选中，不可删除");
        return false;
      }
      data.schemeSelectIds = [];
      data.schemeSelectItem = [];
    };

    // const outProgramme = (item) => {
    //   if (data.selectIds.indexOf(item.id) > -1) {
    //     message.warning("当前能力已在第三步选中，不可删除");
    //     return false;
    //   }
    //   data.programmeSelectIds = data.programmeSelectIds.filter(
    //     (item1, index1) => {
    //       return item1 !== item.id;
    //     }
    //   );
    //   data.programmeSelectItem = data.programmeSelectItem.filter(
    //     (item1, index1) => {
    //       return item1.id !== item.id;
    //     }
    //   );
    // };
    const outSolveScene = (item) => {
      if (data.solveSelectIds.indexOf(item.idFlag) > -1) {
        message.warning("当前能力已在第三步选中，不可删除");
        return false;
      }
      data.solveSchemeSelectIds = data.solveSchemeSelectIds.filter(
        (item1, index1) => {
          return item1 !== item.idFlag;
        }
      );
      data.solveSchemeSelectItem = data.solveSchemeSelectItem.filter(
        (item1, index1) => {
          return item1.idFlag !== item.idFlag;
        }
      );
      data.solveSchemeSelectedFlag = data.solveSchemeSelectedFlag.filter(
        (item1, index1) => {
          return item1 !== item.idFlag;
        }
      );
      console.log(data.solveSchemeSelectedFlag);
    };

    const getId = (item) => {
      data.solveId = {
        type: 1,
        schemeId: item.id,
      };
      data.selectId = item.id;
      data.formStateFront.detail = item.name;
    };

    const getSceneId = (item) => {
      console.log(item);
      let isHave = false;
      for (let i in data.solveSelectIds) {
        if (data.solveSelectIds[i] == item.idFlag) {
          isHave = true;
          break;
        }
      }
      if (!isHave) {
        // classify=2场景，1能力，4产品
        data.selectLists.push({
          type: item.classify == 0 ? 4 : (item.classify == 1 ? 2 : 3),
          schemeId: item.id,
        });
        data.solveSelectIds.push(item.idFlag);
      } else {
        data.selectLists = data.selectLists.filter(
          (item1) => item1.schemeId != item.id
        );
        data.solveSelectIds = data.solveSelectIds.filter(
          (item1) => item1 != item.idFlag
        );
      }
    };

    const refuse = () => {
      data.schemeSelectIds = [];
      data.schemeSelectItem = [];
      // data.programmeSelectIds = [];
      // data.programmeSelectItem = [];
      data.solveSchemeSelectIds = [];
      data.solveSchemeSelectItem = [];
      emit("close");
    };
    const pase = () => {
      emit("close");
    };

    //上一步
    const reLast = () => {
      data.name = ''
      data.status -= 1;
      data.currentPage = 1;
      if (data.status <= 2) {
        getList();
      }
    };
    //下一步
    const submit = () => {
      if (data.schemeSelectIds.length == 0) {
        message.warning("方案必选一条");
        return false;
      }
      data.name = ''
      if (data.status == 2) {
        data.selectId = data.schemeSelectItem[0].id;
        data.formStateFront.detail = data.schemeSelectItem[0].name;
        data.solveId = { type: 1, schemeId: data.schemeSelectItem[0].id };
        data.solveSelectIds = data.solveSchemeSelectItem.map(
          (item) => item.idFlag
        );
        data.solveSelectIds = [...data.solveSelectIds];
        data.selectLists = data.solveSchemeSelectItem.map((item) => {
          if (item.classify == 0) return { type: 4, schemeId: item.id };
          if (item.classify == 1) return { type: 2, schemeId: item.id };
          if (item.classify == 2) return { type: 3, schemeId: item.id };
        });
      }
      if (data.status == 3 && Object.keys(data.solveId).length == 0) {
        message.warning("请选择数据进行定制,方案必选一个");
        return false;
      }
      data.status += 1;
      if (data.status <= 2) {
        data.currentPage = 1;
        getList();
      }
    };
    const putIn = () => {
      formRefFront.value.validate().then(() => {
        formRefFinal.value.validate().then(() => {
          if (Object.keys(data.solveId).length != 0) {
            myCombineList()
              .then((res) => {
                if (
                  res.data.list.some(
                    (item) => item.list && item.list.length > 0
                  )
                ) {
                  data.visible = true;
                } else {
                  handleOk();
                }
              })
              .catch((error) => { });
          } else {
            message.warning("请选择方案数据进行定制！");
            return;
          }
        });
      });
    };

    const handleOk = () => {
      data.selectLists.push(data.solveId);
      data.selectLists = data.selectLists.filter((item1) => item1.schemeId);
      let guideData = {
        list: data.selectLists,
        cover: data.formStateFront.detail,
        conclusion: data.formStateFinal.detail,
        source: 2,
      };
      toCombinePage(guideData).then((res) => {
        if (res.code == 200) {
          data.solveId = {};
          data.visible = false;
          emit("close");
        }
      });
    };
    const pageChange = (page, pageSize) => {
      data.currentPage = page;
      if (!data.isAISearch) {
        getList();
      } else {
        // 前端手动分页
        data.tableList = data.AISearchList.slice((data.currentPage - 1) * data.pageItemSize, data.currentPage * data.pageItemSize)
      }
    };
    const sizeChange = (current, size) => {
      data.pageItemSize = size;
      if (!data.isAISearch) {
        getList();
      } else {
        // 前端手动分页
        data.tableList = data.AISearchList.slice((data.currentPage - 1) * data.pageItemSize, data.currentPage * data.pageItemSize)
      }
    };
    const changeTab = (val) => {
      data.activeTab = val;
      data.searchType = val;
      console.log('data.searchType', data.searchType)
      getList();
    };
    return {
      ...toRefs(data),
      vocation,
      region,
      formRefFront,
      formRefFinal,
      add,
      outScheme,
      outSolveScene,
      // outProgramme,
      getSceneId,
      contentColor,
      sizeChange,
      contentLeave,
      proDetail,
      router,
      pageChange,
      zhCN,
      pase,

      refuse,
      reLast,
      seekContent,
      submit,
      putIn,
      getId,
      handleOk,
      changeTab,
    };
  },
});
</script>

<style lang="scss" scoped src="./guideTable.scss"></style>

<style lang="scss">
.mypage {
  .ant-pagination {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-select-selector {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-pagination-item-active {
    background: #007eff;
  }

  .ant-pagination-item-active a {
    color: #ffffff;
  }

  .ant-pagination-item-active:focus a,
  .ant-pagination-item-active:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-active a:focus,
  .ant-pagination-item-active a:hover {
    color: #ffffff;
  }

  .ant-pagination-item:focus,
  .ant-pagination-item:hover {
    background: #007eff;
  }

  .ant-pagination-item:focus a,
  .ant-pagination-item:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-disabled:hover,
  .ant-pagination-item-disabled:focus {
    background-color: #ffffff;
    border-color: #d9d9d9;

    a {
      color: rgba(0, 0, 0, 0.25);
    }
  }

  .ant-pagination-options-size-changer.ant-select {
    margin-right: 0;
  }

  .ant-pagination-options-quick-jumper {
    margin-left: 8px;
  }
}

/*.shopping {
  position: absolute;
  right: 4px;
  bottom: 12px;
}*/
</style>