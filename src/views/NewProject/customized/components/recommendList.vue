<template>
  <div class="recommendList flex just-sb align-center">
    <div class="solution">
      <div class="listTitle flex just-sb align-center">
        <div class="titleName">推荐能力</div>
        <div
          class="titleBtn pointer"
          @click="jumpSolution('moduleList', '能力')"
        >
          全部能力 >
        </div>
      </div>
      <div class="solutionList">
        <template v-for="(item, index) in solutionList" :key="index">
          <div
            :class="['card_content']"
            style="
              background: #f5f7fc;
              box-shadow: 0px 0px 24px 0px rgba(116, 157, 219, 0.3);
              margin-bottom: 16px;
            "
          >
            <div style="display: flex; padding: 24px">
              <div>
                <a-image
                  :width="168"
                  :height="105"
                  :preview="false"
                  v-if="item.abilityPicture"
                  :src="`${item.abilityPicture}`"
                />
                <img
                  src="@/assets/images/home/<USER>"
                  style="width: 168px; height: 105px"
                  v-else
                />
              </div>
              <div class="card_center maxWidth" style="margin-left: 24px">
                <div class="card_text">
                  <div class="card_tag">
                    <a-tag style="background-color: green; color: white">{{
                      item.abilityClassify
                    }}</a-tag>
                    <div class="card_title">{{ item.name }}</div>
                  </div>
                  <div class="flex">
                    <div class="cityStyle">
                      <a-button
                        size="small"
                        type="link"
                        @click.stop="jionProject(item.id)"
                      >
                        <img
                          class="add"
                          src=" @/assets/images/AI/isadded.png"
                        />
                        加入定制
                      </a-button>
                    </div>
                  </div>
                </div>
                <div class="card_des">
                  {{ item.abilityIntro }}
                </div>
                <div
                  style="
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                  "
                >
                  <div style="display: flex; align-items: center">
                    <img
                      src="@/assets/images/home/<USER>"
                      style="width: 16px; height: 16px"
                    />
                    <span
                      style="font-size: 12px; color: rgba(0, 0, 0, 0.45)"
                      v-if="item.viewCount"
                      >{{ item.viewCount }}</span
                    >
                    <span v-else>-</span>
                    <img
                      src="@/assets/images/home/<USER>"
                      style="width: 16px; height: 16px; margin-left: 18px"
                    />
                    <span
                      style="font-size: 12px; color: rgba(0, 0, 0, 0.45)"
                      v-if="item.downloadCount"
                      >{{ item.downloadCount }}</span
                    >
                    <span v-else>-</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
    <div class="schemeModule">
      <div class="listTitle flex just-sb align-center">
        <div class="titleName">推荐产品</div>
        <div
          class="titleBtn pointer"
          @click="jumpSolution('productList', '产品')"
        >
          全部产品 >
        </div>
      </div>
      <div class="moduleList">
        <template v-for="(item, index) in moduleList" :key="index">
          <div
            :class="['card_content']"
            style="
              background: #f5f7fc;
              box-shadow: 0px 0px 24px 0px rgba(116, 157, 219, 0.3);
              margin-bottom: 16px;
            "
          >
            <div style="display: flex; padding: 24px">
              <div>
                <a-image
                  :width="168"
                  :height="105"
                  :preview="false"
                  v-if="item.image"
                  :src="`${item.image}`"
                />
                <img
                  src="@/assets/images/home/<USER>"
                  style="width: 168px; height: 105px"
                  v-else
                />
              </div>
              <div class="card_center maxWidth" style="margin-left: 24px">
                <div class="card_text">
                  <div class="card_tag">
                    <a-tag style="background-color: green; color: white">{{
                      item.classifyName
                    }}</a-tag>
                    <div class="card_title">{{ item.name }}</div>
                  </div>
                  <div class="flex">
                    <div class="cityStyle">
                      <a-button
                        size="small"
                        type="link"
                        v-if="item.existsPpt == 1"
                        @click.stop="jionModel(item.id)"
                      >
                        <img
                          class="add"
                          src=" @/assets/images/AI/isadded.png"
                        />
                        加入定制
                      </a-button>
                    </div>
                  </div>
                </div>
                <div class="card_des">
                  {{ item.introduction }}
                </div>
                <div
                  style="
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                  "
                >
                  <div style="display: flex; align-items: center">
                    <img
                      src="@/assets/images/home/<USER>"
                      style="width: 16px; height: 16px"
                    />
                    <span
                      style="font-size: 12px; color: rgba(0, 0, 0, 0.45)"
                      v-if="item.viewCount"
                      >{{ item.viewCount }}</span
                    >
                    <span v-else>-</span>
                    <img
                      src="@/assets/images/home/<USER>"
                      style="width: 16px; height: 16px; margin-left: 18px"
                    />
                    <span
                      style="font-size: 12px; color: rgba(0, 0, 0, 0.45)"
                      v-if="item.downloadCount"
                      >{{ item.downloadCount }}</span
                    >
                    <span v-else>-</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>
<script>
import { defineComponent, reactive, toRefs, ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import eventBus from "@/utils/eventBus";
import { addCombine } from "@/api/combine/combine.js";
import { getProjectList } from "@/api/moduleList/home.js";
import { getProductList } from "@/api/product/home.js";
import { getSceneSchemeList } from "@/api/moduleList/home";
export default defineComponent({
  name: "recommendList",
  components: {},
  emits: ["getList"],

  setup(props, { emit }) {
    const Router = useRouter();
    const Route = useRoute();
    const data = reactive({
      solutionList: [],
      moduleList: [],
    });
    const init = () => {
      getProjectList({
        pageNo: 1,
        pageSize: 3,
        addGroup: false,
      }).then((res) => {
        data.solutionList = res.data.rows;
      });
      getProductList({
        pageNo: 1,
        pageSize: 3,
      }).then((res) => {
        data.moduleList = res.data.rows;
      });
    };
    init();
    //点击按钮， 将方案加入购物车
    const jionProject = (id) => {
      addCombine([
        {
          schemeId: id,
          classify: "1",
        },
      ]).then((res) => {
        init();
        emit("getList");
        eventBus.emit("cartRefresh");
      });
    };
    //点击按钮  将模块加入购物车
    const jionModel = (id) => {
      addCombine([
        {
          schemeId: id,
          classify: "3",
        },
      ]).then((res) => {
        init();
        emit("getList");
        eventBus.emit("cartRefresh");
      });
    };
    const jumpSolution = (url, name) => {
      Router.push({
        name: url,
        tabName: name,
      });
    };
    return {
      ...toRefs(data),
      Route,
      Router,
      jionProject,
      jionModel,
      jumpSolution,
    };
  },
});
</script>

<style lang="scss" scoped src="../components/combine.scss"></style>
<style lang="scss" scoped>
.recommendList {
  margin-top: 36px;

  .solution {
    width: 48.3%;

    .solutionList {
      margin-top: 16px;
    }
  }

  .schemeModule {
    width: 48.3%;

    .moduleList {
      margin-top: 16px;
    }
  }

  .listTitle {
    .titleName {
      color: #24456a;
      font-size: 18px;
      font-weight: 500;
    }

    .titleBtn {
      width: 106px;
      height: 32px;
      line-height: 32px;
      background: rgba(30, 99, 255, 0.06);
      color: #1e63ff;
      text-align: center;
      font-weight: 500;
    }
  }
}

.card_text {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .card_tag {
    display: flex;
    align-items: center;
  }

  .card_title {
    font-weight: bold;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.85);
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  :deep(.cityStyle) {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
    background-color: transparent;

    .add-icon {
      width: 16px;
    }

    .deleteGroup {
      font-weight: 500;
      font-size: 12px;
      color: #0c70eb;
    }
  }
}

.card_des {
  height: 48px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  /* 控制显示的行数 */
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
}

.add {
  width: 16px;
  margin-bottom: 3px;
  margin-right: 6px;
  margin-left: -6px;
}

:deep(.ant-tag) {
  font-weight: 500;
  font-size: 12px;
  color: #2e7fff;
  height: 20px;
  display: flex;
  align-items: center;
}
</style>
