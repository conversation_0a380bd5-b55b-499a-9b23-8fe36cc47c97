.box {
    // background-color: #F5F7FC;
    width: 100%;
    height: 100%;
    // padding-top: 24px;
}

// .line {
//     height: 2px;
//     background-color: #F5F5F5;
//     margin-bottom: 28px;
// }

.con {
    width: 1200px;
    margin: 0 auto;

    .tit {
        font-weight: 500;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 22px;
        margin-bottom: 10px;
    }

    .top {
        .img_box {
            margin-bottom: 16px;
            justify-content: space-between;

            .tit {
                width: 135px;
                height: 19px;
                margin-right: 64px;
                margin-bottom: 0;
            }

            .guid {
                width: 102px;
                height: 32px;
                background: linear-gradient(270deg, #0142FD 0%, #2475F9 100%);
                border-radius: 4px 4px 4px 4px;
                font-weight: 500;
                font-size: 14px;
                color: #FFFFFF;
                line-height: 20px;
                padding: 6px 16px;
                cursor: pointer;

            }

            .left_img {
                width: 14px;
                height: 8px;
                margin-right: 5px;
            }

            .right_img {
                width: 138px;
                height: 19px;
            }
        }

        .top_form {
            background-color: #FFFFFF;
            padding: 24px 40px;

            .top_tit {}

        }
    }

    .content {
        // margin-top: 24px;
        padding: 24px 40px;
        background-color: #FFFFFF;
        border-radius: 8px 8px 8px 8px;

    }
}

.foot {
    width: 1200px;
    background-color: #FFFFFF;
    padding: 0 24px;
    margin: 0 auto;
    height: 88px;
    margin-top: 24px;
    display: flex;
    justify-content: space-between;
    font-weight: 500;
    font-size: 16px;
    align-items: center;
    color: #0C70EB;

    .left_btn {
        .delete {
            cursor: pointer;
            margin-right: 24px;
            margin-left: 24px;
        }

        .btn {
            background: rgba(12, 112, 235, 0.08);
            border-radius: 4px 4px 4px 4px;
            padding: 9px 24px;
            line-height: 14px;
            margin-right: 24px;
            color: #0c70eb;
            border: none;
        }

        .btn {
            height: 36px;
            border-radius: 4px;
            padding: 4px 24px;
            border: none !important;
            color: #0c70eb;
            text-align: center;
            background: #0c70eb14;
            font-weight: 500;
            font-size: 16px;
            color: #0C70EB;
            line-height: 22px;
            text-align: center;
        }
    }

}

.right_con {
    display: flex;
    font-weight: 400;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
    line-height: 16px;
    align-items: center;

    .tips {
        text-align: right;

        p {
            margin-bottom: 0;
        }
    }

    .money {
        font-weight: bold;
        font-size: 24px;
        color: #FF5B00;
        line-height: 28px;
    }

    .right_btn {
        margin-left: 28px;
        background: linear-gradient(270deg, #0142FD 0%, #2475F9 100%);
        border-radius: 4px 4px 4px 4px;
        font-weight: 500;
        font-size: 16px;
        color: #FFFFFF;
        line-height: 22px;
        width: 128px;
        height: 40px;
        text-align: center;
        padding-top: 8px;
        cursor: pointer;
    }
    .btn {
            height: 36px;
            border-radius: 4px;
            padding: 4px 24px;
            border: none !important;
            color: #0c70eb;
            text-align: center;
            background: #0c70eb14;
            font-weight: 500;
            font-size: 16px;
            color: #0C70EB;
            line-height: 22px;
            text-align: center;
        }
}

.listContent {
    margin: 0 auto;
    margin-top: 20px;
    padding: 0 24px;
    width: 1200px;
    height: 126px;
    background: #FFFFFF;
    box-shadow: 0px 4px 4px 0px rgba(176, 204, 230, 0.15);
    border-radius: 8px 8px 8px 8px;
    margin-bottom: 20px;

    .line {
        padding: 12px 0;
        border-bottom: 1px solid rgba(0, 6, 14, 0.08);
    }

    .title {
        font-weight: 500;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);

        .sub-count {
            display: inline-block;
            margin-left: 12px;
            font-weight: 400;
            font-size: 14px;
            color: #00060E;
        }
    }
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.7);
    /* 半透明遮罩 */
    z-index: 9999;
}

:deep(.ant-form-item-label > label) {
    // color: #797C80 !important;
}

.addPro {
    text-align: center;
    margin-top: 8px;

    span {
        font-weight: 500;
        font-size: 14px;
        color: #0C70EB;
        line-height: 22px;
        cursor: pointer;
    }
}

:deep(.ant-form-item-label) {
    // width: 80px;
    text-align: right !important;
}

.manual {
    display: flex;
    flex-flow: column;
    justify-content: space-around;
    align-items: center;
    cursor: pointer;
    padding: 10px;
    width: 260px;
    height: 180px;
    background: linear-gradient(135deg, #B1FAFF 0%, #A4CBFF 100%);
    border-radius: 8px;
    opacity: 0.7;

    >img {
        width: 50px;
        height: 45px;
    }

    >div:nth-child(2) {
        font-weight: bold;
        font-size: 18px;
        color: #0176F5;
    }

    >div:last-child {
        font-weight: 400;
        font-size: 14px;
        color: #0176F5;
    }
}

.ai {
    display: flex;
    cursor: pointer;
    flex-flow: column;
    justify-content: space-around;
    align-items: center;
    padding: 10px;
    width: 260px;
    height: 180px;
    background: linear-gradient(135deg, #D2B1FF 0%, #A4CBFF 100%);
    border-radius: 8px;
    opacity: 0.7;

    >img {
        width: 50px;
        height: 45px;
    }

    >div:nth-child(2) {
        font-weight: bold;
        font-size: 18px;
        color: #6B18FF;
    }

    >div:last-child {
        font-weight: 400;
        font-size: 14px;
        color: #6B18FF;
    }
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.7);
    /* 半透明遮罩 */
    z-index: 9999;
}

.loading-super {
    position: fixed;
    top: 20vh;
    left: 20vw;
    width: 60vw;
    height: 60vh;
    z-index: 9999;
}