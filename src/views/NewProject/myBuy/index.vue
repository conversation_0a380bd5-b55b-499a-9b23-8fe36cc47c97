<template>
  <div class="box">
    <div class="loading-overlay" style="z-index:1003;" v-if="loadShow">
      <a-spin :spinning="loadShow"></a-spin>
    </div>
    <div class="loading-overlay" v-if="loadingShow">
      <div class="loading-super">
        <superLoading />
      </div>
    </div>
    <!-- <div class="con"> -->
    <div class="listContent">
      <div class="flex just-sb line">
        <div class="title">
          封面
          <span class="sub-count"></span>
        </div>
        <div class="right_con flex right">
          <a-button class="btn" @click="born('down')" style="margin-right: 12px">
            <span>生成方案</span>
          </a-button>
          <a-button class="btn" @click="born('view')">
            <span>生成产品单</span>
          </a-button>
        </div>
      </div>
      <a-form :model="formData1" class="formRef1" ref="formRef1">
        <a-form-item label="封面标题" name="title" :rules="[
          { required: true, message: '请输入封面标题', trigger: 'blur' },
        ]">
          <a-input class="no-border" v-model:value="formData1.title" placeholder="请输入封面标题"></a-input>
        </a-form-item>
      </a-form>
    </div>
    <!-- </div> -->
    <div class="con">
      <!-- <div class="content"> -->
      <!-- <div class="img_box flex" v-if="showTop">
          <div>
            <img
              class="left_img"
              src="@/assets/images/buyList/left.png"
              alt=""
            />
            <img
              class="right_img"
              src="@/assets/images/buyList/box.png"
              alt=""
            />
          </div>
          <span class="guid" @click="showGuid('1')">新产品订购</span>
        </div> -->
      <!-- <div class="top_form">
          <span class="top_tit">基础信息</span>
          <div class="line"></div>
          <a-form ref="formRefTop" :model="formState" :rules="rules">
            <a-row>
              <a-col :span="11" style="margin-right: 3%"
                ><a-form-item
                  ref="enterpriseName"
                  label="企业名称"
                  name="enterpriseName"
                >
                  <a-input
                    v-model:value="formState.enterpriseName"
                    placeholder="请输入企业名称"
                    maxlength="20"
                  /> </a-form-item
              ></a-col>
              <a-col :span="12"
                ><a-form-item
                  ref="enterpriseScale"
                  label="企业规模"
                  name="enterpriseScale"
                >
                  <a-input
                    v-model:value="formState.enterpriseScale"
                    placeholder="请输入企业规模"
                    maxlength="10"
                  /> </a-form-item
              ></a-col>
            </a-row>
            <a-row>
              <a-col :span="11" style="margin-right: 3%">
                <a-form-item
                  ref="customerName"
                  label="联系人"
                  name="customerName"
                >
                  <a-input
                    v-model:value="formState.customerName"
                    placeholder="请输入联系人"
                    maxlength="10"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  ref="contactNumber"
                  label="联系电话"
                  name="contactNumber"
                >
                  <a-input
                    v-model:value="formState.contactNumber"
                    placeholder="请输入客户电话"
                    maxlength="11"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="11" style="margin-right: 3%">
                <a-form-item ref="address" label="联系地址" name="address">
                  <a-input
                    v-model:value="formState.address"
                    placeholder="请输入联系地址"
                    maxlength="20"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="所属行业" name="industry">
                  <a-select
                    placeholder="请选择所属行业"
                    v-model:value="formState.industry"
                    style="width: 480px"
                  >
                    <template v-for="(opt, index) in typeList" :key="index">
                      <a-select-option :value="opt.name">
                        {{ opt.name }}
                      </a-select-option>
                    </template>
</a-select>
</a-form-item>
</a-col>
</a-row>
</a-form>
</div> -->
      <!-- </div> -->
      <div class="content" style="padding-top: 10px">
        <a-tabs v-model:value="sourceType" @change="tabsChange">
          <a-tab-pane key="1" tab="需求方案维度" force-render></a-tab-pane>
          <a-tab-pane key="2" tab="产品分类维度" force-render></a-tab-pane>
        </a-tabs>
        <div class="content_box">
          <span class="con_tit tit">产品列表</span>
        </div>
        <a-table v-if="tableData && tableData.length > 0" :columns="tableJson" :data-source="tableData"
          :pagination="pagination" childrenColumnName="productLists" :defaultExpandAllRows="true"
          :expandIcon="expandIcon" :rowKey="(record) => {
            return record.demendId
              ? record.demendId == null
                ? -1
                : record.demendId
              : record.id;
          }
            " :key="tablekey">
          <template #selectionsHeader>
            <a-checkbox :indeterminate="tableIndeter" :checked="isAllCheck" @change="tableAllcheck" />
          </template>
          <template #selections="{ record, index }">
            <a-checkbox v-if="!record.productId" :indeterminate="record.indeterminate"
              @change="demandChange($event, record, index)" :checked="record.demendCheck" />
          </template>
          <template #productName="{ record, text, index }">
            <a-tooltip placement="topLeft">
              <template v-if="isShowToolTip(text, 10)" #title>
                {{ record.productName ? record.productName : record.name ? record.name : record.classifyName && record.classifyName == '通用产品' ? '补充产品' : record.classifyName }}
              </template>
              <a-checkbox :checked="record.selectTableRow" style="margin-right: 20px" v-if="record.productName"
                @change="proChange($event, record)"></a-checkbox>
              <span class="font_F51D0F margin_r_12" style="cursor: pointer" @click="goDetail(record)">
                {{ record.productName ? record.productName : record.name ? record.name : record.classifyName && record.classifyName == '通用产品' ? '补充产品' : record.classifyName }}
              </span>
            </a-tooltip>
            <!-- <span class="font_F51D0F margin_r_12" style="cursor: pointer;" @click="goDetail(record)">
              {{record.productName ? record.productName : record.name}}
            </span> -->
          </template>
          <template #productDesc="{ record, text }">
            <a-tooltip placement="topLeft">
              <template v-if="isShowToolTip(text, 10)" #title>{{
                text
              }}</template>
              {{ text }}
            </a-tooltip>
          </template>
          <template #productTariffList="{ record, text }">
            <span>
              <a-select v-model:value="record.productType" @change="(value) => handleSpecChange(value, record)">
                <template v-for="(opt, key) in text" :key="opt.id">
                  <a-select-option :value="opt.id">
                    <a-tooltip placement="topLeft">
                      <template v-if="isShowToolTip(opt.specification, 10)" #title>{{ opt.specification }}</template>
                      {{ opt.specification }}
                    </a-tooltip>
                  </a-select-option>
                </template>
              </a-select>
            </span>
          </template>
          <template #price="{ record, text }">
            <div style="
                font-weight: 400;
                font-size: 12px;
                color: rgba(0, 0, 0, 0.65);
                line-height: 14px;
              ">
              <span>原价：</span>
              <span style="
                  font-weight: 500;
                  font-size: 14px;
                  color: rgba(0, 0, 0, 0.85);
                  line-height: 16px;
                ">{{ text }}</span>
              <span>{{ unit }}</span>
            </div>
          </template>
          <template #productQuantity="{ record, text }">
            <a-input-number v-if="record.productName" v-model:value="record.productQuantity"
              :formatter="(value) => `${value}`" min="1" :precision="0" :parser="(value) => value.replace('.', '')" />
          </template>
          <template #action="{ record, text }">
            <a-popconfirm title="确定删除当前产品?" ok-text="确定" cancel-text="取消" @confirm="deletePro(record, '1')">
              <span class="font_F51D0F margin_r_12" style="cursor: pointer; color: #0c70eb">删除</span>
            </a-popconfirm></template>
        </a-table>
        <div class="addPro" id="addPro">
          <span @click="showGuid('2')">点击添加产品</span>
        </div>
      </div>
      <!-- <div class="content">
        <div class="top_form">
          <span class="top_tit">客户经理信息</span>
          <div class="line"></div>
          <a-form ref="formRef" :model="formState" :rules="rules">
            <a-row>
              <a-col :span="11" style="margin-right: 3%">
                <a-form-item
                  ref="customerManager"
                  label="客户经理"
                  name="customerManager"
                >
                  <a-input
                    v-model:value="formState.customerManager"
                    placeholder="请输入客户经理名称"
                    maxlength="10"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  ref="managerNumber"
                  label="联系电话"
                  name="managerNumber"
                >
                  <a-input
                    v-model:value="formState.managerNumber"
                    placeholder="请输入客户经理电话"
                    maxlength="11"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="11" style="margin-right: 3%">
                <a-form-item
                  ref="contractDate"
                  label="生成日期"
                  name="contractDate"
                >
                  <a-date-picker
                    v-model:value="formState.contractDate"
                    placeholder="请选择生成日期"
                    :value-format="'YYYY-MM-DD'"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </div> -->
      <!-- <div class="content">
        <a-form :model="formData1" ref="formRef2">
          <a-form-item
            label="结束语:"
            name="conclusion"
            :rules="[
              { required: true, message: '请输入结束语', trigger: 'blur' },
            ]"
          >
            <a-textarea
              v-model:value="formData1.conclusion"
              placeholder="请输入结束语"
              :rows="1"
            />
          </a-form-item>
        </a-form>
      </div> -->
      
      <div class="listContent scrollable-content">
        <div class="flex just-sb line">
          <div class="title">结束语<span class="sub-count"></span></div>
        </div>
        <a-form :model="formData1" ref="formRef2" class="formRef2">
          <a-form-item label="结束语:" name="conclusion" :rules="[
            { required: true, message: '请输入结束语', trigger: 'blur' },
          ]">
            <a-textarea v-model:value="formData1.conclusion" placeholder="请输入结束语" :rows="1" />
          </a-form-item>
        </a-form>
      </div>
    </div>
    <div class="foot">
      <div class="left_btn">
        <!-- <a-checkbox v-model:checked="checkedStatus" @click="checkedAll"
          >全选</a-checkbox
        > -->
        <a-button type="primary" class="btn" @click="deletePro">
          <span>清除</span>
        </a-button>
        <!--<a-button
          type="primary"
          class="btn"
          :loading="loading"
          @click="holdData"
          style="cursor: pointer"
          >保存</a-button
        >-->
      </div>
      <div class="left_btn flex">
        <!-- <div class="right_con flex"> -->
        <a-button class="btn" type="primary" @click="born('down')" style="margin-right: 12px">
          <span>生成方案</span>
        </a-button>
        <a-button class="btn" type="primary" @click="born('view')" style="margin-right: 0">
          <span>生成产品单</span>
        </a-button>
        <!-- </div> -->
      </div>
    </div>
    <a-modal :visible="previewVisible" @cancel="colseModel" :title="title" :width="modalWidth" :maskClosable="false"
      :footer="null">
      <templete #title>
        <img src="@/assets/images/combine/title.png" alt="" style="width: 14px; height: 8px" />
      </templete>
      <div v-if="modelType == '1'">
        <guide-table @close="close"></guide-table>
      </div>
      <div v-if="modelType == '3'">
        <a-form :model="formData" labelAlign="right" ref="groupForm">
          <a-row>
            <a-col :span="24">
              <a-form-item label="定制名称" name="name" :rules="[{ required: true, message: '请输入定制名称' }]">
                <a-input v-model:value="formData.name" placeholder="请输入定制名称">
                </a-input>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
        <div class="flex just-center">
          <a-button style="margin-right: 20px" @click="close">取消</a-button>
          <a-button type="primary" @click="submit()">
            <span>提交</span>
          </a-button>
        </div>
      </div>
      <div v-if="modelType == '2'">
        <chose-table :id="id" :parentId="parentId" :nowCustomizeProductBagId="nowCustomizeProductBagId" @close="close"
          @onSubmit="onSubmit" :proList="proList"></chose-table>
      </div>
    </a-modal>
    <a-modal :visible="showTips" @cancel="manualOrAiVisible = false" title="选择场景定制方法" width="634px"
      :destroyOnClose="true" :maskClosable="false" :footer="null" centered :closable="false">
      <templete #header>
        <img src="@/assets/images/home/<USER>" style="width: 14px; height: 8px; margin-right: 10px" />
      </templete>
      <div class="flex just-sb" style="padding: 0 13px 13px">
        <div class="manual" @click="toSelf">
          <img src="@/assets/images/newProject/manual.png" />
          <div>手动定制</div>
          <div>通过场景定制流程添加产品</div>
        </div>
        <div class="ai" @click="toAI">
          <img src="@/assets/images/newProject/aiLogo.png" />
          <div>AI定制</div>
          <div>通过麒麟AI助手智能分析定制场景</div>
        </div>
      </div>
    </a-modal>
  </div>
  <div class="aiBuyDetail">
    <BuyDetail ref="buyDetailRef" @dataSent="changeStatus" :propObj="propObj"></BuyDetail>
  </div>
  <a-modal v-model:visible="showDownloadModal" title="提示" :mask-closable="false" :footer="null" :destroyOnClose="true"
    width="550px">
    <promptBox @downloadModalCancel="downloadModalCancel" @downloadModalConfirm="downloadModalConfirm" />
  </a-modal>
  <a-modal v-model:visible="showDownloadForm" title="新增工单" :mask-closable="false" :footer="null" :destroyOnClose="true"
    width="600px">
    <reviewForm @downloadFormCancel="downloadFormCancel" @downloadFormConfirm="downloadFormConfirm" />
  </a-modal>
  <a-modal v-model:visible="showBornDown" title="文件预览" :mask-closable="false" :footer="null" :destroyOnClose="true"
    width="500px" @cancel="closeShowBornDown">
    <div class="loading-overlay" style="z-index:1003;" v-if="pdfloadShow">
      <a-spin :spinning="pdfloadShow" tip="附件生成中"></a-spin>
    </div>
    <div class="previewProgramBody">
      <div class="previewProgram">
        <div class="fileName" :title="formData1.title+'.pptx'">{{ formData1.title+'.pptx' }}</div>
        <div class="previewProgramBtn">
          <div @click="preview">预览</div>
          <div @click="download">下载</div>
        </div>
      </div>
    </div>
  </a-modal>
</template>
<script>
import {
  defineComponent,
  reactive,
  toRefs,
  ref,
  watch,
  defineEmits,
  onMounted,
} from "vue";
import BasicTable from "@/components/table/table.vue";
import GuideTable from "./compontents/guideTable.vue";
import ChoseTable from "./compontents/choseTable.vue";
import BuyDetail from "./compontents/buyDetail.vue";
import { getTradeList } from "@/api/solutionNew/home";
import { useHomeStore } from "@/store";
import { clearAll, productMerge } from "@/api/AI/ai.js";
import superLoading from "@/components/superLoading/superLoading.vue";
import { useRouter, useRoute } from "vue-router";
import { getNewDownCount } from "../../../api/solutionNew/detail";
import {
  deleteShop,
  holdShop,
  shopTable,
  deleteManyShop,
  viewShop,
  postList,
} from "@/api/buyList/index";
import { isShowToolTip, getDateTime } from "@/utils/index.js";
import eventBus from "@/utils/eventBus";
import { addShopPro,toShopList } from "@/api/buyList/index";
import promptBox from "@/components/promptBox/index.vue"
import reviewForm from "@/components/reviewForm/index.vue"
import { pptTopdf } from '@/api/fileUpload/uploadFile.js'

export default defineComponent({
  name: "buyList",
  components: {
    BasicTable,
    ChoseTable,
    GuideTable,
    BuyDetail,
    superLoading,
    promptBox,
    reviewForm
  },
  props: {
    aiBuy: {
      type: Boolean,
      default: false,
    },
    isBuy: {
      type: Boolean,
      default: false,
    },
    addNew: {
      type: Boolean,
      default: false,
    },
    nowCustomizeProductBagId: {
      type: Number,
      default: null,
    },
  },
  setup(props) {
    const userInfo = JSON.parse(localStorage.getItem("userInfo"));
    const router = useRouter();
    const route = useRoute();
    const buyDetailRef = ref(null);
    const data = reactive({
      sourceType:1,
      pdfloadShow:false,
      pptfilePath:'',
      showBornDown:false,// 生成方案弹窗
      showDownloadModal: false,
      showDownloadForm: false,
      nowCustomizeProductBagId: props.nowCustomizeProductBagId,
      tablekey: 1,
      tableIndeter: false, // indeterminate 状态
      isAllCheck: false, //表格勾选框 全选
      newSelectRows: [],
      newSelectIds: [],
      propObj: {},
      clearAll: false,
      formState: {
        customerName: "",
        contactNumber: "",
        enterpriseScale: "",
        industry: undefined,
        loadShow: false,
        address: "",
        enterpriseName: "",
        customerManager: userInfo.realName,
        managerNumber: userInfo.phone,
        contractDate: getDateTime("date"),
      },
      showTop: false,
      typeList: [],
      proList: [],
      loadingShow: false,
      showTips: props.isBuy,
      formData: {
        name: "",
      },
      formData1: {
        title: "XX客户产品方案",
        conclusion: "谢谢聆听",
      },
      tabData: [],
      aiBuy: props.aiBuy,
      selectIds: [],
      selectList: [],
      modelType: "0",
      loading: false,
      tableJson: [
        {
          // title: '选择',
          dataIndex: "selections",
          slots: { title: "selectionsHeader", customRender: "selections" },
          width: 60,
          align: "center",
        },
        {
          title: "产品名称",
          dataIndex: "productName",
          key: "productName",
          align: "left",
          slots: { customRender: "productName" },
          ellipsis: true,
          width: 190,
        },
        {
          title: "产品介绍",
          dataIndex: "productDesc",
          key: "productDesc",
          align: "left",
          ellipsis: true,
          slots: { customRender: "productDesc" },
          width: 380,
        },
        // {
        //   title: "产品数量",
        //   dataIndex: "productQuantity",
        //   key: "productQuantity",
        //   align: "left",
        //   ellipsis: true,
        //   slots: { customRender: "productQuantity" },
        //   width: 180,
        // },
        {
          title: "操作",
          dataIndex: "action",
          align: "left",
          width: 100,
          slots: { customRender: "action" },
        },
      ],
      previewVisible: false,
      title: "",
      type: "",
      id: undefined,
      modalWidth: "1200px",
      choseType: null,
      parentId: null,
      tableData: [],
    });
    const rules = {
      customerName: [
        { required: true, message: "请输入联系人", trigger: "blur" },
      ],
      contactNumber: [
        { required: true, message: "请输入联系电话", trigger: "blur" },
        { pattern: /^1(3|4|5|6|7|8|9)\d{9}$/, message: "请正确输入手机号码" },
      ],
      enterpriseName: [
        { required: true, message: "请输入企业名称", trigger: "blur" },
      ],
    };
    const counterStore = useHomeStore();
    const mergeDuplicates = (arr) => {
      const map = {};
      const result = [];
      for (const item of arr) {
        const id = item.productId;
        if (id in map) {
          result[map[id]].productQuantity += item.productQuantity;
        } else {
          map[id] = result.length;
          result.push({ ...item });
        }
      }
      return result;
    };
    const showGuid = (e) => {
      data.modelType = e;
      data.previewVisible = true;
      data.modalWidth = "1200px";
      if (e == "1") {
        data.title = "引导";
      } else if (e == "2") {
        data.title = "添加产品";
        holdTable();
      }
    };
    const holdTable = () => {
      counterStore.aiBuyListStroe = {
        ...data.formState,
        ...data.formData1,
      };
      let productPackageLists = [];
      data.tableData.forEach((ele) => {
        ele.productLists.forEach((item) => {
          productPackageLists.push({
            productQuantity: item.productQuantity,
            productId: item.productId,
            demandId: item.demandId,
          });
        });
      });
      console.log(productPackageLists);
      // data.tableData.forEach((ele) => {
      //   let foundMatch = false;
      //   let tariffId = "";
      // ele.productTariffList.forEach((val) => {
      //   if (ele.productType === val.id) {
      //     tariffId = val.id;
      //     foundMatch = true;
      //   }
      // });
      // if (ele.productType == "") {
      //   foundMatch = true;
      //   tariffId = "";
      // }
      // if (foundMatch) {
      // productPackageLists.push({
      //   productQuantity: ele.productQuantity,
      //   productId: ele.productId,
      //   tariffId: tariffId,
      // });
      // }
      // });
      let params = {
        productPackageLists: productPackageLists,
      };
      data.proList = params.productPackageLists;
      counterStore.productList = data.proList;
    };
    const close = () => {
      data.previewVisible = false;
      if (data.modelType == "3") {
        groupForm.value.resetFields();
      }
      if (data.modelType == "1") {
        data.showTips = false;
        setTimeout(() => {
          getList();
        }, 1000);
      }
    };
    const colseModel = () => {
      data.previewVisible = false;
    };
    watch(
      () => props.aiBuy,
      (newV) => {
        data.aiBuy = newV;
        if (newV) {
          getList();
          contral();
        }
      }
    );
    watch(
      () => props.addNew,
      (newVal) => {
        counterStore.productList = [];
        data.tableData.forEach((val) => {
          val.productLists.forEach((item) => {
            counterStore.productList.push({
              productQuantity: item.productQuantity,
              productId: item.productId,
              tariffId: item.tariffId,
              demandId: item.demandId,
            });
          });
        });
        counterStore.aiBuyListStroe = {
          ...data.formState,
          ...data.formData1,
        };
        counterStore.parentId = data.parentId;
      }
    );
    const onSubmit = () => {
      data.previewVisible = false;
      if (data.modelType == "3") {
        groupForm.value.resetFields();
      }
      setTimeout(() => {
        getList();
      }, 1000);
    };
    if (counterStore.showTop == true) {
      (data.previewVisible = true), (data.modelType = "1");
    }
    watch(
      () => counterStore.showTop,
      (newVal) => {
        data.showTop = newVal;
        if (data.showTop == true) {
          (data.previewVisible = true), (data.modelType = "1");
        }
      }
    );
    const getDate = () => {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, "0");
      const day = String(now.getDate()).padStart(2, "0");
      data.formState.contractDate = `${year}-${month}-${day}`;
    };
    const getTypeList = () => {
      getTradeList()
        .then((res) => {
          data.typeList = res.data;
        })
        .catch((error) => {
          console.error("获取行业列表失败：", error);
        });
    };
    getTypeList();
    const formRef = ref();
    const formRefTop = ref();
    const holdData = () => {
      // formRefTop.value.validate().then(() => {
      if (data.selectIds.length === 0) {
        ElMessage.warning("请选择产品");
        return;
      } else {
        data.previewVisible = true;
        data.modalWidth = "900px";
        data.modelType = "3";
        data.title = "定制名称";
      }
      // });
    };
    onMounted(() => {
      contral();
    });
    const changeStatus = (e) => {
      data.loadingShow = e;
    };
    const contral = () => {
      shopTable(data.sourceType).then((res) => {
        if (res.data.productPackageLists.length == 0 && res.data.productClassifyLists.length == 0) {
          // data.showTips = true;
          toSelf();
        } else {
          data.showTips = false;
        }
      });
    };

    const getList = () => {
      data.loadShow = true;
      delete counterStore.aiBuyListStroe.productPackageLists;
      shopTable(data.sourceType).then((res) => {
        data.loadShow = false;
        let packageList = []
        if(data.sourceType == 1){
          packageList = res.data.productPackageLists
        }else{
          packageList = res.data.productClassifyLists
        }
        packageList.forEach((item) => {
          item.productLists = mergeDuplicates(item.productLists);
        });
        data.tableData = packageList || [];
        data.isAllCheck = false;
        data.tableData.forEach((item) => {
          item.demendCheck = false;
          item.indeterminate = false;
          if (item.productLists) {
            item.productLists.forEach((pitem) => {
              pitem.demandName = data.sourceType == 1 ? item.name : item.classifyName
              pitem.selectTableRow = false;
            });
          }
        });
        console.log("data", data.tableData);
        data.id = res.data.id;
        data.formState = {
          ...res.data,
          customerManager: userInfo.realName,
          managerNumber: userInfo.phone,
          contractDate: getDateTime("date"),
        };
        data.parentId = res.data.id;
        let title = "XX客户产品方案";
        let conclusion = "谢谢聆听";

        if (
          counterStore.aiBuyListStore &&
          counterStore.aiBuyListStore.title != null
        ) {
          title = counterStore.aiBuyListStore.title;
        }
        if (
          counterStore.aiBuyListStore &&
          counterStore.aiBuyListStore.conclusion != null
        ) {
          conclusion = counterStore.aiBuyListStore.conclusion;
        }

        if (res.data && res.data.title != null) {
          title = res.data.title;
        }
        if (res.data && res.data.conclusion != null) {
          conclusion = res.data.conclusion;
        }

        if (data.clearAll) {
          title = "XX客户产品方案";
          conclusion = "谢谢聆听";
          data.clearAll = false;
        }

        data.formData1.title = title;
        data.formData1.conclusion = conclusion;
        const ids = data.tableData.map((item) => item.id);
        selectedRowKeys.value = ids;
        console.log(ids, "ids");

        data.selectIds = ids;
        data.selectList = data.tableData;
        data.tabData = data.tableData;
        data.tablekey++;
      });
    };
    const conShowData = () => {
      setTimeout(() => {
        getList();
      }, 1000);
    };
    eventBus.on("buyListRefresh", conShowData);

    const handleSpecChange = (id, record) => {
      const matchedItem = record.productTariffList.find(
        (item) => item.id === id
      );
      if (matchedItem) {
        if (matchedItem.tariff >= 8888) {
          record.price = "以具体业务定价为准";
        } else {
          record.price = `${matchedItem.tariff}${matchedItem.unit}`;
        }
        record.productType = matchedItem.id;
        record.tariffId = matchedItem.id;
      }
    };
    const comfirmDelete = (e) => {
      deleteShop([{ productId: e, type: 2 }]).then((res) => {
        getList();
      });
    };
    getList();
    const selectedRowKeys = ref([]);
    const onSelectionChange = (selectedKeys, selectedRows) => {
      selectedRowKeys.value = selectedKeys;
      data.selectIds = selectedKeys;
      data.selectList = selectedRows;
      data.tabData = selectedRows;
      console.log(selectedRows, "selectedRows");
      console.log(selectedKeys, "selectedKeys");
    };

    const deletePro = async (value, type) => {
      counterStore.aiBuyListStroe = {
        ...data.formState,
        ...data.formData1,
      };
      console.log(value);
      // return
      try {
        if (type === "1") {
          if (value.productLists) {
            const params = value.productLists.map((item) => {
              return item.id;
            });
            await deleteManyShop(params.join(","));
            getList();
            return false;
          }
          await deleteManyShop(value.id);
          //data.selectIds = data.selectIds.filter((item) => item != value.id);
          data.newSelectRows = data.newSelectRows.filter(
            (item) => item.productId != value.productId
          );
          // data.tableData = data.tableData.filter((item) => item.id != value.id);
          let params = [];
          data.tableData.forEach((item) => {
            item.productLists.forEach((item1) => {
              params.push({
                productQuantity: item1.productQuantity,
                productId: item1.productId,
                tariffId: item1.tariffId,
                demandId: item1.demandId,
              });
            });
          });
          params = params.filter((item) => item.productId != value.productId);
          let setData = {
            productPackageLists: params,
          };
          // toShopList addShopPro
          addShopPro(setData).then((res) => {
            getList();
          });
        } else if (data.newSelectRows.length > 0) {
          const params = data.newSelectRows.map((item) => {
            return item.id;
          });
          let allTable = [];
          data.tableData.forEach((ele) => {
            ele.productLists.forEach((item) => {
              allTable.push(item);
            });
          });
          if (allTable.length == data.newSelectRows.length) {
            (data.formData1.title = ""),
              (data.clearAll = true),
              (data.isAllCheck = false);
            await clearAll();
            counterStore.aiBuyListStroe.title = "";
          } else {
            await deleteManyShop(params.join(","));
          }
          data.newSelectRows = [];
          // clearAll().then((res) => {});
          getList();
        } else {
          ElMessage.warning("请选择产品");
          return false;
        }
      } catch (error) {
        console.error("Error deleting products:", error);
      }
    };
    const formRef1 = ref();
    const formRef2 = ref();
    const closeShowBornDown = () => {
      data.pptfilePath = ''
      data.showBornDown = false
    }
    const preview = () => {
      data.pdfloadShow = true
      pptTopdf({
        // filePath: data.pptfilePath,
        fileUrl: data.pptfilePath,
      }).then((res) => {
        data.pdfloadShow = false
        if (res.code == 200) {
          let windowOrigin = window.location.origin;
			    let token = localStorage.getItem("token");
          let newHref = res.data;
          if(res.data.includes(windowOrigin)){
      	    newHref = "/portal" + res.data.split(windowOrigin)[1]
          }
          const newpage = router.resolve({
            name: "lookPdf",
            query: {
              urlMsg: encodeURIComponent(windowOrigin + newHref + "?token=" + token),
              urlName: data.formData1.title,
            },
          });
          window.open(newpage.href, "_blank");
        }
      });
    }
    const download = () => {
      let windowOrigin = window.location.origin;
			let token = localStorage.getItem("token");
			window.open(windowOrigin + data.pptfilePath + "?token=" + token);
    }
    const born = (type) => {
      getNewDownCount({
        businessId: null,
        businessType: null,
      }).then((res) => {
        if (res.code == 200) {
          if (res.data) {
            const tabData = [];
            data.newSelectRows.forEach((item) => {
              console.log('item',item);
              
              tabData.push(item);
            });
            // console.log('tabData',tabData);
            // return
            counterStore.aiBuyListStroe = {};
            counterStore.aiBuyListStroe = {
              ...data.formState,
              productPackageLists: tabData,
              productClassifyLists:tabData,
              title: data.formData1.title,
              conclusion: data.formData1.conclusion,
            };
            counterStore.aiBuyListStroe.productPackageLists = mergeDuplicates(
              counterStore.aiBuyListStroe.productPackageLists
            );
            data.propObj = {
              ...data.formState,
              productPackageLists: tabData,
              ...data.formData1,
            };
            if (data.newSelectRows.length === 0) {
              ElMessage.warning("请选择产品");
              return;
            } else if (type == "view") {
              window.open(window.location.origin + `/#/aiBuyDetail?sourceType=${data.sourceType}&title=${data.formData1.title}`, "_blank");
            } else {
              formRef1.value.validate().then(() => {
                formRef2.value.validate().then(() => {
                  data.loadingShow = true;
                  let dataList = tabData;
                  let arr = [];
                  dataList.forEach((item) => {
                    if(data.sourceType == 1){
                      arr.push({
                        id: item.productId,
                        demandId: item.demandId,
                        name: item.productName,
                        demandName:item.demandName,
                        introduction: item.productDesc,
                      });
                    }else{
                      arr.push({
                        id: item.productId,
                        classifyId: item.productClassify*1,
                        name: item.productName,
                        classifyName:item.demandName,
                        introduction: item.productDesc,
                      });
                    }
                  });
                  let params = {
                    productList: arr,
                    title: data.formData1.title,
                    conclusion: data.formData1.conclusion,
                    dimension:data.sourceType
                  };
                  productMerge(params).then((res) => {
                    if (res.code == 200) {
                      console.log('res',res);
                      data.pptfilePath = res.data.pptFile
                      data.loadingShow = false;
                      data.showBornDown = true
                      return
                    	let windowOrigin = window.location.origin;
											let token = localStorage.getItem("token");
											let newHref = res.data.pptFile;
								      if(res.data.pptFile.includes(windowOrigin)){
								      	newHref = "/portal" + res.data.pptFile.split(windowOrigin)[1]
								      }
										  window.open(windowOrigin + newHref + "?token=" + token);
                      data.loadingShow = false;
                    } else {
                      data.loadingShow = false;
                    }
                  });
                });
              });
            }
          } else {
            data.showDownloadModal = true
          }
        }
      })

    };
    const pagination = {
      defaultPageSize: 100, // 默认每页显示条数
    };
    window.addEventListener("beforeunload", (event) => {
      // 设置returnValue属性可以显示一个提示信息，询问用户是否确实要离开页面
      counterStore.aiBuyListStroe = {
        ...data.formState,
        productPackageLists: data.tabData,
      };
    });

    document.addEventListener("DOMContentLoaded", (event) => {
      // 确保DOM完全加载后执行此操作
      var referenceNode = document.getElementById("addPro");
      if (referenceNode) {
        var newNode = document.createElement("div");
        // 确认参考节点存在后再插入
        referenceNode.parentNode.insertBefore(newNode, referenceNode);
      } else {
        console.error("参考节点不存在，无法插入新节点。");
      }
    });
    const groupForm = ref();
    const submit = () => {
      groupForm.value.validate().then(() => {
        data.loading = true;
        let productPackageLists = data.selectList.map((item) => {
          return {
            productId: item.productId,
            productQuantity: item.productQuantity,
            tariffId: item.tariffId,
          };
        });
        let params = {
          ...data.formState,
          productPackageLists: productPackageLists,
          id: data.id,
          name: data.formData.name,
        };
        holdShop(params).then((res) => {
          if (res.code == 200) {
            ElMessage.success("保存成功,可在后台订购列表查看");
            data.clearAll = true;
            data.selectIds = [];
            data.formData1 = {
              title: "",
              conclusion: "谢谢聆听",
            };
            selectedRowKeys.value = [];
            data.loading = false;
            data.tableData = [];
            data.previewVisible = false;
            data.formData = {
              name: "",
            };
            getList();
          } else {
            data.loading = false;
            data.previewVisible = false;
          }
        });
      });
    };

    const goDetail = (val) => {
      if (val.productId) {
        window.open(
          window.location.origin +
          "/#/product/productDetail?id=" +
          val.productId
        );
      }
      return false;
      router.push({
        query: {
          id: val.productId,
        },
        name: "productDetail",
      });
    };

    const toSelf = () => {
      data.previewVisible = true;
      data.modelType = 1;
      data.showTips = false;
      counterStore.aiBuyListStroe = {};
    };
    const toAI = () => {
      data.showTips = false;
    };
    getDate();
    const closeMo = () => {
      data.showTips = false;
    };
    eventBus.on("closeMo", closeMo);

    // 隐藏表格 展开图标
    const expandIcon = () => {
      return "";
    };

    // 表格 勾选框全选
    const tableAllcheck = (e) => {
      // console.log('e',e);
      if (e.target.checked) {
        data.isAllCheck = true;
        data.tableIndeter = false;
        data.newSelectRows = [];
        data.tableData.forEach((item) => {
          item.demendCheck = true;
          item.indeterminate = false;
          if (item.productLists) {
            item.productLists.forEach((citem) => {
              citem.selectTableRow = true;
              data.newSelectRows.push(citem);
            });
          }
        });
      } else {
        data.isAllCheck = false;
        data.tableIndeter = false;
        data.newSelectRows = [];
        data.tableData.forEach((item) => {
          item.demendCheck = false;
          item.indeterminate = false;
          if (item.productLists) {
            item.productLists.forEach((citem) => {
              citem.selectTableRow = false;
            });
          }
        });
      }
    };
    // 需求方案勾选框变化
    const demandChange = (e, record, index) => {
      console.log('e',e);
      
      if (e.target.checked) {
        data.tableData[index].indeterminate = false;
        data.tableData[index].demendCheck = true;
        data.tableData[index].productLists.forEach((item) => {
          item.selectTableRow = true;
          data.newSelectRows.push(item);
        });
        checkAllIndeter();
      } else {
        data.tableData[index].indeterminate = false;
        data.tableData[index].demendCheck = false;
        data.tableData[index].productLists.forEach((item) => {
          item.selectTableRow = false;
          item.demandName = record.name || record.classifyName
          const isIncluded = data.newSelectRows.findIndex(
            (row) => row.id === item.id
          );
          if (isIncluded !== -1) {
            data.newSelectRows.splice(isIncluded, 1);
          }
        });
        checkAllIndeter();
      }
    };

    // 判断需求方案 表头全选 勾选框状态
    const checkDemandIndeter = (index) => {
      let uncheckedList = data.tableData[index].productLists.filter((item) => {
        return item.selectTableRow == false;
      });
      // console.log('uncheckList',uncheckedList);
      if (uncheckedList.length == 0) {
        data.tableData[index].indeterminate = false;
        data.tableData[index].demendCheck = true;
      } else if (
        uncheckedList.length > 0 &&
        uncheckedList.length < data.tableData[index].productLists.length
      ) {
        data.tableData[index].indeterminate = true;
        data.tableData[index].demendCheck = false;
      } else {
        data.tableData[index].indeterminate = false;
        data.tableData[index].demendCheck = false;
      }
    };
    // 判断表头全选 勾选框状态
    const checkAllIndeter = () => {
      let allCheck = data.tableData.filter((item) => {
        // console.log('item',item);
        return item.demendCheck == true;
      });
      let allI = data.tableData.filter((item) => {
        return item.indeterminate == true;
      });
      if (allI.length > 0) {
        data.tableIndeter = true;
        data.isAllCheck = false;
      } else if (
        allI == 0 &&
        allCheck.length > 0 &&
        allCheck.length < data.tableData.length
      ) {
        data.tableIndeter = true;
        data.isAllCheck = false;
      } else if (allI == 0 && allCheck.length == data.tableData.length) {
        data.tableIndeter = false;
        data.isAllCheck = true;
      } else {
        data.tableIndeter = false;
        data.isAllCheck = false;
      }
    };
    const proChange = (e, record) => {
      // console.log('e==',e);
      console.log("record==", record);
      if (e.target.checked) {
        let isIncluded = data.selectList.findIndex((ele) => {
          ele.productLists?.some(
            (item) => item.id === record.id
          );
        });
        let index = ""; //判断需求方案下的产品是否全部勾选
        data.tableData.forEach((item, pindex) => {
          let childindex = item.productLists.findIndex(
            (item) => item.id == record.id
          );
          if (item.productLists && childindex != -1) {
            index = pindex;
            item.productLists[childindex].selectTableRow = true;
          }
        });
        checkDemandIndeter(index);
        checkAllIndeter();
        if (isIncluded == -1) {
          data.newSelectRows.push(record);
          console.log("isIncluded", isIncluded, data.newSelectRows);
        }
      } else {
        let index = ""; //判断需求方案下的产品是否全部勾选
        data.tableData.forEach((item, pindex) => {
          let childindex = item.productLists.findIndex(
            (item) => item.id == record.id
          );
          if (item.productLists && childindex != -1) {
            index = pindex;
            item.productLists[childindex].selectTableRow = false;
          }
        });
        checkDemandIndeter(index);
        checkAllIndeter();
        const targetIndex = data.newSelectRows.findIndex((item) =>
          item.id === record.id
        );
        if (targetIndex > -1) {
          // console.log("targetIndex", targetIndex, data.newSelectRows);
          data.newSelectRows.splice(targetIndex, 1);
        }
      }
    };
    // 下载超限提示弹窗取消按钮
    const downloadModalCancel = () => {
      data.showDownloadModal = false
    }
    // 下载超限提示弹窗确认按钮
    const downloadModalConfirm = () => {
      data.showDownloadModal = false
      data.showDownloadForm = true
    }
    const downloadFormCancel = () => {
      data.showDownloadForm = false
    }
    const downloadFormConfirm = () => {
      data.showDownloadForm = false
    }
  
    const tabsChange = (val) => {
      data.sourceType = val
      data.newSelectRows = []
      data.isAllCheck = false
      data.tableIndeter = false
      getList()
    }

    return {
      ...toRefs(data),
      tabsChange,
      tableAllcheck,
      checkAllIndeter,
      checkDemandIndeter,
      demandChange,
      proChange,
      expandIcon,
      closeShowBornDown,
      preview,
      download,
      born,
      closeMo,
      holdTable,
      toAI,
      toSelf,
      formRef1,
      formRef2,
      changeStatus,
      colseModel,
      pagination,
      getDate,
      groupForm,
      conShowData,
      onSubmit,
      submit,
      deletePro,
      route,
      handleSpecChange,
      counterStore,
      onSelectionChange,
      selectedRowKeys,
      comfirmDelete,
      isShowToolTip,
      rules,
      getList,
      formRefTop,
      showGuid,
      close,
      getTypeList,
      holdData,
      formRef,
      router,
      getDateTime,
      buyDetailRef,
      goDetail,
      mergeDuplicates,
      downloadModalCancel,
      downloadModalConfirm,
      downloadFormCancel,
      downloadFormConfirm,
    };
  },
});
</script>
<style lang="scss" scoped>
.aiBuyDetail {
  position: fixed;
  top: -9999px;
}

.formRef2 {
  margin-top: 12px;

  :deep(.ant-input) {
    border: 1px solid #00000010;
  }

  :deep(.ant-input):focus {
    border: 1px solid #00000010;
    box-shadow: none;
  }

  :deep(.ant-input):hover {
    border: 1px solid #00000010;
  }
}

:deep(.ant-table-pagination.ant-pagination) {
  display: none !important;
}

.formRef1 {
  margin-top: 12px;

  :deep(.ant-input) {
    border: 1px solid #00000010;
  }

  :deep(.ant-input):focus {
    border: 1px solid #00000010;
    box-shadow: none;
  }

  :deep(.ant-input):hover {
    border: 1px solid #00000010;
  }
}

:deep(.ant-select:not(.ant-select-customize-input) .ant-select-selector) {
  border: 1px solid #d9d9d9 !important;
}

:deep(.ant-select) {
  width: 150px;
}
.previewProgramBody{
  display: flex;
  justify-content: center;
  padding-top: 20px;
  .previewProgram{
    width: 250px;
    display: flex;
    justify-content: space-between;
    >.fileName{
      width: 180px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .previewProgramBtn{
      display: flex;
      user-select: none;
      color: rgb(12, 112, 235);
      >div{
        cursor: pointer;
      }
      >div:first-child{
        margin-right: 6px
      }
    }
  }
}
@import "./index.scss";
</style>
