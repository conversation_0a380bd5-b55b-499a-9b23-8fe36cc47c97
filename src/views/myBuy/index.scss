.box {
    // background-color: #F5F7FC;
    width: 100%;
    height: 100%;
    padding-top: 40px;
}

.line {
    height: 2px;
    background-color: #F5F5F5;
    margin-bottom: 28px;
}

.con {
    width: 1200px;
    margin: 0 auto;

    .tit {
        font-weight: 500;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 22px;
        margin-bottom: 10px;
    }

    .top {
        .img_box {
            margin-bottom: 16px;
            justify-content: space-between;

            .tit {
                width: 135px;
                height: 19px;
                margin-right: 64px;
                margin-bottom: 0;
            }

            .guid {
                width: 102px;
                height: 32px;
                background: linear-gradient(270deg, #0142FD 0%, #2475F9 100%);
                border-radius: 4px 4px 4px 4px;
                font-weight: 500;
                font-size: 14px;
                color: #FFFFFF;
                line-height: 20px;
                padding: 6px 16px;
                cursor: pointer;

            }

            .left_img {
                width: 14px;
                height: 8px;
                margin-right: 5px;
            }

            .right_img {
                width: 138px;
                height: 19px;
            }
        }

        .top_form {
            background-color: #FFFFFF;
            padding: 24px 40px;

            .top_tit {}

        }
    }

    .content {
        margin-top: 24px;
        padding: 24px 40px;
        background-color: #FFFFFF;
    }
}
.foot {
    width: 1200px;
    background-color: #FFFFFF;
    padding: 0 42px;
    margin: 0 auto;
    height: 88px;
    margin-top: 24px;
    display: flex;
    justify-content: space-between;
    font-weight: 500;
    font-size: 16px;
    align-items: center;
    color: #0C70EB;

    .left_btn {
        .delete {
            cursor: pointer;
            margin-right: 24px;
            margin-left: 24px;
        }

        .btn {
            background: rgba(12, 112, 235, 0.08);
            border-radius: 4px 4px 4px 4px;
            padding: 9px 24px;
            line-height: 14px;
            margin-right: 24px;
            color: #0c70eb;
            border: none;
        }
    }

    .right_con {
        display: flex;
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        line-height: 16px;
        align-items: center;

        .tips {
            text-align: right;

            p {
                margin-bottom: 0;
            }
        }

        .money {
            font-weight: bold;
            font-size: 24px;
            color: #FF5B00;
            line-height: 28px;
        }

        .right_btn {
            margin-left: 28px;
            background: linear-gradient(270deg, #0142FD 0%, #2475F9 100%);
            border-radius: 4px 4px 4px 4px;
            font-weight: 500;
            font-size: 16px;
            color: #FFFFFF;
            line-height: 22px;
            width: 128px;
            height: 40px;
            text-align: center;
            padding-top: 8px;
            cursor: pointer;
        }
    }
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.7);
    /* 半透明遮罩 */
    z-index: 9999;
}

:deep(.ant-form-item-label > label) {
    color: #797C80 !important;
}

.addPro {
    text-align: center;
    margin-top: 8px;

    span {
        font-weight: 500;
        font-size: 14px;
        color: #0C70EB;
        line-height: 22px;
        cursor: pointer;
    }
}

:deep(.ant-form-item-label) {
    width: 80px;
    text-align: left !important;
}

// :deep(.ant-modal-close-x){
//     width: 34px;
//     line-height: 30px;
// }
.manual {
    display: flex;
    flex-flow: column;
    justify-content: space-around;
    align-items: center;
    cursor: pointer;
    padding: 10px;
    width: 260px;
    height: 180px;
    background: linear-gradient(135deg, #B1FAFF 0%, #A4CBFF 100%);
    border-radius: 8px;
    opacity: 0.7;

    >img {
        width: 50px;
        height: 45px;
    }

    >div:nth-child(2) {
        font-weight: bold;
        font-size: 18px;
        color: #0176F5;
    }

    >div:last-child {
        font-weight: 400;
        font-size: 14px;
        color: #0176F5;
    }
}

.searchInfo {
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 1200px;
    margin: 0 auto;
    padding: 24px 40px 24px 40px;
    transition: 1s;
    position: relative;
    margin-bottom: 24px;

    .switch {
        margin-right: 16px;
        transition: 1s;
        position: absolute;
        left: 20px;
        top: 24px;
        z-index: 1000;

        .AIlogo {
            width: 60px;
            height: 60px;
            background-image: url(@/assets/images/AI/ai.png);
            background-size: 100% 100%;
        }
    }

    .vocationPull {
        background: #FFFFFF;
        // margin: 0 40px;
        display: flex;
        align-items: center;
        border-top-left-radius: 5px;
        border-bottom-left-radius: 5px;
        border-top-right-radius: 5px;
        border-bottom-right-radius: 5px;
    }

    .inputClass {
        align-items: center;
        border: none;
        width: 88%;
        height: 100%;
        box-shadow: none !important;
        padding-left: 60px;
        margin-left: 38px;
    }

    .sendbtn {
        position: absolute;
        top: 40px;
        right: 60px;
    }

    .line {
        width: 1px;
        height: 26px;
        background: #EFF0F4;
    }

    .lines {
        width: 1px;
        height: 56px;
        background: #EFF0F4;
    }

    .AIbtn {
        background: linear-gradient(108deg, #4446FF 0%, #4173FF 50%, #3EB8FF 100%);
    }

    .commonBtn {
        background-color: #0C70EB;
    }

    .changeContentBtn {
        background: linear-gradient(108deg, #8944FF 0%, #415AFF 50%, #5058FF 100%);
    }

    .commonChangeBtn {
        background-color: #6A4BF5;
    }

    .seekInfo {
        font-weight: bold;
        font-size: 20px;
        color: #FFFFFF;
        display: flex;
        align-items: center;
        width: 160px;
        height: 56px;
        cursor: pointer;

        img {
            width: 24px;
            height: 24px;
            margin-left: 20px;
            margin-right: 8px;
        }
    }

    .customized {
        font-weight: bold;
        font-size: 20px;
        color: #FFFFFF;
        width: 160px;
        height: 56px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-top-right-radius: 5px;
        border-bottom-right-radius: 5px;
    }

    .stage {
        margin-left: 44px;
        margin-top: 16px;
    }
}

.AIBg {
    background-image: url("@/assets/images/AI/bac.png");
}

.ai {
    display: flex;
    cursor: pointer;
    flex-flow: column;
    justify-content: space-around;
    align-items: center;
    padding: 10px;
    width: 260px;
    height: 180px;
    background: linear-gradient(135deg, #D2B1FF 0%, #A4CBFF 100%);
    border-radius: 8px;
    opacity: 0.7;

    >img {
        width: 50px;
        height: 45px;
    }

    >div:nth-child(2) {
        font-weight: bold;
        font-size: 18px;
        color: #6B18FF;
    }

    >div:last-child {
        font-weight: 400;
        font-size: 14px;
        color: #6B18FF;
    }
}

.top_style {
    width: 100%;
    display: flex;
    align-items: center;
    margin-bottom: 32px;
}