.banner {
    background-image: url("@/assets/images/solution/detail/banner.png");
    background-size: cover;
    background-repeat: no-repeat;
    width: 100%;
    padding-top: 32px;
    padding-bottom: 16px;
    .top_card {
        display: flex;
        width: 1200px;
        margin: 0 auto;
        .left {
            width: 80%;
            display: inline-block;
            .left_tit {
                display: flex;
                align-items: flex-start;
                p {
                    font-weight: bold;
                    font-size: 24px;
                    color: #2E3852;
                    line-height: 28px;
                    text-align: left;
                    display: inline-block;
                    margin-right: 8px;
                }
                .score {
                    display: flex;
                    width: 140px;
                    height: 30px;
                    background-color: #FFFFFF;
                    border-radius: 15px;
                    box-shadow: 0px 4px 8px 0px rgba(77, 120, 170, 0.1);
                    .scoreIcon {
                        width: 24px;
                        height: 24px;
                        margin: 3px 4px 0 3px;
                        img {
                            width: 100%;
                            height: 100%;
                        }
                    }
                    .scoreTitle {
                        line-height: 30px;
                        font-size: 14px;
                        color: #00000073;
                    }
                    .scoreNum {
                        line-height: 30px;
                        font-size: 18px;
                        color: #FF9C39FF;
                        font-weight: bold;
                    }
                }
                .tag {
                    position: relative;
                    bottom: 3px;
                    background-color: #D7E4FB;
                    font-weight: 500;
                    font-size: 14px;
                    color: #2E7FFF;
                    line-height: 22px;
                    border: none;
                }
            }
            .left_middle {
                margin-top: 12px;
                // position: absolute;
                // width: 100%;
                p {
                    display: inline-block;
                    font-weight: 400;
                    font-size: 14px;
                    color: #84899A;
                    line-height: 22px;
                    text-align: left;
                    margin-right: 25px;
                }
                .info {
                    font-weight: 400;
                    font-size: 16px;
                    color: #2E3852;
                    line-height: 24px;
                    text-align: left;
                    margin-bottom: 12px;
                    display: -webkit-box;
                    -webkit-line-clamp: 3;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
                .bottom1 {
                    display: flex;
                    img {
                        width: 16px;
                        height: 16px;
                        margin-right: 3px;
                    }
                }
                .info_bottom {
                    font-weight: 300;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    p {
                        color: #84899A;
                        max-width: 3000px;
                        min-width: 180px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }
                    .tips {
                        p {
                            display: flex;
                            justify-content: center;
                            align-items: center;
                        }
                    }
                    img {
                        width: 16px;
                        height: 16px;
                        margin-right: 3px;
                    }
                }
            }
            .left_bottom {
                position: absolute;
                bottom: 0;
                font-weight: 300;
                .bottom_tip {
                    p {
                        margin-left: 16px;
                    }
                }
                div {
                    display: inline-block;
                    margin-left: 16px;
                    p {
                        display: inline-block;
                        color: #84899A;
                    }
                    img {
                        display: inline-block;
                        margin-bottom: 3px;
                        margin-right: 4px;
                        width: 16px;
                        height: 16px;
                    }
                }
                .btn {
                    background-color: #1A66FB;
                    color: #E2ECFF;
                    width: 144px;
                    height: 40px;
                    border-radius: 4px 4px 4px 4px;
                    font-weight: bold;
                    font-size: 16px;
                    color: #FFFFFF;
                    line-height: 16px;
                }
            }
        }
        .right {
            display: inline-block;
            margin-left: 20px;
            width: 405px;
            height: 236px;
            img {
                width: 405px;
                height: 236px;
            }
        }
    }
}

.apply_list {
    border: 1px solid #FFFFFF;
    width: 100%;
    .list_top {
        img {
            width: 420px;
            height: 280px;
            // margin-right: 32px;
        }
        .top_right {
            background: rgba(234, 239, 247, 0.1);
            box-shadow: 0px -8px 32px 0px #FFFFFF, inset 0px 8px 24px 0px rgba(223, 228, 237, 0.5);
            border-radius: 4px 4px 4px 4px;
            opacity: 0.8;
            padding: 24px 32px;
            flex: 1;
            height: 280px;
            .tit {
                font-weight: bold;
                font-size: 20px;
                color: #2E3852;
                line-height: 23px;
                margin-bottom: 16px;
                cursor: pointer;
            }
            .con {
                font-weight: 400;
                font-size: 16px;
                color: #2E3852;
                line-height: 28px;
                display: -webkit-box;
                -webkit-line-clamp: 5;
                /* 限制为3行 */
                -webkit-box-orient: vertical;
                overflow: hidden;
                /* 隐藏超出部分 */
                text-overflow: ellipsis;
            }
            .label {
                font-weight: 400;
                background: #D3DFF9;
                border-radius: 2px 2px 2px 2px;
                border: 1px solid #236CFF;
                font-size: 14px;
                color: #236CFF;
                line-height: 25px;
                padding: 2px 8px;
                display: inline-block;
                margin-right: 8px;
                margin-top: 16px;
            }
        }
    }
    .line {
        border: 1px solid #DAE2F5;
        margin: 24px 0;
    }
    .list_function {}
    .name {
        font-weight: bold;
        font-size: 20px;
        color: #2E3852;
        line-height: 23px;
    }
    .list_caseList {}
}

.oneStyle {
    background: linear-gradient(163deg, #F2F5F8 0%, #F6F7F9 38%, #FFFFFF 100%);
    margin-bottom: 28px;
}

.oneStyle:last-child {
    margin-bottom: 0;
}

.add {
    background: linear-gradient(270deg, #0142FD 0%, #2475F9 100%);
    border-radius: 4px 4px 4px 4px;
    font-weight: 500;
    font-size: 14px;
    color: #FFFFFF;
    line-height: 20px;
    text-align: center;
    padding: 6px 16px;
    width: 88px;
    margin-top: 24px;
}

.active {
    color: red;
}

.addCar {
    margin-top: 16px;
    margin-bottom: 16px;
    button {
        background: linear-gradient(270deg, #0142FD 0%, #2475F9 100%);
        border-radius: 4px 4px 4px 4px;
        padding: 6px 16px;
        font-weight: 500;
        font-size: 14px;
        color: #FFFFFF;
        line-height: 20px;
        border: none;
        cursor: pointer;
    }
    .disabled {
        background: #A6A6A6 !important;
        cursor: not-allowed;
    }
}

.info_bottom {
    // width: 80%;
    // display: flex;
    // justify-content: space-between;
    font-weight: 300;
    overflow: hidden;
    text-overflow: ellipsis;
    p {
        color: #84899A;
        // max-width: 219px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    img {
        width: 16px;
        height: 16px;
        margin-right: 3px;
    }
    .white_bac {
        background: #f3f7ff99;
        box-shadow: 0 2px 4px #4d78aa1a;
        border-radius: 26px;
        padding: 5px 16px;
        font-weight: 400;
        font-size: 12px;
        color: #0c213a;
        line-height: 22px;
        span {
            margin-right: 6px;
        }
    }
}

.anchors {
    display: flex;
    // padding-left: 58px;
    justify-content: center;
    :deep(.ant-anchor-ink::before) {
        display: none;
    }
    :deep(.currentActive) {
        a {
            padding-bottom: 8px;
            border-bottom: 2px solid #236CFF;
        }
    }
}

:deep(.ant-anchor-ink) {
    height: 3px !important;
}

:deep(.ant-tabs-tab) {
    font-weight: bold;
    font-size: 20px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 23px;
}

:deep(.ant-tabs-tab-active .ant-tabs-tab) {
    font-size: 20px;
    color: #2E7FFF;
    line-height: 23px;
    .ant-tabs-tab {
        font-weight: bold !important;
    }
}

:deep(.ant-anchor-link) {
    a {
        color: #262626 !important;
    }
}

:deep(.currentActive) {
    a {
        font-weight: bold !important;
        color: #236CFF !important;
    }
}

ul {
    list-style-type: disc;
    li {
        line-height: 20px;
    }
}

.tab_content {
    text-align: center;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 24px;
    .tit {
        font-weight: bold;
        font-size: 24px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 28px;
        display: inline-block;
        margin-left: 6px;
        margin-right: 6px;
    }
    img {
        width: 33px;
        height: 22px;
    }
    .left {
        display: inline-block;
        width: 24px;
        height: 24px;
        background-size: contain;
        position: relative;
        top: 3px;
    }
    .right {
        display: inline-block;
        width: 24px;
        height: 24px;
        background-size: contain;
        position: relative;
        top: 3px;
    }
}

.content {
    width: 1200px;
    margin: 30px auto 0;
    // margin-top: 56px;
}

.card {
    box-shadow: none !important;
    // padding-bottom: 56px;
    //display: flex;
    img {
        height: 280px;
        width: 420px;
        display: inline-block;
    }
    .card_content {
        margin-bottom: 56px;
        display: flex;
        // flex: 1;
        width: 100%;
        //   卡片式
        .cards {
            //display: flex;
            //justify-content: start;
            // margin: 24px 0;
            .item_card {
                //width: 374px;
                height: 100%;
                padding-bottom: 12px;
                //margin-right: 24px;
                background: linear-gradient(163deg, #F1F3F6 0%, #F6F7F9 38%, #FFFFFF 100%);
                box-shadow: 8px 8px 12px 0px rgba(0, 0, 0, 0.04), -8px -8px 12px 0px rgba(255, 255, 255, 0.5);
                border-radius: 0px 0px 0px 0px;
                border: 2px solid #FFFFFF;
                p {
                    margin-bottom: 6px;
                    padding: 0 12px;
                }
                img {
                    width: 100%;
                    // height: 200px;
                    // float: right;
                }
                .title {
                    margin-top: 12px;
                    font-weight: bold;
                    font-size: 20px;
                    color: #2E3852;
                }
                .desc {
                    font-weight: 400;
                    font-size: 16px;
                    color: rgba(46, 56, 82, 0.85);
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 3;
                    /* 控制显示的行数 */
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                }
            }
        }
        .oneCards {
            display: flex;
            justify-content: start;
            // margin: 24px 0;
            width: 100%;
            .desc {
                display: -webkit-box;
                -webkit-line-clamp: 5;
                /* 限制为3行 */
                -webkit-box-orient: vertical;
                overflow: hidden;
                /* 隐藏超出部分 */
                text-overflow: ellipsis;
            }
        }
        // 文字描述
        .function {
            margin: 24px 0;
            flex-wrap: wrap;
            justify-content: space-between;
            .card_title {
                font-weight: bold;
                font-size: 18px;
                color: rgba(0, 0, 0, 0.85);
            }
            img {
                margin-right: 12px;
                width: 31px;
                height: 35px;
            }
            .card_list {
                background-image: url("@/assets/images/solution/detail/cardbg.png");
                background-size: cover;
                background-repeat: no-repeat;
                padding: 16px 32px;
                width: 49%;
                margin-bottom: 24px;
                overflow: hidden;
                .desc {
                    font-weight: 400;
                    font-size: 16px;
                    color: #2E3852;
                    overflow: hidden;
                    display: -webkit-box;
                    -webkit-line-clamp: 3;
                    -webkit-box-orient: vertical;
                }
            }
        }
        // 表格
        // 文字描述
        .table {
            margin: 24px 0;
            background: #FBFCFC;
            box-shadow: 8px 8px 12px 0px rgba(0, 0, 0, 0.04), -8px -8px 12px 0px rgba(255, 255, 255, 0.5);
            border-radius: 0px 0px 0px 0px;
            border: 1px solid #FFFFFF;
            padding: 16px 32px;
            .td {
                width: 85%;
                margin: 0 auto;
                margin-bottom: 16px;
                font-weight: 500;
                font-size: 20px;
                text-align: center;
                color: rgba(46, 56, 82, 0.65);
            }
            .tr {
                width: 85%;
                margin: 0 auto;
                margin-bottom: 12px;
                height: 50px;
                background: #F8FBFF;
                border-radius: 32px 32px 32px 32px;
                .tariff_index {
                    background: linear-gradient(180deg, #8DB7FF 0%, #65AEFF 100%);
                    box-shadow: 8px 8px 12px 0px rgba(0, 0, 0, 0.04), -8px -8px 12px 0px rgba(255, 255, 255, 0.5);
                    border-radius: 32px 32px 32px 32px;
                    border: 1px solid #FFFFFF;
                    color: #fff;
                    font-weight: bold;
                    font-size: 22px;
                }
                .ant-col {
                    line-height: 50px;
                    text-align: center;
                }
                .name {
                    font-weight: 500;
                    font-size: 20px;
                    color: #2E3852;
                }
                .price {
                    font-weight: bold;
                    font-size: 20px;
                    color: #236CFF;
                }
            }
        }
    }
    .right {
        background: linear-gradient(163deg, #F2F5F8 0%, #F6F7F9 38%, #FFFFFF 100%);
        box-shadow: -8px -8px 12px 0px rgba(255, 255, 255, 0.5);
        font-weight: 400;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.65);
        line-height: 32px;
        padding: 24px 32px;
        height: 280px;
        // overflow-y: auto;
        flex: 1;
    }
}

.provider_con {
    background: linear-gradient( 163deg, #F2F5F8 0%, #F6F7F9 38%, #FFFFFF 100%);
    box-shadow: 8px 8px 12px 0px rgba(0, 0, 0, 0.04), -8px -8px 12px 0px rgba(255, 255, 255, 0.5);
    border-radius: 0px 0px 0px 0px;
    border: 1px solid #FFFFFF;
    padding: 32px 40px;
    .pro {
        margin-bottom: 30px;
        padding: 30px 20px 20px 20px;
        box-shadow: 8px 8px 12px 0px rgba(4, 58, 107, 0.04);
        background: linear-gradient( 90deg, #FFFFFF 0%, #EAEFF7 100%);
        position: relative;
        .score {
            position: absolute;
            left: 0;
            top: 0;
            display: flex;
            min-width: 80px;
            height: 28px;
            padding-right: 20px;
            background-color: #FFFFFF;
            border-radius: 15px;
            box-shadow: 0px 4px 8px 0px rgba(77, 120, 170, 0.1);
            .scoreIcon {
                width: 24px;
                height: 24px;
                margin: 2px 6px 2px 2px;
            }
            .scoreBody {
                display: flex;
                .scoreTitle {
                    line-height: 28px;
                    font-size: 14px;
                    color: #00000073;
                    font-weight: 500;
                }
                .scoreNo {
                    color: #00000073;
                    font-size: 14px;
                    line-height: 28px;
                }
                .scoreNum {
                    line-height: 28px;
                    font-size: 18px;
                    color: #FF9C39FF;
                    font-weight: bold;
                }
            }
        }
    }
}

:deep(.ant-tabs-tab-active) {
    background: #E6EFFF !important;
    border-radius: 2px 2px 2px 2px;
    font-weight: 400 !important;
    color: #216AFB !important;
}

.provider {
    background-image: url('@/assets/images/solution/detail/provider.png');
    background-size: cover;
    background-repeat: no-repeat;
    padding: 25px 40px;
    .pro {
        margin-bottom: 32px;
        display: flex;
        p {
            display: inline-block;
            margin-bottom: 0;
        }
        img {
            width: 28px;
            height: 28px;
            margin-right: 8px;
        }
        .right_con {
            .title {
                font-weight: bold;
                font-size: 20px;
                color: rgba(0, 0, 0, 0.85);
                line-height: 23px;
                margin-bottom: 12px;
            }
            .boot {
                font-weight: 400;
                font-size: 16px;
                color: #2E3852;
                line-height: 28px;
                .conpro {
                    font-weight: 500;
                    font-size: 16px;
                    color: #2E3852;
                    line-height: 28px;
                }
            }
        }
    }
}

.pro:last-child {
    margin-bottom: 0;
}

.tab_card {
    .right {
        background: linear-gradient(163deg, #F2F5F8 0%, #F6F7F9 38%, #FFFFFF 100%);
        box-shadow: -8px -8px 12px 0px rgba(255, 255, 255, 0.5);
        font-weight: 400;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.65);
        line-height: 32px;
        padding: 24px 32px;
        max-height: 280px;
        overflow-y: auto;
    }
}

:deep(.ant-tabs-bar) {
    margin-bottom: 40px;
}

.bottomEmpty {
    height: 20px;
    width: 100%;
}

.top {
    position: fixed;
    bottom: 11px;
    left: 92%;
    cursor: pointer;
    width: 91px;
    height: 91px;
}

.info_card {
    background: linear-gradient(180deg, #EDF0F9 0%, #FEFEFF 100%);
    box-shadow: 0px 4px 24px 0px #EAEDF3;
    border-radius: 10px 10px 10px 10px;
    border: 2px solid #FFFFFF;
    margin-left: 6.5%;
    margin-right: 6.5%;
    margin-top: 32px;
    margin-bottom: 56px;
    .content {
        background: linear-gradient(196deg, #EAEFF7 0%, rgba(234, 239, 247, 0.41) 100%);
        box-shadow: 0px -8px 32px 0px #FFFFFF, inset 0px 8px 24px 0px #DFE4ED;
        border-radius: 4px 4px 4px 4px;
        opacity: 0.8;
        margin: 32px 40px;
        padding-left: 32px;
        padding-top: 16px;
        padding-bottom: 1px;
        .line {
            width: 100%;
            // margin-bottom: 16px;
            p {
                display: inline-block;
                font-weight: 500;
                font-size: 14px;
                color: #2E3852;
                line-height: 28px;
                text-align: left;
                // margin-left: 25%;
            }
            span {
                font-weight: bold;
            }
        }
        .desc {
            font-weight: 400;
        }
    }
}

:deep(.fixed) {
    position: static !important;
}

.bottom_say {
    padding-top: 40px;
    background-color: #F5F7FC;
    text-align: center;
    .title {
        font-weight: bold;
        font-size: 24px;
        color: #24456A;
        line-height: 28px;
    }
    .area {
        padding: 24px 40px;
        margin: 32px 120px;
        background-color: #FFFFFF;
        .area_tit {
            text-align: left;
            margin-bottom: 16px;
            .area_title {
                font-weight: 500;
                font-size: 14px;
                color: #2E3852;
                line-height: 28px;
            }
        }
        button {
            background: linear-gradient(270deg, #0142FD 0%, #2475F9 100%);
            border-radius: 2px 2px 2px 2px;
            font-weight: 500;
            font-size: 14px;
            color: #FFFFFF;
            line-height: 20px;
            padding: 6px 16px;
            border: none;
            margin-top: 16px;
            float: right;
        }
        textarea {
            border: none;
            background-color: #F5F7FC;
        }
    }
    .say_list {
        .listBox {
            padding-left: 0;
            .con {
                display: flex;
                margin-bottom: 33px;
                width: 100%;
                .con_left {
                    padding-top: 15px;
                    img {
                        width: 40px;
                        height: 40px;
                    }
                }
                .con_right {
                    margin-left: 16px;
                    text-align: left;
                    font-weight: 400;
                    font-size: 14px;
                    color: rgba(0, 0, 0, 0.65);
                    line-height: 24px;
                    .con_title {
                        margin-bottom: 8px;
                        .name {
                            font-weight: 500;
                            color: rgba(0, 0, 0, 0.85);
                            line-height: 22px;
                            margin-right: 12px;
                        }
                    }
                    .con_boot {
                        display: flex;
                        justify-content: space-between;
                        .delete {
                            color: #236CFF;
                        }
                    }
                }
            }
        }
    }
    .showMore {
        width: 100%;
        text-align: center;
        span {
            font-weight: 400;
            font-size: 14px;
            color: #236CFF;
            line-height: 24px;
            cursor: pointer;
        }
    }
}

.fileFlex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 0 20px;
}

:deep(.ant-anchor) {
    padding-left: 48px;
    .ant-anchor-link {
        font-weight: 500;
        font-size: 20px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 23px;
        margin-right: 48px;
        //padding-bottom: 20px;
    }
}

.top_nav {
    padding-left: 120px;
    height: 60px;
    background-color: #f5f7fc;
    width: 100%;
    margin-top: 8px;
    padding-right: 70px;
    display: flex;
    justify-content: space-between;
    position: fixed;
    top: 50px;
    z-index: 21;
    padding-top: 20px;
    div {
        display: inline-block;
    }
    .left_nav {
        padding-bottom: 8px;
        .title {
            font-weight: 400;
            font-size: 12px;
            color: #84899A;
            line-height: 20px;
            margin-right: 8px;
            cursor: pointer;
        }
        .current {
            font-weight: 400;
            font-size: 12px;
            color: #2E3852;
            line-height: 20px;
        }
    }
    .right_nav {
        color: #2E7FFF;
        cursor: pointer;
    }
}

.list {
    padding-inline-start: 0;
    list-style-type: none;
    width: 1200px;
    margin: 24px auto;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    li {
        width: 48%;
        display: inline-block;
        background: linear-gradient(180deg, #EDF0F9 0%, #FEFEFF 100%);
        box-shadow: 0px 4px 24px 0px #EAEDF3;
        border-radius: 10px 10px 10px 10px;
        border: 2px solid #FFFFFF;
        padding: 4px 24px 4px 24px;
        margin-bottom: 24px;
        img {
            display: inline-block;
            width: 72px;
            height: 72px;
        }
        p {
            display: inline-block;
            font-weight: 500;
            font-size: 16px;
            color: #2E3852;
            line-height: 28px;
        }
        .left_box {
            display: flex;
            padding-top: 19px;
        }
    }
    .li {
        width: 48%;
        display: inline-block;
        background: linear-gradient(180deg, #EDF0F9 0%, #FEFEFF 100%);
        box-shadow: 0px 4px 24px 0px #EAEDF3;
        border-radius: 10px 10px 10px 10px;
        border: 2px solid #FFFFFF;
        padding: 4px 24px 4px 24px;
        margin-bottom: 24px;
        img {
            display: inline-block;
            width: 72px;
            height: 72px;
        }
        p {
            display: inline-block;
            font-weight: 500;
            font-size: 16px;
            color: #2E3852;
            line-height: 28px;
        }
        .left_box {
            display: flex;
            padding-top: 19px;
        }
    }
    .li_box {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    li:nth-of-type(odd) {
        margin-right: 24px;
    }
    .fileText {
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
    }
}

.file {
    cursor: pointer;
    color: #236CFF;
    align-items: center;
    p {
        margin-bottom: 0;
    }
}

.applyCard {
     :deep(.ant-tabs-tab) {
        font-weight: 500;
        font-size: 20px;
        line-height: 23px;
        // width: 100%;
    }
     :deep(.ant-tabs-nav-list) {
        display: flex;
        justify-content: space-between;
    }
     :deep(.ant-tabs-nav-container-scrolling) {
        // padding: 0;
    }
     :deep(.ant-tabs-tab-prev) {
        // display: none;
    }
     :deep(.ant-tabs-tab-next) {
        // display: none;
    }
     :deep(.ant-tabs-nav) {
        // width: 100%;
    }
     :deep(.ant-tabs-nav>div:first-child) {
        // display: flex;
        // justify-content: space-between;
    }
     :deep(.ant-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab) {
        font-weight: bold;
        font-size: 20px;
        color: #2E7FFF;
        line-height: 23px;
    }
     :deep(.ant-tabs-tab-active) {
        font-weight: bold;
        font-size: 20px;
        color: #2E7FFF;
        line-height: 23px;
    }
     :deep(.ant-tabs-content-nav-operations) {
        display: none;
    }
     :deep(.ant-tabs-nav-operations) {
        display: none !important;
    }
     :deep(.ant-tabs-tab) {
        // flex-grow: 1;
        text-align: center;
        margin-top: 19px;
        color: #4D6886 !important;
        border: 1px solid #C5D2E4 !important;
        margin-right: 16px !important;
        padding: 0 16px !important;
    }
     :deep(.ant-tabs-tab:last-child) {
        margin-right: 0 !important;
    }
     :deep(.ant-tabs-tab-active) {
        color: #3A7AFC !important;
    }
}

:deep(.ant-breadcrumb) {
    li {
        cursor: pointer;
    }
}

.reset_btn {
    background-color: #FFFFFF;
    color: #2E3852;
    margin-right: 24px;
    border: 1px solid #C1CCE5;
    box-shadow: none;
}

.submit_btn {
    background-color: #1A66FB;
    color: #FFFFFF;
}

.btn_box {
    margin-top: 60px;
}

:deep(.ant-tabs-nav-list) {
    width: 100%;
}

:deep(.ant-tabs-tabpane) {
    // background-color: #F5F7FC;
    // margin-top: 32px;
    // height: 220px;
    display: flex;
}

:deep(.ant-tabs-ink-bar) {
    margin-top: 16px;
    height: 3px !important;
}

:deep(.ant-tabs-nav) {
    height: 80px;
    margin: 0;
}