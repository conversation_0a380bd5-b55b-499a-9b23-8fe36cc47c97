<template>
  <div>
  <!-- 方案场景进来的方案详情 -->
  <div class="box">
    <div class="loading-overlay" v-if="loadShow">
      <a-spin :spinning="loadShow" tip="附件加载中"></a-spin>
    </div>
    <div class="top_nav">
      <div class="left_nav">
        <span class="title" @click="back">行业方案</span>
        <span class="title"> / </span>
        <span class="current">{{ detailData.name || detailData.projectName }}</span>
      </div>
      <div class="right_nav">
        <div @click="backLast" style="margin-right: 20px">返回</div>
        <!-- <div @click="collectById" :class="{ active: collectActive }">
          <img
            width="22px"
            height="22px"
            v-if="detailData.collect == 0"
            src="@/assets/images/solution/detail/notCollect.png"
          />
          <img
            v-else
            width="22px"
            height="22px"
            src="@/assets/images/solution/detail/isCollect.png"
          />
        </div> -->
      </div>
    </div>
    <div style="margin-top: 50px">
      <div class="banner" style="height: 325px;" id="bacPhoto">
        <div class="top_card">
          <div class="left" style="height: 180px;">
            <div class="left_tit flex">
              <div class="title">{{ detailData.name || detailData.projectName }}</div>
              <!-- <a-tag class="code">方案场景编码：{{ detailData.code }}</a-tag> -->
            </div>
            <div class="left_middle">
              <!-- <p class="info">
                {{ detailData.abilityIntro }}
              </p> -->
              <a-tooltip overlayClassName="tooltip_class">
                <template v-if="isShowToolTip(detailData.summary || detailData.projectIntroduction, 174)" #title>
                  {{ detailData.summary || detailData.projectIntroduction }}</template>
                <p class="info"> {{ detailData.summary || detailData.projectIntroduction }}</p>
              </a-tooltip>
              <!--<div class="label flex" style="display: inline-block; margin-right: 24px">
                <span style="margin-right: 6px" v-for="(item, key) in detailData.labelName" :key="key">
                	{{ item}}
                </span>
              </div>-->
              <div class="info_bottom" style="margin-top: 10px;">
                <p>联系人：{{ detailData.contact || "-" }}</p>
                <p>联系电话：{{ detailData.phone || "-" }}</p>
                <p>联系邮箱：{{ detailData.mail || "-" }}</p>
              </div>
              <div class="info_bottom">
              	<p>提供方： {{ detailData.provider || "-" }}</p>
                <p>首次上架： {{ detailData.shelfTime ? shelfTimeWith(detailData.shelfTime) : detailData.createTime }}</p>
                <p>最近更新： {{ detailData.editTime ? shelfTimeWith(detailData.editTime) : shelfTimeWith(detailData.shelfTime) }}</p>
              </div>
              <div class="info_bottom">
              	<p>
              		场景方案分类：
                  <span v-if="detailData.ecologyType == 1">自有场景</span>
                  <span v-if="detailData.ecologyType == 2">生态场景</span>
                  <span
                    v-if="
                      detailData.ecologyType &&
                      detailData.ecologyType.split(',').length == 2
                    "
                    >自有场景+生态场景</span
                  >
                </p>
              </div>
              <!-- <div class="info_bottom bottom1">
                <p v-if="detailData.phone != null">
                  <img
                    src="@/assets/images/solution/detail/eyes.png"
                    alt=""
                  />{{ detailData.viewCount }}
                </p>
                <p v-else>
                  <img src="@/assets/images/solution/detail/eyes.png" alt="" />-
                </p>
                <p v-if="detailData.downloadCount != null">
                  <img
                    src="@/assets/images/solution/detail/left_down.png"
                    alt=""
                  />{{ detailData.downloadCount }}
                </p>
                <p v-else>
                  <img
                    src="@/assets/images/solution/detail/left_down.png"
                    alt=""
                  />-
                </p>
                <p v-if="detailData.collectCount != null">
                  <img
                    src="@/assets/images/solution/detail/star.png"
                    alt=""
                  />{{ detailData.collectCount }}
                </p>
                <p v-else>
                  <img src="@/assets/images/solution/detail/star.png" alt="" />-
                </p>
                <p v-if="detailData.combineCount != null">
                  <img
                    src="@/assets/images/solution/detail/yinru.png"
                    alt=""
                  />{{ detailData.combineCount }}
                </p>
                <p v-else>
                  <img
                    src="@/assets/images/solution/detail/yinru.png"
                    alt=""
                  />-
                </p>
              </div>
              <div class="info_bottom">
                <p>联系人：{{ detailData.contact }}</p>
                <p>联系电话：{{ detailData.phone }}</p>
                <p>联系邮箱：{{ detailData.email }}</p>
              </div>
              <div class="info_bottom">
                <p>方案场景来源：{{ detailData.source }}</p>
                <p>标签：{{ detailData.label }}</p>
              </div> -->

              <!-- <div class="info_bottom">
                <span class="white_bac">
                  <span
                    v-if="detailData.industryName != null"
                    style="margin-left: 0; padding-left: 0; border-left: none"
                  >
                    {{ detailData.industryName }}
                  </span>

                  <span v-if="detailData.provider != null">
                    {{ detailData.provider }}
                  </span>
                  <span
                    v-if="detailData.categoryName != null"
                    style="
                      border-left: 1px solid #dbe2ed;
                      border-right: 1px solid #dbe2ed;
                      padding: 0 16px;
                    "
                  >
                    {{ detailData.categoryName }}
                  </span>
                  <span
                    v-if="detailData.createTime != null"
                    style="padding-right: 0; border-right: none"
                  >
                    {{ detailData.createTime }}
                  </span>
                </span>
              </div>
              -->
              <div class="addCar" v-if="detailData.pptName">
                <button v-if="detailData.addCart">已加入</button>
                <button v-else @click="add">加入预选</button>
                <button v-if="showApply" @click="apply" style="margin-left: 16px;">申请调度</button>
              </div>
            </div>
            <div class="left_bottom"></div>
          </div>
          <div>
            <img style="width: 390px;height: 217px;" v-if="detailData.imageUrl == '' || detailData.imageUrl == undefined"
              src="@/assets/images/solution/detail/noneData.png" alt="">
            <img style="width: 390px;height: 217px;" v-else v-lazy="`${detailData.imageUrl}`" alt="">
          </div>
        </div>

      </div>
      <div class="anchors">
        <a-anchor direction="horizontal" :affix="false" v-for="(item, key) in anchorList" :key="key" @click="handleClick">
          <a-anchor-link :class="{ currentActive: isActive === key }" @click="change(key)" :href="item.href"
            :title="item.title" />
        </a-anchor>
      </div>

      <div class="content" id="anchorContent">
        <div class="card applyCard" id="#functionList" v-if="detailData.functionListShow" style="margin-top: 56px;">
          <div class="card_content" style="padding-top: 0px; display: block">
            <div class="tab_content">
              <img src="@/assets/images/solution/detail/leftIcon.png" style="width: 33px; height: 22px" alt="" />
              <div class="tit">核心功能</div>
              <img src="@/assets/images/solution/detail/rightIcon.png" style="width: 33px; height: 22px" alt="" />
            </div>
            <div class="cards">
              <div class="item_card" v-for="(item, key) in detailData.functionList" :key="key + 1">
                <img v-if="`${item.image}`" v-lazy="`${item.image}`" alt="" class="img" />
                <img v-else src="@/assets/images/ability/adlityDetail/apply.png" class="img" />
                <p class="title">{{ item.name }}</p>
                <a-tooltip overlayClassName="tooltip_class">
                  <template v-if="isShowToolTip(item.description, 60)" #title>
                    {{ item.description }}</template>
                  <p class="desc">{{ item.description }}</p>
                </a-tooltip>
                <!-- <el-tooltip placement="top" v-if="item.description.length>60">
                  <template #content>
                    <p
                      style="
                        width: 420px;
                        font-weight: 400;
                        font-size: 16px;
                        color: #ffffff;
                        line-height: 19px;
                      "
                    >
                      {{ item.description }}
                    </p>
                  </template>
                  <p class="desc">{{ item.description }}</p>
                </el-tooltip>
                <p class="desc" v-else>{{ item.description }}</p> -->
              </div>
            </div>
          </div>
        </div>
        <div class="card applyCard" id="#caseList" v-if="detailData.caseListShow" style="margin-top: 56px;">
          <div class="card_content" style="padding-top: 0px; display: block">
            <div class="tab_content">
              <img src="@/assets/images/solution/detail/leftIcon.png" style="width: 33px; height: 22px" alt="" />
              <div class="tit">项目案例</div>
              <img src="@/assets/images/solution/detail/rightIcon.png" style="width: 33px; height: 22px" alt="" />
            </div>
            <div class="cards">
              <div class="case_card" v-for="(value, index) in detailData.caseList" :key="index" style="width: 100%;">
                <div class="case_box flex">
                  <div class="left_img">
                    <img v-if="value.image == '' || value.image == null"
                      src="@/assets/images/ability/adlityDetail/bac.png" alt="">
                    <img v-else v-lazy="`${value.image}`" alt="" />
                  </div>
                  <div class="right_con">
                    <div class="con">
                    <p class="right_name" v-if="value.name != null">
                      项目名称：{{ value.name }}
                    </p>
                    <p class="right_name" v-if="value.scale && value.scale != null">规模：{{ value.scale }}（万元）</p>
                    <p class="right_name" v-if="value.scale && value.time != null">时间：{{ value.time }}</p>
                    <!-- <p class="right_name" v-if="value.people.length>4">
                          联系人：{{ value.people }}
                        </p> -->
                    <a-tooltip overlayClassName="tooltip_class">
                      <template v-if="isShowToolTip(value.description, 136)" #title>
                        {{ value.description }} </template>
                      <span class="right_name caseinfo">{{ value.description }}</span>
                    </a-tooltip></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="card" id="#solutionList" style="width: 1200px; margin-top: 56px" v-if="detailData.solutionList && detailData.solutionList.length>0">
          <div class="tab_content">
            <img src="@/assets/images/solution/detail/leftIcon.png" style="width: 33px; height: 22px" alt="" />
            <div class="tit">场景应用</div>
            <img src="@/assets/images/solution/detail/rightIcon.png" style="width: 33px; height: 22px" alt="" />
          </div>
          <div class="content flex" style="width: 1200px" v-for="(item, index) in detailData.solutionList" :key="index">
            <div style="
                        width: 370px;
                        height: 255px;
                        text-align: center;
                        position: relative;
                      " :style="backgroundStyles()">
              <p style="
                          font-weight: 700;
                          display: block;
                          color: #1F82C8;
                          position: absolute;
                          left: 50%;
                          top: 50%;
                          transform: translate(-50%, -50%);
                          font-size: 16px;
                        ">
                {{ item.solutionName }}
              </p>
            </div>
            <div class="right" @click="toSolution(item.solutionId)" style="height: 255px; cursor: pointer; text-align: left">
              <p class="title" style="
                  font-weight: bold;
                  font-size: 20px;
                  color: #2e3852;
                  line-height: 23px;
                  margin-bottom: 16px;
                ">
                {{ item.solutionName }}
              </p>
              <p style="
                  font-weight: 400;
                  font-size: 16px;
                  color: #2e3852;
                  line-height: 28px;
                  display: -webkit-box;
                  -webkit-line-clamp: 5;
                  -webkit-box-orient: vertical;
                  overflow: hidden;
                  text-overflow: ellipsis;
                ">
                <a-tooltip overlayClassName="tooltip_class">
                  <template v-if="isShowToolTip(item.solutionDesc, 165)" #title>
                    {{ item.solutionDesc }}
                  </template>
                  <span class="right_name">{{
                    item.solutionDesc
                  }}</span>
                </a-tooltip>
              </p>
            </div>
          </div>
        </div>
        <div v-if="detailData.ecopartnerListShow" id="#partner" class=" card applyCard" style="margin-top: 56px;">
          <div class="tab_content" style="margin-top: 30px;">
            <img src="@/assets/images/solution/detail/leftIcon.png" style="width: 33px; height: 22px" alt="" />
            <div class="tit">生态合作方</div>
            <img src="@/assets/images/solution/detail/rightIcon.png" style="width: 33px; height: 22px" alt="" />
          </div>
          <div class="provider_con" style="width: 1200px;">
                  <div>
                    <div class="pro" v-for="(value, key) in detailData.ecopartnerNewList" :key="key" @click="goHtml(value)"
                    	:style="{'cursor': value.sync==1&&value.auth==1 ? 'pointer' : 'default'}"
                  	>
                      <div class="score" v-if="value.sync==1&&value.auth==1&&value.type ==2">
                        <img class="scoreIcon" src="@/assets/images/score.png" alt="" />
                        <div class="scoreBody" v-if="value.totalScore || value.introScore">
                          <div class="scoreTitle">生态评分：</div>
                          <div class="scoreNum">{{ value.totalScore || value.introScore}}</div>
                        </div>
                        <div class="scoreBody" v-else>
                          <div class="scoreNum" style="font-size: 14px;margin-left: 10px;">暂无评分</div>
                        </div>
                      </div>
                      <div class="score" v-if="(value.sync!=1 || value.auth!=1) && value.type==2">
                        <img class="scoreIcon" src="@/assets/images/scoreNo.png" alt="" />
                        <div class="scoreBody">
                        	<div class="scoreNo">未认证</div>
                        </div>
                      </div>
                      <a-tooltip>
                        <template #title v-if="value.sync==1&&value.auth==1">点击查看详情</template>
                        <div class="flex align-center" style="margin-bottom: 10px;">
                          <img style="width: 26px;height: 26px;margin-right: 8px;"
                            src="@/assets/images/solution/detail/pro_icon.png" alt="" v-if="value.type ==2" />
                          <img style="width: 26px;height: 26px;margin-right: 8px;"
                            src="@/assets/images/solution/detail/pro_own.png" alt="" v-else />
                          <div v-if="value.type ==2" style="font-weight: 400;font-size: 16px;line-height: 28px;color: #2E3852;margin-right: 10px;">
                          	生态合作方：{{ dealData(value.ecopartnerName) }}
                          </div>
                          <div v-if="value.type ==1" style="font-weight: 400;font-size: 16px;line-height: 28px;color: #2E3852;margin-right: 10px;">
                          	自有能力方：{{ dealData(value.ecopartnerName) }}
                          </div>
                        </div>
                        <div class="flex align-center" style="margin-left: 34px;">
                          <div style="width: 24%;font-weight: 400;font-size: 16px;line-height: 28px;color: #2E3852;">
                          	联系人：{{ dealData(value.children[0].ecologyContact) }}
                          </div>
                          <div style="width: 34%;font-weight: 400;font-size: 16px;color: #2E3852;line-height: 28px;">
                          	联系方式：{{ dealData(value.children[0].ecologyPhone) }}
                          </div>
                          <div style="width: 34%;font-weight: 400;font-size: 16px;color: #2E3852;line-height: 28px;">
                            <div style="width:340px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">
                              <a-tooltip overlayClassName="tooltip_class">
                                <template v-if="isShowToolTip(dealData(value.children[0].contactAddress), 14)"
                                  #title>{{ dealData(value.children[0].contactAddress) }}
                                </template>
                                负责区域：{{ dealData(value.children[0].contactAddress || '江苏省') }}
                              </a-tooltip>
                            </div>
                          </div>
                        </div>
                      </a-tooltip>
                    </div>
                  </div>
                </div>
          </div>
        <div class="tab_content" id="#download" v-if="detailData.pptPath != null" style="margin-top: 56px">
          <img src="@/assets/images/solution/detail/leftIcon.png" alt="" />
          <div class="tit">场景附件</div>
          <img src="@/assets/images/solution/detail/rightIcon.png" alt="" />
        </div>
        <ul class="list" v-if="detailData.pptPath != null">
          <!-- <span v-if="item.category == 1">功能介绍：</span> -->
          <!-- <li v-for="(item, key) in detailData.fileList"> -->
          <div class="li">
            <div class="li_box" @click="fileShow(detailData.pptPath, detailData.pptUrl)">
              <div class="left_box">
                <img src="../../../assets/images/solution/detail/word.png" alt="" @click="doladFile"
                  style="width: 40px; height: 40px" />
                <p class="fileText">{{ detailData.pptName }}</p>
              </div>
              <img src="../../../assets/images/solution/detail/download.png" alt=""
                @click.stop="downloadBtn(detailData.pptUrl, detailData.pptName)" style="cursor: pointer" />
            </div>
          </div>
          <!-- </li> -->
        </ul>
      </div>

      <img class="top" src="../../../assets/images/solution/detail/toTap.png" alt="" @click="scrollUp" />
      <view-list :id="viewId" :type="viewType"></view-list>
      <div class="bottom"></div>
    </div>
  </div>
  <a-modal v-model:visible="showDownloadModal" title="提示" :mask-closable="false" :footer="null" :destroyOnClose="true"
    width="550px">
    <promptBox @downloadModalCancel="downloadModalCancel" @downloadModalConfirm="downloadModalConfirm" />
  </a-modal>
  <a-modal v-model:visible="showDownloadForm" title="新增工单" :mask-closable="false" :footer="null" :destroyOnClose="true"
    width="600px">
    <reviewForm @downloadFormCancel="downloadFormCancel" @downloadFormConfirm="downloadFormConfirm" />
  </a-modal>
  <!-- <DispatchTypeModal
    v-model="showDispatchModal"
    @select="handleDispatchSelect"
  /> -->
  </div>
</template>
<script lang="ts" setup>
import { isShowToolTip } from "../../../utils/index.js";
import viewList from "../../product/detail/viewList.vue";
import { onMounted, ref, toRaw, UnwrapRef, reactive, nextTick } from "vue";
import { getTradeList } from "../../../api/solutionNew/home";
import { getEcologicalDetails } from "../../../api/login/login.js";
import { getDownCount } from "../../../api/solutionNew/detail";
import { useRouter, useRoute } from "vue-router";
import { addShoppingCart } from "../../../api/combine/shoppingCart.js";
import eventBus from "../../../utils/eventBus";
import {
  getDetail,
  getSoluDownCount,
  getIntroduce,
  cancelCollect,
  collect,
  getReviewList,
  getSolveDetail,
  getSceneInfoDetail,
} from "../../../api/moduleList/detail";
import { pptTopdf } from "../../../api/fileUpload/uploadFile.js";
import axios from "axios";
import { message } from "ant-design-vue";
import bac from "../../../assets/images/noDataBac.png";
import promptBox from "../../../components/promptBox/index.vue";
import reviewForm from "../../../components/reviewForm/index.vue";
import { getNewDownCount } from "../../../api/solutionNew/detail";
import { getAllUserList, roleDetail } from "@/api/system/user.js";
// import DispatchTypeModal from "@/components/DispatchTypeModal/index.vue";

interface FormState {
  name: string;
  code: string;
  categoryId: number | undefined;
  estimatedAmount: string;
  schemeId: number;
  status: string;
}

interface DetailData {
  solutionList: any[];
  ecopartnerNewList: any[];
  collect: number;
  [key: string]: any;
}

const route = useRoute();
onMounted(() => {
  getData();
});
const speakList = ref();
const pageItemSize = ref("3");
const currentPage = ref("1");
const totalItemCount = ref(0);
const viewType = ref("5");
const loadShow = ref(false);
const showDownloadModal = ref(false);
const showDownloadForm = ref(false);
// const showDispatchModal = ref(false);
const reviewList = () => {
  let params = {
    sourceId: route.query.id,
    pageSize: pageItemSize.value,
    pageNo: currentPage.value,
    sourceType: "2",
    delStatus: 1,
  };
  getReviewList(params).then((res) => {
    if (res.code == 200) {
      totalItemCount.value = res.data.totalRows;
      speakList.value = res.data.rows.map((item) => ({
        ...item,
        show: item.creatorName === userInfo.realName,
      }));
    }
  });
};
reviewList();
const userInfo = JSON.parse(localStorage.getItem("userInfo"));
console.log("userInfo", userInfo);

const functionKey = ref(1);
const isActive = ref(0);
const collectActive = ref(false);
const ecoName = ref(null);

function change(v) {
  isActive.value = v;
}
const shelfTimeWith = (value) => {
  if (value) {
    return value.slice(0, 10);
  }
  return "-";
};
const downloadBtn = (e, n) => {
  console.log("下载超限");
  getNewDownCount({
    businessId: route.query.id,
    businessType: 10,
  }).then((res) => {
    if (res.code == 200) {
      if (res.data) {
      	let windowOrigin = window.location.origin;
				let token = localStorage.getItem("token");
				let newHref = e;
	      if(e.includes(windowOrigin)){
	      	newHref = "/portal" + e.split(windowOrigin)[1]
	      }
				window.open(windowOrigin + newHref + "?token=" + token);
      } else {
        showDownloadModal.value = true;
      }
    }
  });
};
const fileShow = (val, valu) => {
  loadShow.value = true;
  // 转PDF的下载
  pptTopdf({
    filePath: val,
    fileUrl: valu,
  }).then((res) => {
    loadShow.value = false;
    if (res.code == 200) {
      let windowOrigin = window.location.origin;
      let token = localStorage.getItem("token");
      let newHref = res.data;
	    if(res.data.includes(windowOrigin)){
	      newHref = "/portal" + res.data.split(windowOrigin)[1]
	    }
      const newpage = Router.resolve({
        name: "lookPdf",
        query: {
          urlMsg: encodeURIComponent(
          	windowOrigin + newHref + "?token=" + token
          ),
          urlName: val.name,
        },
      });
      window.open(newpage.href, "_blank");
    }
  });
};
const formRef = ref();

const getCurrentAnchor = () => {
  return currentAnchor.value;
};
const currentAnchor = ref("#desc");
const viewId = ref(route.query.id);
const back = () => {
  Router.push({
    name: "topContentNew",
    query: {
      activeNum: "2",
    },
  });
};
const backLast = () => {
  Router.back();
  return false;
  if (route.query.activeBtn == "2") {
    Router.push({
      name: "topContentNew",
      query: {
        activeNum: "2",
      },
    });
  } else {
    Router.go(-1);
  }
};
// 滚动函数
const scrollUp = () => {
  currentAnchor.value = "#desc";
  getCurrentAnchor();
  isActive.value = 0;
  document.getElementById("layout_content").scrollTo({
    top: 0,
    behavior: "smooth",
  });
};
const detailData = ref<DetailData>({
  solutionList: [],
  ecopartnerNewList: [],
  collect: 0,
});
const transformEcoPartnerList = (originalList) => {
  // 边界情况处理
  if (!originalList || !Array.isArray(originalList)) {
    return [];
  }

  // 使用Map实现高性能分组
  const groupMap = originalList.reduce((map, item) => {
    // 跳过无效元素
    if (!item || typeof item !== "object") return map;

    try {
      // 安全提取字段（防止undefined报错）
      const {
        ecopartnerName,
        ecopartnerId,
        enterpriseId,
        totalScore,
        introScore,
        sync,
        auth,
        approve,
        delStatus,
      } = item;
      const key = `${ecopartnerName}|${ecopartnerId}|${enterpriseId}`;

      // 自动跳过缺失关键字段的元素
      if (!key.includes("undefined")) {
        if (!map.has(key)) {
          map.set(key, {
            ecopartnerName: ecopartnerId ? ecopartnerName : ecoName.value,
            ecopartnerId,
            enterpriseId,
            totalScore,
            introScore,
            sync,
            auth,
            type: ecopartnerId == null ? 1 : 2,
            approve,
            delStatus,
            children: [],
          });
        }
        // 安全提取子字段
        map.get(key).children.push({
          contactName: item.ecologyContact || item.contactName || "",
          contactPhone: item.ecologyPhone || item.contactPhone || "",
          contactAddress: item.contactAddress || "",
          ecologyContact: item.ecologyContact || item.contactName || "",
          ecologyPhone: item.ecologyPhone || item.contactPhone || "",
        });
      }
    } catch (e) {
      console.warn("Invalid data format:", e);
    }

    return map;
  }, new Map());

  return Array.from(groupMap.values());
};
const apply = () => {
  console.log("Opening dispatch modal");
  nextTick(() => {
    handleDispatchSelect('售前调度');// 直接发起售前调度工单
    // showDispatchModal.value = true;
  });
};
const showApply = ref(false);
const getData = () => {
  if (route.query.id) {
    if (
      (userInfo.orgId === 2 ||
      userInfo.orgId === 3 ||
      userInfo.orgId === 4 ||
      userInfo.orgId === 5 ||
      userInfo.orgId === 6 ||
      userInfo.orgId === 7 ||
      userInfo.orgId === 8 ||
      userInfo.orgId === 9 ||
      userInfo.orgId === 11 ||
      userInfo.orgId === 13 ||
      userInfo.orgId === 14 ||
      (userInfo.orgId !== 173 &&
        userInfo.orgId !== 184 &&
        userInfo.orgId !== 185 &&
        userInfo.orgId !== 211 &&
        userInfo.orgId !== 212 &&
        userInfo.orgId !== 213 &&
        userInfo.orgId !== 174 &&
        userInfo.orgId !== 186 &&
        userInfo.orgId !== 190 &&
        userInfo.orgId !== 191 &&
        userInfo.orgId !== 192) ||
      userInfo.orgId === 10 ||
      userInfo.orgId == 12) && userInfo.orgNamePath.split("/")[0] === "江苏公司"
    ) {
      showApply.value = true;
    }
    //getDetail(route.query.id)
    getSceneInfoDetail(route.query.id).then(async (res) => {
      roleDetail(res.data.contactId).then(val=>{
		    if(val.data.orgNamePath && val.data.orgNamePath.split("/").length == 3) {
		      if( val.data.orgNamePath.split("/")[1] == "苏移集成"){
		        ecoName.value = "江苏移动信息系统集成有限公司";
		      } else {
		        ecoName.value = val.data.orgNamePath.split("/")[1]
		      }
		    } else {
		      ecoName.value = val.data.orgName
		    }
        detailData.value = [];
        anchorList.value = [];
        let date = new Date(res.data.createTime);
        var Y = date.getFullYear();
        var M =
          date.getMonth() + 1 < 10
            ? "0" + (date.getMonth() + 1)
            : date.getMonth() + 1;
        var D =
          (date.getDate() < 10 ? "0" + date.getDate() : date.getDate()) + " ";
        let GMT = Y + "-" + M + "-" + D;
        res.data.createTime = GMT;
        res.data.caseList.forEach((element) => {
          let text = element.description;
          let caseInfo = {};
          let keywords = {
            name: "项目名称[：:](.*?)\\n",
            size: "规模[：:](.*?)\\n",
            time: "时间[：:](.*?)\\n",
            people: "联系人[：:](.*?)\\n",
            intro: "(案例介绍|项目介绍)[：:](.*)",
          };
          for (let key in keywords) {
            let regex = new RegExp(keywords[key], "s");
            let match = text.match(regex);
            if (match) {
              caseInfo[key] =
                key === "intro"
                  ? (match[2] || "").trim()
                  : (match[1] || "").trim();
            } else {
              caseInfo[key] = "";
              // 如果您不想在找不到匹配时保留该属性，可以取消注释下一行
              // delete caseInfo[key];
            }
          }
          element.caseInfo = caseInfo;
        });
        // res.data.provider = res.data.provider.split("/")[1];
        detailData.value = res.data;
        detailData.value.solutionList = [];
        detailData.value.ecopartnerNewList = [];

        let solutionIdPro = res.data.solutionSceneList.filter(
          (item) => item.type == 1
        );
        solutionIdPro.forEach((item) => {
          getSolveDetail(item.solutionId).then((result) => {
            if (result.code == 200) {
              detailData.value.solutionList.push({
                solutionId: result.data.id,
                solutionName: result.data.name,
                solutionDesc: result.data.description,
                solutionImg: result.data.logo,
                labelName: result.data.labelName.split(","),
                provider: result.data.provider,//.split("/")[1],
                contact: result.data.contact,
                phone: result.data.phone,
                mail: result.data.mail,
              });
              if (
                result.data.ecopartnerList &&
                result.data.ecopartnerList.length > 0
              ) {
                let ecopartnerNewList = transformEcoPartnerList(
                  result.data.ecopartnerList
                );
                if (detailData.value.ecopartnerNewList.length == 0) {
                  detailData.value.ecopartnerNewList = ecopartnerNewList;
                }
                res.data.ecopartnerListShow = true;
                if (!anchorList.value.some((val) => val.key == "partner")) {
                  let length =
                    anchorList.value.length > 0
                      ? anchorList.value.length - 1
                      : 0;
                  anchorList.value.splice(length, 0, {
                    key: "partner",
                    href: "#partner",
                    title: "生态合作方",
                  });
                }
              }
            }
          });
        });

        if (res.data.functionList.length != 0) {
          res.data.functionListShow = true;
          anchorList.value.push({
            key: "functionList",
            href: "#functionList",
            title: "核心功能",
          });
        }
        if (res.data.caseList.length != 0) {
          res.data.caseListShow = true;
          anchorList.value.push({
            key: "caseList",
            href: "#caseList",
            title: "项目案例",
          });
        }
        if (
          res.data.solutionSceneList &&
          res.data.solutionSceneList.length > 0
        ) {
          anchorList.value.push({
            key: "solutionList",
            href: "#solutionList",
            title: "场景应用",
          });
        }
        if (
          (res.data.ecopartnerList && res.data.ecopartnerList.length > 0) ||
          (res.data.enterpriseList && res.data.enterpriseList.length > 0)
        ) {
          //res.data.enterpriseList && res.data.enterpriseList.length > 0
          if (false) {
            // let dataIds = [];
            // res.data.enterpriseList.forEach((item) => {
            //   if (item.enterpriseId > 10000) {
            //     dataIds.push(item.enterpriseId);
            //   }
            // });
            // if (dataIds.length > 0) {
            //   let result = await getEcologicalDetails(dataIds);
            //   let ecopartnerNewList = [];
            //   if (result.data) {
            //     result.data.forEach((item) => {
            //       let children = [];
            //       item.basicContactInfos.forEach((itemChild) => {
            //         children.push({
            //           ecologyContact: itemChild.contactName,
            //           ecologyPhone: itemChild.contactPhone,
            //           contactAddress: itemChild.contactAddress,
            //         });
            //       });
            //       ecopartnerNewList.push({
            //         ecopartnerName: item.enterpriseName,
            //         ecopartnerId: item.id,
            //         children: children,
            //       });
            //     });
            //     res.data.ecopartnerNewList = ecopartnerNewList;
            //   }
            // } else {
            //   if (res.data.ecopartnerList && res.data.ecopartnerList.length > 0) {
            //     res.data.ecopartnerNewList = transformEcoPartnerList(
            //       res.data.ecopartnerList
            //     );
            //   }
            // }
          } else if (
            res.data.ecopartnerList &&
            res.data.ecopartnerList.length > 0
          ) {
            res.data.ecopartnerNewList = transformEcoPartnerList(
              res.data.ecopartnerList
            );
          }
          res.data.ecopartnerListShow = true;
          anchorList.value.push({
            key: "partner",
            href: "#partner",
            title: "生态合作方",
          });
        }

        if (res.data.pptPath != null && res.data.pptPath != "") {
          anchorList.value.push({
            key: "download",
            href: "#download",
            title: "场景附件",
          });
        }
      });
    });
  }
};
const add = () => {
  addShoppingCart({
    schemeId: route.query.id,
    type: "3",
  }).then((res) => {
    getData();
    eventBus.emit("cartRefresh");
  });
};
const dealData = (value) => {
  if (value) {
    return value;
  } else {
    return "-";
  }
};
const tabList = ref([]);
const getTarde = () => {
  let tradeParams = {};
  getTradeList(tradeParams).then((result) => {
    result.data.map((item) => {
      tabList.value.push({
        name: item.name,
        id: item.id.toString(),
      });
    });
  });
};
getTarde();
const isShow = ref("desc");
const anchorList = ref([]);

const collectById = () => {
  if (detailData.value.collect == 1) {
    cancelCollect(route.query.id)
      .then(() => {
        message.success("取消收藏成功");
        getData();
      })
      .catch((err) => {
        console.log(err);
      });
  } else {
    collect(route.query.id)
      .then(() => {
        message.success("收藏成功");
        getData();
      })
      .catch((err) => {
        console.log(err);
      });
  }
  collectActive.value = !collectActive.value;
};
const Router = useRouter();
const handleClick = (e, link) => {
  const href = link.href.replace("#", "");
  e.preventDefault();
  currentAnchor.value = "#" + href;
  let srcolls = document.getElementById(link.href);
  srcolls &&
    srcolls.scrollIntoView({
      block: "center",
      behavior: "smooth",
    });
  isShow.value = href;
};
eventBus.on("applyDetailRefresh", getData);
const toSolution = (solutionId) => {
  // let id = detailData.value.solutionId;
  Router.push({
    path: "/solveNew/detailNew",
    query: { id: solutionId },
  });
};
const backgroundStyles = () => {
  return {
    backgroundImage: `url(${bac})`, // 使用模板字符串来插入变量
    backgroundRepeat: "no-repeat",
    backgroundSize: "cover",
  };
};
const downloadModalCancel = () => {
  showDownloadModal.value = false;
};
// 下载超限提示弹窗确认按钮
const downloadModalConfirm = () => {
  showDownloadModal.value = false;
  showDownloadForm.value = true;
};
const downloadFormCancel = () => {
  showDownloadForm.value = false;
};
const downloadFormConfirm = () => {
  showDownloadForm.value = false;
};
const goHtml = (item) => {
  if (item.sync == 1 && item.auth == 1) {
    console.log(item.ecopartnerId);
    if (item.ecopartnerId > 10000) {
      window.open(
        "https://ipartner.jsdict.cn/static/detail?partner_id=" +
          item.ecopartnerId +
          "&token=bhubh3333ugy",
        "_blank"
      );
    } else {
      window.open(
        "https://ipartner.jsdict.cn/static/detail?partner_id=" +
          item.enterpriseId +
          "&token=bhubh3333ugy",
        "_blank"
      );
    }
  }
};
const handleDispatchSelect = (type: string) => {
  console.log("Selected type:", type);
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  const dispatchConfig = {
    售前调度: {
      to: "starWork",
      checkPermission: () => true,
    },
    售中调度: {
      to: "coordination",
      checkPermission: (userInfo) =>
        userInfo.roleKeyList.includes("deliveryManager"),
      errorMessage: "目前售中调度工单仅支持交付经理发起",
    },
    售后调度: {
      to: "",
      checkPermission: () => false,
      errorMessage: "售后调度工单流程暂未开放，敬请期待！",
    },
  };
  const config = dispatchConfig[type];
  if (!config) return;

  if (!config.checkPermission(userInfo)) {
    message.warning(config.errorMessage);
    return;
  }

  const params = {
    id: route.query.id,
    action: "edit",
    from: "scene",
    to: config.to,
    active: '调度中心',
  };

  const searchParams = new URLSearchParams(params);
  window.location.replace(
    window.location.origin + "/#/dispatchCenter/transfer?" + searchParams.toString()
  );
};
</script>
<style lang="scss" scoped>
@import "./index.scss";
</style>

<style lang="scss">
.label {
  span {
    display: inline-block;
    border-radius: 0px 0px 0px 0px;
    border: 1px solid #236cff;
    font-weight: 400;
    font-size: 14px;
    color: #236cff;
    line-height: 25px;
    padding: 0 10px;
  }
}

.dialogModal {
  .dia_box {
    background-image: url("@/assets/images/solution/detail/downBgc.png");
    height: 150px;
    padding: 20px 24px;
  }

  .ant-modal .ant-modal-title {
    font-weight: bold;
    font-size: 24px;
    color: #122c6c;
    line-height: 28px;
    text-align: center;
  }

  .ant-modal-content {
    height: 395px;
    padding: 0;
  }

  .ant-form {
    width: 100%;
  }

  .title {
    font-weight: bold;
    font-size: 24px;
    color: #122c6c;
    line-height: 28px;
    margin-bottom: 8px;
  }

  .ant-tabs-tab-active {
    background: #1a66fb;

    .ant-tabs-tab-btn {
      color: #ffffff !important;
    }
  }

  .ant-tabs-nav-wrap {
    margin-top: 16px;
    width: 236px;
    height: 48px;
    background: #ffffff;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #ffffff;
  }

  .ant-input {
    background: linear-gradient(
      196deg,
      #eaeff7 0%,
      rgba(234, 239, 247, 0.41) 100%
    );
  }

  .ant-input-affix-wrapper {
    background: linear-gradient(
      196deg,
      #eaeff7 0%,
      rgba(234, 239, 247, 0.41) 100%
    );
    box-shadow: 0px -8px 32px 0px #ffffff, inset 0px 8px 24px 0px #dfe4ed;
    border-radius: 4px 4px 4px 4px;

    button {
      font-weight: 500;
      font-size: 16px;
      color: #1a66fb;
      line-height: 28px;
    }
  }

  .ant-tabs-nav::before {
    display: none;
  }

  .ant-tabs-tabpane {
    background-color: #ffffff !important;
    font-weight: 500;
    font-size: 16px;
    color: #2e3852;
    line-height: 28px;
    height: 150px;
  }

  .ant-tabs-ink-bar {
    display: none;
  }

  .ant-tabs-content {
    padding-left: 10px;
  }

  .ant-tabs-tab {
    width: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .key {
    font-weight: 400;
    font-size: 16px;
    color: #2b3f66;
    line-height: 28px;
  }
}

.left_tit {
  .title {
    font-weight: 700;
    font-size: 24px;
    color: #2e3852;
    line-height: 28px;
    text-align: left;
    display: inline-block;
    margin-right: 8px;
  }
}

.code {
  display: inline-block;
  padding: 5px 8px;
  border: none;
  font-size: 16px;
  margin-left: 12px;
  color: #0c70eb;
  background: transparent;
}

.case_box {
  .left_img {
    width: 420px;
    height: 280px;

    img {
      height: 280px;
    }
  }

  .right_con {
    height: 280px;
    flex: 1;
    padding: 24px;
    background: linear-gradient(163deg, #f1f3f6 0%, #f6f7f9 38%, #ffffff 100%),
      #ffffff;
    box-shadow: 8px 8px 12px 0px rgba(0, 0, 0, 0.04),
      -8px -8px 12px 0px rgba(255, 255, 255, 0.5);
    border: 1px solid #ffffff;
    font-weight: 400;
    font-size: 16px;
    color: #2e3852;
    line-height: 28px;

    .right_name {
      margin-bottom: 0;
    }
  }
}

.caseinfo {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  /* 限制为3行 */
  -webkit-box-orient: vertical;
  overflow: hidden;
  /* 隐藏超出部分 */
  text-overflow: ellipsis;
}

.cards {
  display: flex;
  justify-content: start;
  margin-top: 24px;
  flex-wrap: wrap;

  .item_card {
    width: 374px;
    padding-bottom: 12px;
    margin-right: 39px;
    margin-bottom: 24px;
    background: linear-gradient(163deg, #f1f3f6 0%, #f6f7f9 38%, #ffffff 100%);
    box-shadow: 8px 8px 12px 0px rgba(0, 0, 0, 0.04),
      -8px -8px 12px 0px rgba(255, 255, 255, 0.5);
    border-radius: 0px 0px 0px 0px;
    border: 2px solid #ffffff;

    p {
      margin-bottom: 6px;
      padding: 0 12px;
    }

    img {
      width: 100%;
      height: 176px;
    }

    .title {
      margin-top: 12px;
      font-weight: bold;
      font-size: 20px;
      color: #2e3852;
    }

    .desc {
      font-weight: 400;
      font-size: 16px;
      color: rgba(46, 56, 82, 0.85);
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      /* 控制显示的行数 */
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      height: 75px;
    }
  }
}

.item_card:nth-child(3n) {
  margin-right: 0;
}

// .item_card:nth-child(4n) {
//   margin: 0;
// }
.info_bottom {
  display: flex;

  p {
    display: flex !important;
    align-items: center;
  }
}

:deep(.an_title) {
  background: #ffffff !important;
}

tooltip-style.el-tooltip__popper {
  max-width: 220px;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.7);
  /* 半透明遮罩 */
  z-index: 9999;
}
</style>
