<template>
  <div>
  <div class="topImage">
    <div class="topImage_content">
      <div class="topImage_title">{{ title }}</div>
      <div class="topImage_details">{{ description }}</div>
    </div>

    <div class="flex just-sb align-center" style="margin: 43px 0 0 0">
      <template v-for="(item, index) in projectList" :key="index">
        <div
          class="cardBac"
          :style="{ backgroundImage: `url('${item.backgroundImageUrl}')` }"
          @click="reGet(index)"
        >
          <div class="card_dec">
            <!-- <div class="card_num"> -->
            <p
              style="
                font-family: DIN, DIN;
                font-weight: bold;
                text-align: center;
                font-size: 20px;
                color: rgba(0, 0, 0, 0.65);
                margin: 0 6px 0 8px;
              "
            >
              {{ item.num }}
            </p>
            <!-- </div> -->
            <div
              style="
                font-weight: 400;
                font-size: 14px;
                color: rgba(0, 0, 0, 0.45);
                display: flex;
                align-items: center;
                margin-top: 11px;
              "
            >
              <p>{{ item.title }}</p>
            </div>
          </div>
          <div style="margin-right: 24px">
            <img :src="item.image" style="width: 88px; height: 88px" />
            <div v-show="!item.rate" style="margin-top: 8px">&nbsp;</div>
          </div>
        </div>
      </template>
    </div>
  </div>

  <div class="totaoText">
    <span @click="tabChange('1')" :class="{ activeBtn: sourceType === '1' }"
      >基础方案</span
    >
    <span @click="tabChange('2')" :class="{ activeBtn: sourceType === '2' }"
      >场景方案</span
    >
  </div>

  <table-list :sourceType="sourceType" :reGetList="reGetList"></table-list>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, watch } from "vue";
import tableList from "./components/tableList.vue";
import { getCount } from "@/api/solutionNew/home";
import backgroundImageUrl1 from "@/assets/images/home/<USER>";
import backgroundImageUrl2 from "@/assets/images/home/<USER>";
import backgroundImageUrl3 from "@/assets/images/home/<USER>";
import backgroundImageUrl4 from "@/assets/images/home/<USER>";
import image1 from "@/assets/images/home/<USER>";
import image2 from "@/assets/images/home/<USER>";
import image3 from "@/assets/images/home/<USER>";
import image4 from "@/assets/images/home/<USER>";
import { useRoute } from "vue-router";
export default defineComponent({
  name: "solveNew",
  components: {
    tableList,
  },
  setup(props, { emit }) {
    const data = reactive({
      title: "",
      description: "",
      reGetList: false,
      projectList: [
        {
          number: "",
          text: "",
          num: "",
          title: "基础方案上架数",
          textDec: "近七天上架方案数",
          backgroundImageUrl: backgroundImageUrl1,
          image: image1,
        },
        {
          number: "",
          text: "",
          num: "",
          rate: "",
          title: "方案场景上架数",
          textDec: "近七天复制数",
          // rateText: '复制率',
          backgroundImageUrl: backgroundImageUrl1,
          image: image1,
        },
        {
          number: "",
          text: "",
          num: "",
          rate: "",
          title: "方案查阅总次数",
          textDec: "近七天查阅数",
          backgroundImageUrl: backgroundImageUrl3,
          image: image3,
        },
        {
          number: "",
          text: "",
          num: "",
          title: "方案构建次数",
          textDec: "方案构建次数",
          backgroundImageUrl: backgroundImageUrl4,
          image: image4,
        },
      ],
      sourceType: "1",
    });
    const getCountDate = () => {
      if (route.query.activeNum) {
        data.sourceType = route.query.activeNum;
      }
      getCount().then((res) => {
        data.projectList[0].num =
          res.data.upCount.slice(0, -1) == 0 ? "-" : res.data.upCount;
        data.projectList[1].num =
          res.data.sceneCount.slice(0, -1) == 0 ? "-" : res.data.sceneCount;
        data.projectList[2].num =
          res.data.viewCount.slice(0, -1) == 0 ? "-" : res.data.viewCount; //组合
        data.projectList[3].num =
          res.data.combineCount.slice(0, -1) == 0 ? "-" : res.data.combineCount; //收藏
      });
    };

    const getNavigate = () => {
      let urlList = [];
      if (localStorage.getItem("urlList")) {
        urlList = JSON.parse(localStorage.getItem("urlList"));
      }
      urlList.forEach((item) => {
        if (item.linkUrl.indexOf(route.name) > -1) {
          data.title = item.title;
          data.description = item.description;
        }
      });
    };

    const route = useRoute();
    watch(
      () => route.query.activeNum,
      (newVal) => {
        data.sourceType = newVal;
      }
    );
    const tabChange = (type) => {
      data.sourceType = type;
    };

    const reGet = (index) => {
      console.log(index);
      if (index === 0) {
        data.reGetList = true;
      } else if (index === 1) {
        data.reGetList = true;
      } else {
        data.reGetList = false;
      }
      setTimeout(() => {
        data.reGetList = false;
      }, 1000);
    };

    getCountDate();
    getNavigate();
    return {
      ...toRefs(data),
      tabChange,
      reGet,
      route,
    };
  },
});
</script>

<style lang="scss" scoped>
.topImage {
  /*width: 100%;*/
  width: 1200px;
  margin: 0 auto;
  height: 400px;
  background-image: url("@/assets/images/home/<USER>");
  background-repeat: no-repeat;
  background-size: 100% 100%;

  .topImage_content {
    padding: 63px 0 16px 0;

    .topImage_title {
      font-weight: bold;
      font-size: 42px;
      color: #122c6c;
    }

    .topImage_details {
      width: 603px;
      font-size: 16px;
      color: #2b3f66;
      line-height: 32px;
      margin-top: 16px;
    }
  }

  .cardBac:nth-child(1) {
    margin-left: -18px;
  }

  .cardBac:nth-child(4) {
    margin-right: -18px;
  }

  .cardBac {
    flex: 1;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: space-around;
    &:hover {
      cursor: pointer;
    }
    .card_num {
      display: flex;
      align-items: flex-start;
    }
    .card_dec {
      margin: 24px 0 24px 24px;
      .dec_num {
        font-size: 40px;
        font-weight: bold;
        font-family: DIN, DIN;
        color: #2e7fff;
        text-shadow: 0px 4px 6px rgba(46, 127, 255, 0.25);
      }

      .dec_box {
        background-color: #ff5b00;
        border-radius: 50%;
        width: 18px;
        height: 18px;
        font-weight: 500;
        font-size: 12px;
        color: #ffffff;
        text-align: center;
        line-height: 18px;
      }
    }
  }
}

.totaoText {
  font-weight: bold;
  font-size: 28px;
  color: rgba(0, 0, 0, 0.85);
  text-align: center;
  margin-top: 80px;
  padding-bottom: 32px;
  margin-left: 118px;
  // border-bottom: 1px solid #DAE2F5;
  span {
    cursor: pointer;
    margin-right: 96px;
    padding-bottom: 16px;
  }
  .activeBtn {
    color: #0c70eb;
    border-bottom: 3px solid #0c70eb;
  }
}
</style>
