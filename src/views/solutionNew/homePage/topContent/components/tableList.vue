<template>
  <div class="">
    <div class="searchInfo flex" :class="switchOnOff ? contentType == 'K' ? 'AIBg' : 'AIBgShengTai' : 'commonBg'">
      <div class="vocationPull" style="flex: 1; height: 56px">
        <div class="switch">
          <div class="AIlogo"></div>
          <a-switch checked-children="on" un-checked-children="off" v-model:checked="switchOnOff" />
        </div>
        <a-config-provider :locale="zhCN" :getPopupContainer="(triggerNode) => triggerNode.parentNode">
          <div class="line"></div>

          <div class="lines"></div>
          <a-input v-model:value="name" class="inputClass" allow-clear height="56px" @keyup.enter="seekContent"
            :placeholder="switchOnOff ? contentType == 'K' ? '请输入方案名称、标签等关键词进行检索' : '请输入生态公司名称进行查询，或以“xx生态的xx方案”的形式进行指定生态的内容检索' : '请输入方案名称、标签等关键词进行检索'" />
          <div class="flex align-center">
            <div v-if="switchOnOff" class="chooseSearchType flex align-center ">
              <div class="chooseContent flex align-center just-sb"
                :class="contentType === 'K' ? 'border_blue' : 'border_green'">
                <div class="solutionBox flex align-center just-center pointer" :class="[
                  contentType === 'K' ? 'solutionBg' : '',
                  contentType === 'K' ? 'choosedWord' : 'nochoosedWord'
                ]" @click="chooseContentType('K')">
                  <div class="flex align-center just-center">
                    <div class="margin_r_4">
                      <img v-if="contentType === 'K'" src="../../../../../assets/images/AI/solutionIcon2.png" alt="">
                      <img v-else src="../../../../../assets/images/AI/solutionIcon1.png" alt="">
                    </div>
                    <div>{{ sourceType == '1' ? '方案' : '场景' }}</div>
                  </div>
                </div>
                <div class="shengtaiBox flex align-center just-center pointer" :class="[
                  contentType === 'E' ? 'shengtaiBg' : '',
                  contentType === 'E' ? 'choosedWord' : 'nochoosedWord'
                ]" @click="chooseContentType('E')">
                  <div class="flex align-center just-center">
                    <div class="margin_r_4">
                      <img v-if="contentType === 'E'" src="../../../../../assets/images/AI/shengtai2.png" alt="">
                      <img v-else src="../../../../../assets/images/AI/shengtai1.png" alt="">
                    </div>
                    <div>生态</div>
                  </div>
                </div>
              </div>
            </div>
            <!--<voiceRecorder v-if="switchOnOff" :isTranslating="isTranslating" :canBtnUse="canBtnUse"
            @audioReady="handleAudio" />-->
            <div style="position: relative;">
              <div class="seekInfo border_top_right_5" :class="[
                switchOnOff ? contentType == 'K' ? 'AIbtn' : 'AIbtnShengTai' : 'commonBtn'
              ]" @click="seekContent()">
                <img src="@/assets/images/home/<USER>" />
                <div>搜索</div>
              </div>
            </div>
          </div>

          <!--<div class="customized pointer" :class="switchOnOff ? 'changeContentBtn' : 'commonChangeBtn'"
            @click="changeContent()">
            <div>{{ newContentName }}</div>
          </div>-->
          <!-- <div class="AISearchLogo pointer" @click="getAIList()">
            <img src="@/assets/images/home/<USER>" alt="">
          </div> -->
        </a-config-provider>
      </div>
    </div>
    <div class="newLoading" v-if="AIsearch">
      <loadingSmall />
    </div>
    <div class="selectData" ref="selectData" v-if="true || !switchOnOff">
      <div :class="[
        'selcet_box',
        { showMore: showScense == 'label' && showIndex === index },
      ]" v-for="(val, index) in vocationList" :key="index">
        <div class="left_select" :style="{
          'background-color': val.level === 2 ? '#F3F8FF' : (val.level === 3 ? '#DDEAFF' : '#FFFFFFFF'),
        }">{{ val.label }}：</div>
        <div :class="[
          'right_select',
          { showHidden: showScense == 'label' && showIndex === index },
        ]" :style="{
          'background-color': val.level === 2 ? '#F3F8FF' : (val.level === 3 ? '#DDEAFF' : '#FFFFFFFF'),
        }">
          <span v-if="val.label == '行业'" v-for="(value, key1) in val.children" :key="key1"
            :class="{ activeBtn: providerSelect.includes(value.label) }" style="height: 49px">
            <div class="title" @click="providerNewBtn(value, 'default', index)" :style="{
              'background-color': providerSelect.includes(value.label) ? '#F3F8FF' : '#FFFFFFFF',
            }">
              {{ value.label }}
            </div>
          </span>
          <span v-else-if="val.label == '提供方'" v-for="(value, key3) in val.children" :key="key3"
            :class="{ activeBtn: providerName === value.label }" style="height: 49px">
            <div class="title" @click="providerClick(value, index)">
              {{ value.label }}
            </div>
          </span>
          <span v-else v-for="(value, key2) in val.children" :key="key2"
            :class="{ activeBtn: selectList.indexOf(value.label) > -1 }" :style="{
              height: showLast && showId == value.value ? '89px' : '49px',
            }">
            <div v-if="val.level !== 2 && val.level !== 3" class="title" @click="labelSelect(value, 'default', index)">
              {{ value.label }}
            </div>
            <div class="last_data" v-if="val.level !== 2 && val.level !== 3 && showLast && showId == value.value">
              <span v-for="(e, i) in value.children" @click="labelSelect(e, 'last')"
                :class="{ activeBtn: selectList.indexOf(e.label) > -1 }" :key="i" style="width: 60px">
                {{ e.label }}
              </span>
            </div>
          </span>
          <span ref="box" v-if="val.level === 2" v-for="(value, key1) in val.children" :key="key1"
            :class="{ activeBtn: providerSelect.includes(value.label) }" style="height: 49px;" :style="{
              'background-color': providerSelect.includes(value.label) ? '#DDEAFF' : '#F3F8FF',
            }">
            <div class="title" @click="providerNewNewBtn(value, 'default', index)">
              <!--@mouseenter="providerEnter(value, index)"-->
              {{ value.label }}
            </div>
          </span>
          <span ref="box" v-if="val.level === 3" v-for="(value, key1) in val.children" :key="key1"
            :class="{ activeBtn: providerSelect.includes(value.label) }" :style="{
              'height': '49px',
              'background-color': providerSelect.includes(value.label) ? '#DDEAFF' : 'F3F8FF'
            }">
            <div class="title" @click="providerNewNewNewBtn(value, 'default', index)">
              {{ value.label }}
            </div>
          </span>
        </div>
        <span class="more flex" v-if="val.children && val.children.length > 8 && showIndex !== index"
          @click="showMore('label', index)">更多<img src="@/assets/images/solution/home/<USER>" alt="" /></span>
        <span class="more flex" v-if="val.children && val.children.length > 8 && showIndex === index"
          @click="showless('label_less', index)">收起<img src="@/assets/images/solution/home/<USER>" alt="" /></span>
      </div>
      <div v-if="vocationList[1]" v-for="(value, key1) in vocationList[1].children"
        @mouseleave="providerLeave(value, key1)">
        <div class="last_data_top" :style="getBoxTitle(key1)" v-if="showLast && showId == value.value"
          @click="providerBtn(value, 'default', key1)">
          {{ value.label }}
        </div>
        <div class="last_data" v-if="showLast && showId == value.value && value.children" :style="getBoxLeft(key1)">
          <!--{ left: -135 * key1 + 'px' }-->
          <span v-for="(e, i) in value.children" @click="providerBtn(e, 'last', i, value)"
            style="width: auto; padding: 11px 20px; cursor: pointer"
            :class="{ activeBtn: providerSelect.indexOf(e.label) > -1 }" :key="i">
            {{ e.label }}
          </span>
        </div>
      </div>

      <div class="selectData" v-if="(sourceType == '1' && contentType == 'K') || (contentType != 'K' && !switchOnOff)" style="z-index: 2000;">
        <div :class="['selcet_box']">
          <div class="left_select">{{ "生态合作方" }}：</div>
          <div :class="['right_select']">
            <a-input v-model:value="searchContent.label" placeholder="请输入生态合作方名称" allowClear @change="searchChange"
              @click="clickOn" @blur="clickBlur" />
            <!-- searchShow && searchList.length > 0 -->
          </div>
        </div>
        <div class="searchList" v-if="searchShow && searchList && searchList.length > 0">
          <p v-for="(item, index) in searchList" :key="index" @mousedown="setName(item)">
            {{ item.name }}
          </p>
        </div>
      </div>

      <div class="select_boot flex">
        <div>
          已选条件：
          <span v-if="providerSelect.length > 0">行业：</span>
          <span v-for="(val, index) in providerSelect" :key="index">
            <span style="margin-left: 8px">
              {{ val }}
            </span>
            <img src="@/assets/images/solution/home/<USER>" alt=""
              style="width: 16px; height: 16px; cursor: pointer" @click="deleteSelect(val, index, 'pro')" />
          </span>
          <span class="label" v-if="selectList.length > 0">标签：</span>
          <span v-for="(item, index) in selectList" :key="key">
            <span style="margin-left: 8px">
              {{ item }}
            </span>
            <img src="@/assets/images/solution/home/<USER>" alt=""
              style="width: 16px; height: 16px; cursor: pointer" @click="deleteSelect(item, index)" />
          </span>
          <span v-if="providerName !== ''">提供方：</span>
          <span v-if="providerName !== ''">
            <span style="margin-left: 8px">
              {{ providerName }}
            </span>
            <img src="@/assets/images/solution/home/<USER>" alt=""
              style="width: 16px; height: 16px; cursor: pointer" @click="deleteSelect(providerName, 0, 'provider')" />
          </span>
        </div>
        <div class="right_con">
          共找到 <span>{{ totalItemCount }}</span> 条结果
        </div>
      </div>
    </div>

    <!-- 热门方案 -->
    <!-- <div class="tabContent">
      <div v-if="activeKey != '' && hotTableList && hotTableList.length > 0" style="width: 100%">
        <div style="padding: 24px 24px 0 24px;">热门方案</div>
        <div class="cardContent">
          <div class="card_total flex-1">
            <template v-for="(item, index) in hotTableList" :key="index">
              <div :class="[
                'card_content',
                {
                  cardActive: cardActive == index,
                  rightActive: index % 2 != 0,
                  cardObvious: index < 2 && hotTableList.length < 3,
                  bottomLine:
                    (index == hotTableList.length - 1 ||
                      index == hotTableList.length - 2) &&
                    index > 1,
                },
              ]" @mouseenter="contentColor(index)" @mouseleave="contentLeave" @click="proDetail(item)">
                <div style="display: flex; margin: 24px">
                  <div>
                    <div
                      style="
                        width: 168px;
                        height: 105px;
                        text-align: center;
                        position: relative;
                      " :style="backgroundStyles()">
                      <p style="
                          font-weight: 700;
                          display: block;
                          color: #1F82C8;
                          position: absolute;
                          left: 50%;
                          top: 50%;
                          transform: translate(-50%, -50%);
                          font-size: 10px;
                        ">
                        {{ item.name }}
                      </p>
                    </div>
                  </div>
                  <div class="card_center">
                    <div class="card_text">
                      <div class="card_tag">
                        <div class="card_title">{{ item.name }}</div>
                      </div>
                      <a-tag v-if="sourceType == 1" :bordered="false" class="cityStyle">{{ item.provider }}</a-tag>
                    </div>
                    <div class="card_des">
                      {{ item.description }}{{ item.summary }}
                    </div>
                    <div class="flex" style="justify-content: space-between">
                      <div class="flex">
                        <a-tag color="#D7E6FF" v-if="item.labelName && item.labelName[0]" style="
                            display: block;
                            color: rgba(0, 0, 0, 0.45);
                            background-color: transparent;
                            border: 1px solid #d9d9d9;
                            line-height: 17px;
                          ">{{ item.labelName[0] }}</a-tag>
                        <a-tag color="#D7E6FF" v-if="item.labelName && item.labelName[1]" style="
                            display: block;
                            color: rgba(0, 0, 0, 0.45);
                            background-color: transparent;
                            border: 1px solid #d9d9d9;
                            line-height: 17px;
                          ">{{ item.labelName[1] }}</a-tag>
                      </div>
                    </div>
                    <div style="
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                      ">
                      <div style="
                          display: flex;
                          align-items: center;
                          justify-content: space-between;
                          margin-right: 5px;
                        ">
                        <div style="
                            display: flex;
                            align-items: center;
                            margin-right: 18px;
                          ">
                          <img src="@/assets/images/home/<USER>" style="width: 16px; height: 16px" />
                          <span style="font-size: 12px; color: rgba(0, 0, 0, 0.45)" v-if="item.viewCount">{{
                            item.viewCount }}</span>
                          <span v-else>-</span>
                        </div>
                        <div style="display: flex; align-items: center">
                          <img src="@/assets/images/home/<USER>" style="width: 16px; height: 16px" />
                          <span style="font-size: 12px; color: rgba(0, 0, 0, 0.45)" v-if="item.downloadCount">{{
                            item.downloadCount }}</span>
                          <span v-else>-</span>
                        </div>
                      </div>
                      <div v-if="item.isSensitive != 1">
                        <button class="cart-button" disabled v-if="item.addCart">
                          <span class="add" style="color: rgba(0, 0, 0, 0.9)">
                            &nbsp;已加入</span>
                        </button>
                        <button class="cart-button pointer" v-else @click.stop="add(item.id)">
                          <img v-if="!item.addCart" class="add-icon" src=" @/assets/images/AI/isadded.png" /><span
                            class="add"> &nbsp;加入预选</span>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
</div>
</div>
</div>
</div> -->

    <div class="tabContent">
      <div v-if="tableList && tableList.length > 0" style="width: 100%">
        <div class="AITips flex align-center" v-if="!showPagination">
          <img style="width: 40px; height: 40px; margin-right: 10px" src="../../../../../assets/images/AI/ai.png"
            alt="" />
          <div class="words">以下是AI助手为您找到的相关结果</div>
        </div>
        <div class="cardContent">
          <div class="card_total flex-1">
            <template v-for="(item, index) in tableList" :key="index">
              <div :class="[
                'card_content',
                {
                  cardActive: cardActive == index,
                  rightActive: index % 2 != 0,
                  cardObvious: index < 2 && tableList.length < 3,
                  bottomLine:
                    (index == tableList.length - 1 ||
                      index == tableList.length - 2) &&
                    index > 1,
                },
              ]" @mouseenter="contentColor(index)" @mouseleave="contentLeave" @click="proDetail(item)">
                <div class="listTag">
                  <!--<img v-if="item.isEcologyOrAbility==1" src="@/assets/images/newProject/classify6.png" alt="" />-->
                  <img v-if="item.ecologyType && item.ecologyType.split(',').includes('1')"
                    src="@/assets/images/newProject/classify8.png" alt="" />
                </div>
                <div style="display: flex; margin: 24px">
                  <div>
                    <!-- <img
                      v-if="item.logo"
                      v-lazy="`${item.logo}`"
                      style="width: 168px; height: 105px"
                    /> -->
                    <div style="
                        width: 168px;
                        height: 105px;
                        text-align: center;
                        position: relative;
                      " :style="backgroundStyles()">
                      <p style="
                          font-weight: 700;
                          display: block;
                          color: #1f82c8;
                          position: absolute;
                          left: 50%;
                          top: 50%;
                          transform: translate(-50%, -50%);
                          font-size: 10px;
                        ">
                        {{ item.name }}
                      </p>
                    </div>
                  </div>
                  <div class="card_center">
                    <div class="card_text">
                      <div style="display: flex;align-items: center;">
                        <div class="sensitive" v-if="item.isSensitive == 1">敏感</div>
                        <div class="card_tag">
                          <!--<a-tag color="#D7E6FF">{{ item.categoryName }}</a-tag>-->
                          <div class="card_title">{{ item.name }}</div>
                        </div>
                      </div>

                      <a-tag v-if="sourceType == 1" :bordered="false" class="cityStyle">{{ item.provider }}</a-tag>
                    </div>
                    <div class="card_des">
                      {{ item.description || item.summary }}
                    </div>
                    <div class="flex" style="justify-content: space-between">
                      <div class="flex">
                        <a-tag color="#D7E6FF" v-if="item.labelName && item.labelName[0]" style="
                            display: block;
                            color: rgba(0, 0, 0, 0.45);
                            background-color: transparent;
                            border: 1px solid #d9d9d9;
                            line-height: 17px;
                          ">{{ item.labelName[0] }}</a-tag>
                        <a-tag color="#D7E6FF" v-if="item.labelName && item.labelName[1]" style="
                            display: block;
                            color: rgba(0, 0, 0, 0.45);
                            background-color: transparent;
                            border: 1px solid #d9d9d9;
                            line-height: 17px;
                          ">{{ item.labelName[1] }}</a-tag>
                      </div>
                    </div>
                    <div style="
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                      ">
                      <div style="
                          display: flex;
                          align-items: center;
                          justify-content: space-between;
                          margin-right: 5px;
                        ">
                        <div style="
                            display: flex;
                            align-items: center;
                            margin-right: 18px;
                          ">
                          <img src="@/assets/images/home/<USER>" style="width: 16px; height: 16px" />
                          <span style="font-size: 12px; color: rgba(0, 0, 0, 0.45)" v-if="item.viewCount">{{
                            item.viewCount }}</span>
                          <span v-else>-</span>
                        </div>
                        <div style="display: flex; align-items: center">
                          <img src="@/assets/images/home/<USER>" style="width: 16px; height: 16px" />
                          <span style="font-size: 12px; color: rgba(0, 0, 0, 0.45)" v-if="item.downloadCount">{{
                            item.downloadCount }}</span>
                          <span v-else>-</span>
                        </div>
                      </div>
                      <div v-if="sourceType === '1' && item.isSensitive != 1">
                        <button class="cart-button" disabled v-if="item.addCart">
                          <span class="add" style="color: rgba(0, 0, 0, 0.9)">
                            &nbsp;已加入</span>
                        </button>
                        <button class="cart-button pointer" v-else @click.stop="add(item.id, index)">
                          <img v-if="!item.addCart" class="add-icon" src=" @/assets/images/AI/isadded.png" /><span
                            class="add"> &nbsp;加入预选</span>
                        </button>
                      </div>
                      <div v-if="sourceType === '2' && item.pptName">
                        <button class="cart-button" disabled v-if="item.addCart">
                          <span class="add" style="color: rgba(0, 0, 0, 0.9)">
                            &nbsp;已加入</span>
                        </button>
                        <button class="cart-button pointer" v-else @click.stop="add(item.id, index)">
                          <img v-if="!item.addCart" class="add-icon" src=" @/assets/images/AI/isadded.png" /><span
                            class="add"> &nbsp;加入预选</span>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>
        <div class="layPage">
          <a-pagination v-model:pageSize="pageItemSize" v-model:current="currentPage" :pageSizeOptions="pageSizeOptions"
            show-quick-jumper show-size-changer :total="totalItemCount" @change="pageChange"
            @showSizeChange="sizeChange" class="mypage" />
        </div>
      </div>
      <div v-if="tableList.length == 0" class="emptyPhoto">
        <img src="@/assets/images/home/<USER>" />
      </div>
      <div class="loading" v-show="loadingShow">
        <a-spin />
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, ref, watch } from "vue";
import { useRouter } from "vue-router";
import {
  getTradeList,
  getsolutionList,
  getLabelTreeList,
  getTeamTree,
  getHotList,
  getSearchList,
} from "@/api/solutionNew/home";
import { useHomeStore } from "@/store";
import bac from "@/assets/images/noDataBac.png";
import { addShoppingCart } from "@/api/combine/shoppingCart.js";
import voiceRecorder from "@/components/voiceRecorder/voiceRecorder.vue";

import loadingSmall from "@/components/superLoadingSmall/loadingSmall.vue";

import zhCN from "ant-design-vue/es/locale/zh_CN";
import { getMakeUrl } from "@/utils/getUrl";
import eventBus from "@/utils/eventBus";
import { getSceneSchemeList, getNewSceneSchemeList } from "@/api/moduleList/home";
import { AISearch, AIvoice, getNewAssociate } from "@/api/AI/ai.js";
import { DownOutlined } from '@ant-design/icons-vue';
import { el } from "element-plus/es/locale/index.mjs";
export default defineComponent({
  components: {
    voiceRecorder,
    loadingSmall,
    DownOutlined,
  },
  props: {
    sourceType: {
      type: String,
      default: "1",
    },
    reGetList: {
      type: Boolean,
      default: false,
    },
  },
  setup(props, { emit }) {
    const baseURL = getMakeUrl();
    const vocation = ref("");
    const box = ref(null);
    const selectData = ref(null);
    const searchList = ref([]);
    const searchShow = ref(false);
    const data = reactive({
      searchContent: {
        label: "",
        id: "",
      },
      hotTableList: [], //热门方案
      name: "",
      providerName: "",
      AIsearch: false,
      moment: "",
      backgroundImage: bac,
      sourceType: props.sourceType,
      loadingShow: true,
      showLabel: true,
      activeKey: "",
      cardActive: "-1",
      pageSizeOptions: ["10", "20", "30", "50"],
      totalItemCount: 0,
      tabList: [],
      vocationList: [],
      tableList: [],
      tableAIAllList: [],
      filterArr: [],// ai搜索过滤用
      totalNum: 0,
      totalItemCount1: 0,
      currentPage: 1,
      pageItemSize: 10,
      labelIdlist: [],
      showLast: false,
      showId: undefined,
      selectList: [],
      selectListNew: [],
      selectListOld: [],
      showMore: false,
      showScense: "",
      morePro: true,
      providerSelect: [],
      showIndex: "",
      showPagination: true,
      switchOnOff: true,
      isTranslating: false,
      canBtnUse: false,
      AISearchType: 2,
      newContentName: "手动定制",
      contentType: "K",
      searchText: "搜索",
      showEcoSearch: false,
      showDownIcon: true,
      isHover: false,
    });
    watch(
      () => props.reGetList,
      (val) => {
        if (val) {
          console.log(1);
          data.activeKey = "";
          data.showLast = false;
          data.showId = "";
          data.name = "";
          data.labelIdlist = [];
          data.providerSelect = [];
          data.selectList = [];
          data.selectListNew = [];
          data.selectListOld = [];
          data.currentPage = "1";
          data.pageItemSize = "10";
          getList();
        }
      }
    );

    watch(
      () => props.sourceType,
      (val) => {
        data.contentType = 'K'
        if (val == "1") {
          data.AISearchType = 2;
        } else if (val == "2") {
          data.AISearchType = 8;
        }
        data.sourceType = val;
        data.activeKey = "";
        data.showLast = false;
        data.showId = "";
        data.name = "";
        data.labelIdlist = [];
        data.providerSelect = [];
        data.selectList = [];
        data.selectListNew = [];
        data.selectListOld = [];
        data.currentPage = "1";
        data.pageItemSize = "10";
        getList();
      }
    );

    // 获取热门方案
    const getHotProgramList = () => {
      getHotList({
        pageNo: 1,
        pageSize: 10,
        industryId: data.activeKey,
        shelfStatus: 1,
        type: 1,
      })
        .then((res) => {
          // console.log('res222',res,data.activeKey);
          if (res.code == 200) {
            data.hotTableList = res.data.rows.slice(0, 4);
          }
        })
        .catch((err) => { });
    };
    const getList = () => {
      data.tableList = [];
      data.totalItemCount = 0;
      if (data.sourceType === "2") {
        let pageParams = {
          pageNo: data.currentPage,
          pageSize: data.pageItemSize,
          keyword: data.name,
          industryId:
            data.selectListOld.length == 3
              ? data.selectListOld[2]
              : data.selectListOld.length == 2
                ? data.selectListOld[1]
                : data.selectListOld.length == 1
                  ? data.selectListOld[0]
                  : "",
          labelIds: data.labelIdlist,
          provider: data.providerName,
          //type: 5,
          shelfStatus: 1
        };
        data.loadingShow = true;
        //getSceneSchemeList(pageParams)
        getNewSceneSchemeList(pageParams)
          .then((res) => {
            data.showPagination = true;
            data.loadingShow = false;
            data.tableList = [];
            data.tableList = res.data.rows;
            data.totalItemCount = res.data.totalRows;
            data.tableList.map((item) => {
              item.description = item.summary;
              item.logo = item.imageUrl;
            });

            if (data.activeKey == "") {
              data.totalItemCount1 = res.data.totalRows;
            }
          })
          .catch((error) => {
            data.loadingShow = false;
          });
      } else {
        let pageParams = {
          pageNo: data.currentPage,
          pageSize: data.pageItemSize,
          keyword: data.name,
          industryId:
            data.selectListOld.length == 3
              ? data.selectListOld[2]
              : data.selectListOld.length == 2
                ? data.selectListOld[1]
                : data.selectListOld.length == 1
                  ? data.selectListOld[0]
                  : "",
          labelIds: data.labelIdlist,
          provider: data.providerName,
          ecopartnerId: data.searchContent.id,
        };
        data.loadingShow = true;
        getsolutionList(pageParams)
          .then((res) => {
            data.showPagination = true;
            data.loadingShow = false;
            data.tableList = [];
            data.tableList = res.data.rows;
            data.totalItemCount = res.data.totalRows;
            data.tableList.forEach((item) => {
              item.labelName = item.labelName.split(",");
              item.provider = item.provider.split("/")[0];
            });
            if (data.activeKey == "") {
              data.totalItemCount1 = res.data.totalRows;
            }
          })
          .catch((error) => {
            data.loadingShow = false;
          });
      }
    };
    getList();
    const providerList = () => {
      getLabelTreeList().then((res) => {
        data.vocationList = res.data.map((item) => ({
          label: item.name,
          value: item.id,
          length: item.children ? item.children.length : 0,
          children: item.children
            ? item.children.map((child) => ({
              label: child.name,
              value: child.id,
              level: 2,
              children: child.children
                ? child.children.map((ele) => ({
                  label: ele.name,
                  value: ele.id,
                  level: 3,
                  children: ele.children
                    ? ele.children.map((three) => ({
                      label: three.name,
                      value: three.id,
                      level: 4,
                    }))
                    : undefined,
                }))
                : undefined,
            }))
            : undefined,
        }));
        data.vocationList = data.vocationList.filter(item => item.label !== '专项领域');
        getTeamTree().then((resTree) => {
          console.log(resTree);
          let treeList = [];
          resTree.data.forEach((item) => {
            item.label = item.name;
            item.value = item.id;
            treeList.push(item);
          });
          data.vocationList.push({
            label: "提供方",
            children: treeList,
          });
          console.log(data.vocationList);
        });
        //console.log(data.vocationList);
        //      data.vocationList = data.vocationList.slice(1);
      });
    };
    providerList();
    const seekContent = () => {
      data.currentPage = 1;
      if (data.switchOnOff) {
        getAIList();
      } else {
        getList();
      }
    };
    const changeSearch = () => {
      if (data.sourceType == '1') {
        data.contentType = data.contentType == 'K' ? 'E' : 'K';
        data.searchText = data.contentType == 'K' ? '搜方案' : '搜生态';
        seekContent();
      }
    }
    const add = (id, index) => {
      let addParams = {
        schemeId: id,
        type: "1",
      };
      if (data.sourceType == "1") {
        addParams.type = "1";
      } else {
        addParams.type = "3";
      }
      addShoppingCart(addParams).then((res) => {
        refreshList();
        eventBus.emit("cartRefresh");
      });
    };
    const tabChange = (val) => {
      if (val.value !== data.activeKey) {
        data.activeKey = vocation.value = val.value;
        data.currentPage = 1;
        getList();
      }
    };
    const contentColor = (index) => {
      data.cardActive = index;
    };
    const router = useRouter();
    const proDetail = (val) => {
      if (data.sourceType == "1") {
        router.push({
          query: {
            id: val.id,
          },
          name: "solveDetailNew",
        });
      } else {
        router.push({
          query: {
            id: val.id,
            activeBtn: 2,
          },
          name: "applyNew",
        });
      }
    };
    const contentLeave = () => {
      data.cardActive = "-1";
    };
    const pageChange = (page, pageSize) => {
      data.currentPage = page;
      if (data.showPagination) {
        getList();
      } else {
        getAIPageList();
      }
    };
    const sizeChange = (current, size) => {
      data.pageItemSize = size;
      if (data.showPagination) {
        getList();
      } else {
        getAIPageList();
      }
    };
    const labelChange = (val) => {
      data.labelIdlist = val.join(",");
    };
    const getTarde = () => {
      let tradeParams = {};
      getTradeList(tradeParams).then((result) => {
        result.data.map((item) => {
          data.tabList.push({
            label: item.name,
            value: item.id,
          });
        });
      });
    };

    const aiFilter = () => {
      let industryId = data.selectListOld.length == 3
        ? data.selectListOld[2]
        : data.selectListOld.length == 2
          ? data.selectListOld[1]
          : data.selectListOld.length == 1
            ? data.selectListOld[0]
            : ""
      if (data.providerName != '') {
        data.filterArr = data.tableAIAllList.filter(item => {
          return item.provider.includes(data.providerName)
        })
      }
      if (data.selectList.length > 0) {
        data.filterArr = data.tableAIAllList.filter(item => {
          let arr = item.labelId.split(',')
          return arr.some(item1 => data.selectListNew.includes(item1 * 1))
        })
      }
      if (industryId != '') {
        data.filterArr = data.tableAIAllList.filter(item => {
          return item.industryIdReq.includes(industryId * 1)
        })
      }
      if (data.providerName != '' && data.selectList.length > 0) {
        let arr = data.tableAIAllList.filter(item => {
          return item.provider.includes(data.providerName)
        })
        data.filterArr = arr.filter(item => {
          let ar = item.labelId.split(',')
          return ar.some(item1 => data.selectListNew.includes(item1 * 1))
        })
      }
      if (data.providerName != '' && industryId != '') {
        let arr = data.tableAIAllList.filter(item => {
          return item.provider.includes(data.providerName)
        })
        data.filterArr = arr.filter(item => {
          return item.industryIdReq.includes(industryId * 1)
        })
      }
      if (industryId != '' && data.selectList.length > 0) {
        let arr = data.tableAIAllList.filter(item => {
          return item.industryIdReq.includes(industryId * 1)
        })
        data.filterArr = arr.filter(item => {
          let ar = item.labelId.split(',')
          return ar.some(item1 => data.selectListNew.includes(item1 * 1))
        })
      }
      if (data.providerName != '' && data.selectList.length > 0 && industryId != '') {
        let arr = data.tableAIAllList.filter(item => {
          return item.provider.includes(data.providerName)
        })
        let arr1 = arr.filter(item => {
          let ar = item.labelId.split(',')
          return ar.some(item1 => data.selectListNew.includes(item1 * 1))
        })
        data.filterArr = arr1.filter(item => {
          return item.industryIdReq.includes(industryId * 1)
        })
      }
      if (data.providerName == '' && data.selectList.length == 0 && industryId == '') {
        data.filterArr = data.tableAIAllList
      }
      let firstPageNum = (data.currentPage - 1) * data.pageItemSize;
      let lastPageNum = data.currentPage * data.pageItemSize;
      data.tableList = data.filterArr.slice(firstPageNum, lastPageNum);
      data.totalItemCount = data.filterArr.length
    }

    const labelSelect = (value, type = "default", index) => {
      if (value.children && type !== "last") {
        if (data.selectList.includes(value.label)) {
          data.showLast = false;
        } else {
          data.showLast = true;
        }
        data.showId = value.value;
        data.showScense = "label";
        data.showIndex = index;
      }
      if (data.selectList.includes(value.label)) {
        const index = data.selectList.findIndex((item) => item === value.label);
        if (index !== -1) {
          data.selectList.splice(index, 1);
        }
        const index1 = data.selectListNew.findIndex(
          (item) => item === value.value
        );
        if (index1 !== -1) {
          data.selectListNew.splice(index, 1);
        }
      } else {
        data.selectList.push(value.label);
        data.selectListNew.push(value.value);
      }
      data.selectList = data.selectList.filter((value, index, self) => {
        return self.indexOf(value) === index;
      });
      data.selectListNew = data.selectListNew.filter((value, index, self) => {
        return self.indexOf(value) === index;
      });
      data.labelIdlist = data.selectListNew.join(",");
      data.currentPage = 1;
      if (data.switchOnOff) {
        if (data.name != '') {
          aiFilter()
        } else {
          data.currentPage = 1;
          getList();
        }
      } else {
        data.currentPage = 1;
        getList();
      }
      //    }
    };

    const providerNewBtn = (value, type = "default", index, parvalue) => {
      if (value.level == 2) {
        if (data.providerSelect.includes(value.label)) {
          if (data.providerSelect.length == 1 && data.vocationList.length > 3) {
            data.vocationList.splice(1, 1);
          } else if (data.providerSelect.length >= 2 && data.vocationList.length > 3) {
            if (data.vocationList[2].level) {
              data.vocationList.splice(2, 1);
            }
            data.vocationList.splice(1, 1);
          } else { }
          data.providerSelect = [];
          data.selectListOld = [];
        } else {
          if (data.vocationList[1].level) {
            console.log(data.providerSelect);
            if (data.providerSelect.length == 2) {
              if (data.vocationList[2].level) {
                data.vocationList.splice(2, 1);
              }
            } else if (data.providerSelect.length >= 3) {
              data.vocationList.splice(2, 1);
            } else { }
            data.providerSelect = [value.label];
            data.selectListOld = [value.value];
            data.vocationList[1] = value;
          } else {
            data.providerSelect.push(value.label);
            data.selectListOld.push(value.value);
            data.vocationList.splice(1, 0, value);
          }
        }
      }
      data.labelIdlist = data.selectListNew.join(",");
      data.currentPage = 1;
      if (data.switchOnOff) {
        if (data.name != '') {
          aiFilter()
        } else {
          data.currentPage = 1;
          getList();
        }
      } else {
        data.currentPage = 1;
        getList();
      }
    }

    const providerNewNewBtn = (value, type = "default", index, parvalue) => {
      if (value.level == 3) {
        if (data.providerSelect.includes(value.label)) {
          // return
          if (data.providerSelect.length == 2 && value.children) {
            data.vocationList.splice(2, 1);
            data.providerSelect.splice(1, 1);
            data.selectListOld.splice(1, 1);
          } else if (data.providerSelect.length == 2 && !value.children) {
            data.vocationList.splice(1, 1);
            data.providerSelect.splice(1, 1);
            data.selectListOld.splice(1, 1);
          } else if (data.providerSelect.length >= 3) {
            data.vocationList.splice(2, 1);
            data.providerSelect.splice(2, 1);
            data.providerSelect.splice(1, 1);
            data.selectListOld.splice(2, 1);
            data.selectListOld.splice(1, 1);
          } else { }
        } else {
          if (data.vocationList[2].level) {
            data.providerSelect = [data.providerSelect[0], value.label];
            data.selectListOld = [data.selectListOld[0], value.value];
            if (value.children) {
              data.vocationList[2] = value;
            } else {
              data.vocationList.splice(2, 1);
            }
          } else {
            data.providerSelect = [data.providerSelect[0], value.label];
            data.selectListOld = [data.selectListOld[0], value.value];
            if (value.children) {
              data.vocationList.splice(2, 0, value);
            }
          }
        }
      }
      data.labelIdlist = data.selectListNew.join(",");
      data.currentPage = 1;
      if (data.switchOnOff) {
        if (data.name != '') {
          aiFilter()
        } else {
          data.currentPage = 1;
          getList();
        }
      } else {
        data.currentPage = 1;
        getList();
      }
    }
    const providerNewNewNewBtn = (value, type = "default", index, parvalue) => {
      if (value.level == 4) {
        if (data.providerSelect.includes(value.label)) {
          if (data.providerSelect.length == 3) {
            // data.vocationList.splice(3, 1);
            data.providerSelect.splice(2, 1);
            data.selectListOld.splice(2, 1);
          } else if (data.providerSelect.length == 4) {
            // data.vocationList.splice(3, 1);
            data.providerSelect.splice(3, 1);
            data.providerSelect.splice(2, 1);
            data.selectListOld.splice(3, 1);
            data.selectListOld.splice(2, 1);
          }
        } else {
          if (data.vocationList[3].level) {
            data.providerSelect[2] = value.label;
            data.selectListOld[2] = value.value;
            data.vocationList[3] = value;
          } else {
            data.providerSelect[2] = value.label;
            data.selectListOld[2] = value.value;
          }
        }
      }
      data.labelIdlist = data.selectListNew.join(",");
      data.currentPage = 1;
      if (data.switchOnOff) {
        if (data.name != '') {
          aiFilter()
        } else {
          data.currentPage = 1;
          getList();
        }
      } else {
        data.currentPage = 1;
        getList();
      }
    }

    const providerBtn = (value, type = "default", index, parvalue) => {
      if (type == "last") data.showLast = false;
      if (value.children && type !== "last") {
        data.showId = value.value;
        data.showScense = "label";
        //data.showIndex = index;
      }
      if (type != "last") {
        data.activeKey = vocation.value = value.value;
        // getHotProgramList()
        if (data.providerSelect.includes(value.label)) {
          data.providerSelect = [];
          data.selectListOld = [];
          data.activeKey = "";
          data.showLast = false;
        } else {
          if (value.children) {
            data.showLast = true;
          } else {
            data.showLast = false;
          }
          data.providerSelect = [];
          data.selectListOld = [];
          data.providerSelect.push(value.label);
          data.selectListOld.push(value.value);
          data.providerSelect = data.providerSelect.filter(
            (value, index, self) => {
              return self.indexOf(value) === index;
            }
          );
          data.selectListOld = data.selectListOld.filter(
            (value, index, self) => {
              return self.indexOf(value) === index;
            }
          );
        }
      } else {
        data.activeKey = parvalue.value;
        if (data.providerSelect.includes(value.label)) {
          data.providerSelect = [parvalue.label];
          data.selectListOld = [parvalue.value];
        } else {
          data.providerSelect = [parvalue.label];
          data.providerSelect.push(value.label);
          data.providerSelect = data.providerSelect.filter(
            (value, index, self) => {
              return self.indexOf(value) === index;
            }
          );
          data.selectListOld = [parvalue.value];
          data.selectListOld.push(value.value);
          data.selectListOld = data.selectListOld.filter(
            (value, index, self) => {
              return self.indexOf(value) === index;
            }
          );
        }
      }
      data.labelIdlist = data.selectListNew.join(",");
      data.currentPage = 1;
      if (data.switchOnOff) {
        if (data.name != '') {
          aiFilter()
        } else {
          data.currentPage = 1;
          getList();
        }
      } else {
        data.currentPage = 1;
        getList();
      }
    };

    const providerEnter = (val, index) => {
      console.log(val, index);
      data.showId = val.value;
      data.showLast = true;
    };

    const providerLeave = (val, index) => {
      console.log(val, index);
      data.showId = "";
      data.showLast = false;
    };

    const providerClick = (val, index) => {
      data.currentPage = 1;
      if (data.providerName == val.label) {
        data.providerName = "";
        if (data.switchOnOff) {
          if (data.name != '') {
            aiFilter()
          } else {
            data.currentPage = 1;
            getList();
          }
        } else {
          data.currentPage = 1;
          getList();
        }
      } else {
        data.providerName = val.label;
        if (data.switchOnOff) {
          if (data.name != '') {
            aiFilter()
          } else {
            getList();
          }
        } else {
          getList();
        }
      }
    };

    const deleteSelect = (val, index, type) => {
      if (type == "provider") {
        data.providerName = "";
        data.currentPage = 1;
        if (data.switchOnOff) {
          if (data.name != '') {
            aiFilter()
          } else {
            getList();
          }
        } else {
          getList();
        }
      } else {
        if (type == "pro") {
          if (index == 1) {
            data.providerSelect = [data.providerSelect[0]];
            data.selectListOld = [data.selectListOld[0]];
          } else if (index == 0) {
            data.providerSelect = [];
            data.selectListOld = [];
            data.activeKey = "";
            data.showLast = false;
          }
        }
        data.selectList.splice(index, 1);
        data.selectListNew.splice(index, 1);
        data.labelIdlist = data.selectListNew.join(",");
        data.currentPage = 1;
        if (data.switchOnOff) {
          if (data.name != '') {
            aiFilter()
          } else {
            getList();
          }
        } else {
          getList();
        }
      }
    };
    const showMore = (type, index) => {
      if (type == "provider") {
        data.showScense = type;
        data.morePro = false;
      } else {
        data.showIndex = index;
        data.showScense = type;
        data.showLast = true;
      }
      console.log(data.showIndex);
    };
    const showless = (type, index) => {
      if (type == "provider_less") {
        data.showScense = type;
        data.morePro = true;
      } else {
        data.showIndex = "";
        data.showScense = type;
        data.showLast = false;
        console.log(index, `index`, data.showIndex);
      }
    };
    getTarde();

    //AI结果分页
    const getAIPageList = () => {
      let firstPageNum = (data.currentPage - 1) * data.pageItemSize;
      let lastPageNum = data.currentPage * data.pageItemSize;
      let industryId = data.selectListOld.length == 3
        ? data.selectListOld[2]
        : data.selectListOld.length == 2
          ? data.selectListOld[1]
          : data.selectListOld.length == 1
            ? data.selectListOld[0]
            : ""
      data.tableList = data.tableAIAllList.slice(firstPageNum, lastPageNum);
    };

    const getAIList = () => {
      if (data.name == "") {
        data.showPagination = true;
        getList();
        return false;
      }

      if (data.providerSelect.length == 1) {
        data.vocationList.splice(1, 1);
      } else if (data.providerSelect.length >= 2) {
        if (data.vocationList[2].level) {
          data.vocationList.splice(2, 1);
        }
        data.vocationList.splice(1, 1);
      } else { }
      data.providerName = ''
      data.activeKey = "";
      data.showId = '';
      data.showScense = "";
      data.showIndex = '';
      data.showLast = false;
      data.labelIdlist = [];
      data.providerSelect = [];
      data.selectList = [];
      data.selectListNew = [];
      data.selectListOld = [];

      data.loadingShow = true;
      data.AIsearch = true;
      let params = {
        question: data.name,
        type: data.AISearchType,
        limitedType: data.contentType,
      }
      // if (data.sourceType == '1') {
      //   params = {
      //     question: data.name,
      //     type: data.AISearchType,
      //     limitedType: data.contentType,
      //   }
      // } else if (data.sourceType == '2') {
      //   params = {
      //     question: data.name,
      //     type: data.AISearchType,
      //   }
      // }
      AISearch(params).then((res) => {
        data.loadingShow = false;
        data.AIsearch = false;
        if (res.code == 200) {
          data.tableList = [];
          data.tableAIAllList = [];
          data.tableAIAllList = res.data;
          data.showPagination = false;
          // if (data.sourceType === "1") {
          // 	if(res.data.resultType == "0"){
          //     for (let i of res.data.solutionList) {
          //      	for (let x of i.priority) {
          //      		for (let j of i.solution) {
          //      			if (j.id == x.id) {
          //      				data.tableAIAllList.push(j)
          //      			}
          //      		}
          //      	}
          //     }
          data.tableAIAllList.forEach((item) => {
            if (item.labelName) item.labelName = item.labelName.split(",");
            if (item.provider) item.provider = item.provider.split("/")[1];
          });
          data.totalItemCount = data.tableAIAllList.length;
          getAIPageList()
          // data.tableList = data.tableAIAllList.slice(0, 10);
          // }
          // } else if(data.sourceType === "2"){
          // 	if(res.data.resultType == "0"){
          //     for (let i of res.data.solutionModelList) {
          //      	for (let x of i.priority) {
          //      		for (let j of i.scene) {
          //      			if (j.id == x.id) {
          //      				data.tableAIAllList.push({
          //                ...j,
          //                classify:1
          //              })
          //      			}
          //      		}
          //      	}
          //     }
          //     data.tableAIAllList.forEach((item) => {
          //       item.description = item.summary;
          //       item.logo = item.image;
          //     });
          //     data.totalItemCount = data.tableAIAllList.length;
          //     data.tableList = data.tableAIAllList.slice(0,10);
          // 	}
          // }
        }
      });
    };
    // eventBus.on("solutionAIRefresh", getAIList);
    const refreshList = () => {
      console.log(`刷新刷新`, data.switchOnOff);

      if (!data.switchOnOff) {
        getList();
      } else {
        getAIList();
      }
    };
    eventBus.on("solutionAllRefresh", refreshList);
    const toggleShowLabel = () => {
      data.showLabel = !data.showLabel;
    };
    // 语音输入
    const handleAudio = (audioBlob) => {
      const formData = new FormData();
      formData.append("file", audioBlob, "recording.wav"); // 上传文件
      // console.log("ssssssss", formData);
      // 调用 AIvoice 函数并传递音频数据
      data.isTranslating = true;
      data.canBtnUse = true;
      AIvoice(formData).then((res) => {
        data.isTranslating = false;
        data.canBtnUse = false;
        console.log(res);
        if (res.code == 200) {
          data.name = res.msg;
          // seekContent()
        }
      });
    };
    const backgroundStyles = () => {
      return {
        backgroundImage: `url(${data.backgroundImage})`, // 使用模板字符串来插入变量
        backgroundRepeat: "no-repeat",
        backgroundSize: "cover",
      };
    };

    const getBoxTitle = (key) => {
      const rect = box.value[key].getBoundingClientRect();
      const selectHtml = selectData.value.getBoundingClientRect();
      return {
        left: rect.x - selectHtml.x + "px",
        top: rect.y - selectHtml.y + "px",
      };
    };

    const getBoxLeft = (key) => {
      const rect = box.value[key].getBoundingClientRect();
      const firstRect = box.value[0].getBoundingClientRect();
      const selectHtml = selectData.value.getBoundingClientRect();
      return {
        left: firstRect.x - selectHtml.x + "px",
        top: rect.y - selectHtml.y + 49 + "px",
      };
    };

    const counterStore = useHomeStore();
    const changeContent = () => {
      if (data.newContentName == "手动定制") {
        data.newContentName = "方案查询";
      } else {
        data.newContentName = "手动定制";
      }
    };

    //生态合作方搜索
    const searchChange = debounce(function () {
      if (data.providerSelect.length == 1) {
        data.vocationList.splice(1, 1);
      } else if (data.providerSelect.length >= 2) {
        if (data.vocationList[2].level) {
          data.vocationList.splice(2, 1);
        }
        data.vocationList.splice(1, 1);
      } else { }
      data.activeKey = "";
      data.showLast = false;
      data.showId = "";
      data.name = "";
      data.providerName = ""
      data.labelIdlist = [];
      data.providerSelect = [];
      data.selectList = [];
      data.selectListNew = [];
      data.selectListOld = [];
      getSearchRes(data.searchContent.label);
    }, 1000);
    function debounce(fn, delay) {
      let time = null;
      return function () {
        if (time) {
          clearTimeout(time);
        }
        time = setTimeout(() => {
          fn.call(this);
        }, 600);
      };
    }
    const getSearchRes = (val) => {
      let submitData = {
        name: val,
      };
      if (val == "") {
        searchList.value = [];
        data.searchContent.id = "";
        data.currentPage = 1;
        getList();
      } else {
        getSearchList(submitData).then((res) => {
          if (res.code == 200) {
            searchList.value = res.data.slice(0, 50);
            searchShow.value = true;
          }
        });
      }
    };
    const setName = (value) => {
      data.searchContent.label = value.name;
      data.searchContent.id = value.id;
      data.currentPage = 1;
      getList();
    };
    const clickBlur = () => {
      searchShow.value = false;
    };
    const clickOn = () => {
      searchShow.value = true;
    };
    const chooseContentType = (type) => {
      data.contentType = type;
      // if (data.name) {
      //   seekContent()
      // }
    };
    // 鼠标悬浮事件处理
    const handleMouseEnter = () => {
      if (data.sourceType == '1' && data.switchOnOff) {
        if (data.contentType == 'K') {
          data.searchText = '搜方案';
        } else {
          data.searchText = '搜生态';
        }
        data.showEcoSearch = true;
        data.showDownIcon = false;
        data.isHover = true;
      }
    };

    const handleMouseLeave = () => {
      data.searchText = '搜索';
      data.showEcoSearch = false;
      data.showDownIcon = true;
      data.isHover = false;
    };
    return {
      ...toRefs(data),
      clickBlur,
      clickOn,
      searchShow,
      searchList,
      setName,
      debounce,
      getSearchRes,
      searchChange,
      box,
      selectData,
      vocation,
      labelSelect,
      backgroundStyles,
      getBoxLeft,
      getBoxTitle,
      counterStore,
      toggleShowLabel,
      providerBtn,
      showMore,
      showless,
      deleteSelect,
      tabChange,
      contentColor,
      sizeChange,
      contentLeave,
      add,
      proDetail,
      router,
      pageChange,
      zhCN,
      seekContent,
      baseURL,
      labelChange,
      getAIList,
      refreshList,
      handleAudio,
      changeContent,
      providerClick,
      providerEnter,
      providerLeave,
      getHotProgramList,
      providerNewBtn,
      providerNewNewBtn,
      providerNewNewNewBtn,
      chooseContentType,
      handleMouseEnter,
      handleMouseLeave,
      changeSearch,
    };
  },
});
</script>

<style lang="scss" scoped src="./tableList.scss"></style>

<style lang="scss">
.ant-select:not(.ant-select-customize-input) .ant-select-selector {
  border: none;
}

:deep(.ant-cascader-input.ant-input) {
  border: none !important;
}

// 搜生态按钮悬浮效果
.ecoSearchBtn {
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(2, 191, 216, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
}

.mypage {
  .ant-pagination {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-select-selector {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-pagination-item-active {
    background: #007eff;
  }

  .ant-pagination-item-active a {
    color: #ffffff;
  }

  .ant-pagination-item-active:focus a,
  .ant-pagination-item-active:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-active a:focus,
  .ant-pagination-item-active a:hover {
    color: #ffffff;
  }

  .ant-pagination-item:focus,
  .ant-pagination-item:hover {
    background: #007eff;
  }

  .ant-pagination-item:focus a,
  .ant-pagination-item:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-disabled:hover,
  .ant-pagination-item-disabled:focus {
    background-color: #ffffff;
    border-color: #d9d9d9;

    a {
      color: rgba(0, 0, 0, 0.25);
    }
  }

  .ant-pagination-options-size-changer.ant-select {
    margin-right: 0;
  }

  .ant-pagination-options-quick-jumper {
    margin-left: 8px;
  }
}

.AISearchLogo {
  width: 190px;
  height: 80px;

  img {
    width: 100%;
    height: 100%;
  }
}

.chooseSearchType {
  background: #ffffff;
  height: 56px;

  .border_blue {
    border: 2px solid #848EFD;
  }

  .border_green {
    border: 2px solid #72F7FF;
  }

  .chooseContent {
    width: 164px;
    height: 32px;
    border-radius: 32px;
    margin-right: 17px;
    padding: 4px;
    font-size: 14px;

    .solutionBox {
      width: 76px;
      height: 24px;
      border-radius: 24px;
    }

    .shengtaiBox {
      width: 76px;
      height: 24px;
      border-radius: 24px;
    }

    .choosedWord {
      color: #ffffff;
    }

    .nochoosedWord {
      color: #A2ABB5;
    }

    .solutionBg {
      background: linear-gradient(108deg, #4446FF 0%, #4173FF 50%, #3EB8FF 100%);
    }

    .shengtaiBg {
      background: linear-gradient(108deg, #02BFE1 0%, #0AD1D7 50%, #6CEBF5 100%);
    }
  }
}
</style>
