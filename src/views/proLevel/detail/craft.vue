<template>
  <div class="markInfo">
    <span class="left margin_r_24"></span>
    商客市场信息
    <span class="right margin_l_24"></span>
  </div>

  <div class="anchors">
    <a-anchor
      direction="horizontal"
      :affix="false"
      v-for="(item, key) in anchorList"
      :key="key"
      @click="handleClick"
    >
      <a-anchor-link
        :class="{ currentActive: isActive === key }"
        @click="change(key)"
        :href="item.href"
        :title="item.title"
      />
    </a-anchor>
  </div>

  <div id="#sortManage">
    <div class="app_content">
      <img
        src="@/assets/images/solution/detail/leftIcon.png"
        style="width: 33px; height: 22px"
        alt=""
      />
      <div class="tit">分类管理</div>
      <img
        src="@/assets/images/solution/detail/rightIcon.png"
        style="width: 33px; height: 22px"
        alt=""
      />
    </div>
    <div class="classify">
      <div class="general" style="margin-right: 80px">
        <div class="text">产品分类</div>
        <div class="word">{{ detailData.classifyName }}</div>
      </div>

      <div class="midst">
        <div class="label">
          <div class="text">功能标签</div>
          <div class="word">{{ detailData.labelName }}</div>
        </div>
      </div>
      <div class="general" style="margin-left: 80px">
        <div class="text">需求标签</div>
        <div class="word">{{ detailData.typeName }}</div>
      </div>
    </div>
  </div>

  <div id="#typeList">
    <div class="app_content">
      <img
        src="@/assets/images/solution/detail/leftIcon.png"
        style="width: 33px; height: 22px"
        alt=""
      />
      <div class="tit">交付形态</div>
      <img
        src="@/assets/images/solution/detail/rightIcon.png"
        style="width: 33px; height: 22px"
        alt=""
      />
    </div>
    <div
      class="cards"
      v-if="detailData.deliveryList && detailData.deliveryList.length > 0"
    >
      <div
        class="item_card"
        v-for="(item, key) in detailData.deliveryList"
        :key="key + 1"
      >
        <img
          v-if="item.cover && item.cover != ''"
          v-lazy="`${item.cover}`"
          alt=""
          class="img"
        />
        <img
          v-else
          src="@/assets/images/ability/adlityDetail/apply.png"
          class="img"
        />
        <p class="title">{{ item.name }}</p>
        <p class="desc">
          <a-tooltip overlayClassName="tooltip_class">
            <template v-if="isShowToolTip(item.description, 70)" #title>
              {{ item.description }}</template
            >
            {{ item.description }}
          </a-tooltip>
        </p>
      </div>
    </div>
  </div>

  <div id="#expenses">
    <div class="app_content">
      <img
        src="@/assets/images/solution/detail/leftIcon.png"
        style="width: 33px; height: 22px"
        alt=""
      />
      <div class="tit">资费套餐</div>
      <img
        src="@/assets/images/solution/detail/rightIcon.png"
        style="width: 33px; height: 22px"
        alt=""
      />
    </div>
    <div class="combo" v-if="showTariff">
      <template v-for="(item, index) in detailData.tariffList" :key="index">
        <div class="proHead">
          <div class="proText"></div>
          <div class="proText">产品名称</div>
          <div class="proText">产品资费</div>
        </div>

        <div class="proContent">
          <div class="matter">
            <div style="width: 33%">
              <div class="package">{{ item.name }}</div>
            </div>
            <div class="packName">{{ item.specification }}</div>
            <div class="packCmmb">
              <span class="money">{{ item.tariff }}</span>
              <span class="month">{{ item.unit }}</span>
            </div>
          </div>
        </div>
      </template>
    </div>
    <div v-else style="width: 100%">
      <div class="tariff_none">
        <span class="con">根据客户定制需求，按实际工作量报价收费</span>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, watch } from "vue";
import { isShowToolTip } from "@/utils/index.js";

export default defineComponent({
  props: {
    craftData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  setup(props, { emit }) {
    const data = reactive({
      activeKey: "1",
      loadShow: false,
      detailData: {},
      isActive: 0,
      showTariff: false,
      currentAnchor: "#sortManage",
      anchorList: [
        {
          key: "sortManage",
          href: "#sortManage",
          title: "分类管理",
        },
        {
          key: "typeList",
          href: "#typeList",
          title: "交付形态",
        },
        {
          key: "expenses",
          href: "#expenses",
          title: "资费套餐",
        },
      ],
    });

    watch(
      () => props.craftData,
      (val) => {
        if (val) {
          data.detailData = val;
          if (
            val.tariffList &&
            val.tariffList.length > 0 &&
            val.tariffList[0].name &&
            val.tariffList[0].name != ""
          ) {
            data.showTariff = true;
          } else {
            data.showTariff = false;
          }
        }
      },
      { immediate: true }
    );

    const handleClick = (e, link) => {
      const href = link.href.replace("#", "");
      e.preventDefault();
      data.currentAnchor = "#" + href;
      let srcolls = document.getElementById(link.href);
      srcolls &&
        srcolls.scrollIntoView({
          block: "center",
          behavior: "smooth",
        });
    };

    const change = (v) => {
      data.isActive = v;
    };

    return {
      ...toRefs(data),
      handleClick,
      change,
      isShowToolTip,
    };
  },
});
</script>

<style lang="scss" scoped>
@import "./index.scss";
.markInfo {
  font-weight: bold;
  font-size: 28px;
  color: #24456a;
  line-height: 33px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;

  .left {
    width: 48px;
    height: 2px;
    background: linear-gradient(90deg, rgba(36, 69, 106, 0) 0%, #24456a 100%);
  }
  .right {
    width: 48px;
    height: 2px;
    background: linear-gradient(-90deg, rgba(36, 69, 106, 0) 0%, #24456a 100%);
  }
}
.list {
  padding-inline-start: 0;
  list-style-type: none;
  margin: 24px auto;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;

  li {
    width: 48%;
    display: inline-block;
    background: linear-gradient(180deg, #edf0f9 0%, #fefeff 100%);
    box-shadow: 0px 4px 24px 0px #eaedf3;
    border-radius: 10px 10px 10px 10px;
    border: 2px solid #ffffff;
    padding: 4px 24px 4px 24px;
    margin-bottom: 24px;

    img {
      display: inline-block;
      width: 72px;
      height: 72px;
    }

    p {
      display: inline-block;
      font-weight: 500;
      font-size: 16px;
      color: #2e3852;
      line-height: 28px;
    }

    .left_box {
      display: flex;
      padding-top: 19px;
    }
  }

  .li_box {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  li:nth-of-type(odd) {
    margin-right: 24px;
  }

  .fileText {
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }
}

.app_content {
  text-align: center;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 48px;

  .tit {
    font-weight: bold;
    font-size: 24px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 28px;
    display: inline-block;
    margin-left: 6px;
    margin-right: 6px;
  }

  img {
    width: 33px;
    height: 22px;
  }

  .left {
    display: inline-block;
    width: 24px;
    height: 24px;
    background-size: contain;
    position: relative;
    top: 3px;
  }

  .right {
    display: inline-block;
    width: 24px;
    height: 24px;
    background-size: contain;
    position: relative;
    top: 3px;
  }
}

.classify {
  background-color: #ffffff;
  margin-top: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 20px;
  padding-bottom: 60px;

  .midst {
    background-image: url(@/assets/images/product/precinct/label.png);
    width: 278px;
    height: 309px;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: relative;
    .label {
      width: 240px;
      height: 114px;
      background: linear-gradient(
        163deg,
        #f8fbff 0%,
        #f8fbff 38%,
        #ffffff 100%
      );
      box-shadow: 8px 8px 12px 0px rgba(0, 0, 0, 0.04),
        -8px -8px 12px 0px rgba(255, 255, 255, 0.5);
      border-radius: 100px 100px 100px 100px;
      border: 1px solid #ffffff;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      position: absolute;
      bottom: -30px;
      left: 20px;

      .text {
        font-size: 16px;
        color: #2e3852;
      }
      .word {
        font-weight: bold;
        font-size: 20px;
        color: rgba(0, 0, 0, 0.85);
      }
    }
  }

  .general {
    width: 240px;
    height: 114px;
    background: linear-gradient(163deg, #f8fbff 0%, #f8fbff 38%, #ffffff 100%);
    box-shadow: 8px 8px 12px 0px rgba(0, 0, 0, 0.04),
      -8px -8px 12px 0px rgba(255, 255, 255, 0.5);
    border-radius: 100px 100px 100px 100px;
    border: 1px solid #ffffff;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .text {
      font-size: 16px;
      color: #2e3852;
    }
    .word {
      font-weight: bold;
      font-size: 20px;
      color: rgba(0, 0, 0, 0.85);
    }
  }
}
.cards {
  margin-top: 24px;
  display: flex;
  justify-content: start;
  flex-wrap: wrap;

  .item_card {
    width: 374px;
    padding-bottom: 12px;
    background: linear-gradient(163deg, #f1f3f6 0%, #f6f7f9 38%, #ffffff 100%);
    box-shadow: 8px 8px 12px 0px rgba(0, 0, 0, 0.04),
      -8px -8px 12px 0px rgba(255, 255, 255, 0.5);
    border-radius: 0px 0px 0px 0px;
    border: 2px solid #ffffff;
    margin-bottom: 12px;
    margin-right: 39px;

    p {
      margin-bottom: 6px;
      padding: 0 12px;
    }

    img {
      width: 100%;
      height: 176px;
    }

    .title {
      margin-top: 12px;
      font-weight: bold;
      font-size: 20px;
      color: #2e3852;
    }

    .desc {
      font-weight: 400;
      font-size: 16px !important;
      color: rgba(46, 56, 82, 0.85);
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      /* 控制显示的行数 */
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
    }
  }

  .item_card:nth-child(3n) {
    margin-right: 0;
  }
}
.combo {
  margin-top: 50px;
  margin-bottom: 30px;
  background: #fbfcfc;
  box-shadow: 8px 8px 12px 0px rgba(0, 0, 0, 0.04),
    -8px -8px 12px 0px rgba(255, 255, 255, 0.5);
  border: 1px solid #ffffff;

  .proHead {
    display: flex;
    margin: 0 48px;
  }
  .proText {
    width: 33%;
    font-weight: 500;
    font-size: 20px;
    color: rgba(46, 56, 82, 0.65);
    text-align: center;
    padding-top: 18px;
  }
  .proContent {
    background: #ffffff;
    border-radius: 16px 16px 16px 16px;
    margin: 20px;
    padding: 20px 0 8px 0;

    .matter {
      margin: 0 48px;
      height: 64px;
      background: #f8fbff;
      border-radius: 32px;
      display: flex;
      align-items: center;
      margin-bottom: 12px;
    }
    .package {
      width: 264px;
      height: 64px;
      background: linear-gradient(180deg, #8db7ff 0%, #65aeff 100%);
      box-shadow: 8px 8px 12px 0px rgba(0, 0, 0, 0.04),
        -8px -8px 12px 0px rgba(255, 255, 255, 0.5);
      border-radius: 32px 32px 32px 32px;
      border: 1px solid #ffffff;
      font-weight: bold;
      font-size: 22px;
      color: #ffffff;
      text-align: center;
      line-height: 64px;
    }
    .packName {
      font-weight: 500;
      font-size: 20px;
      color: #2e3852;
      width: 33%;
      text-align: center;
    }
    .packCmmb {
      width: 33%;
      text-align: center;
      .money {
        font-weight: bold;
        font-size: 20px;
        color: #236cff;
      }
      .month {
        font-weight: 500;
        font-size: 14px;
        color: #2e3852;
      }
    }
  }
}
</style>
