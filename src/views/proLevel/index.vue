<template>
  <div class="topImage">
    <div class="topImage_content">
      <div class="topImage_title">{{ formData.name }}</div>
      <div class="topImage_details">
        <a-tooltip overlayClassName="tooltip_class">
          <template v-if="isShowToolTip(formData.introduce, 127)" #title>
            {{ formData.introduce }}
          </template>
          {{ formData.introduce }}
        </a-tooltip>
      </div>
    </div>

    <div
      class="flex just-sb align-center"
      style="margin: 43px 27px 0 0"
      v-if="cloudShow && zoneId != 17"
    >
      <template v-for="(item, index) in projectList" :key="index">
        <div
          class="cardBac"
          :style="{ backgroundImage: `url('${item.backgroundImageUrl}')` }"
        >
          <div class="card_dec">
            <p
              style="
                font-family: DIN, DIN;
                font-weight: bold;
                text-align: center;
                font-size: 20px;
                color: rgba(0, 0, 0, 0.65);
                margin: 0 6px 0 8px;
              "
            >
              {{ item.num }}
            </p>
            <div
              style="
                font-weight: 400;
                font-size: 14px;
                color: rgba(0, 0, 0, 0.45);
                display: flex;
                align-items: center;
                margin-top: 11px;
              "
            >
              <p>{{ item.title }}</p>
            </div>
          </div>
          <div style="margin-right: 24px">
            <img :src="item.image" style="width: 88px; height: 88px" />
            <div v-show="!item.rate" style="margin-top: 8px">&nbsp</div>
          </div>
        </div>
      </template>
    </div>
  </div>

  <div class="totaoText" v-if="cloudShow">
    <span @click="tabChange('2')" :class="{ activeBtn: sourceType === '2' }">
      {{formData.name}}产品
    </span>
    <span
      @click="tabChange('1')"
      :class="{ activeBtn: sourceType === '1' }"
      v-if="totalItemCount != 0 && zoneId != 17"
    >
      {{formData.name}}场景
    </span>
  </div>
  <div v-show="sourceType === '1'">
    <user-scene />
  </div>
  <div v-show="sourceType === '2'">
    <product-list />
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, watch } from "vue";
import { isShowToolTip } from "@/utils/index.js";
import backgroundImageUrl1 from "@/assets/images/home/<USER>";
import backgroundImageUrl2 from "@/assets/images/home/<USER>";
import backgroundImageUrl3 from "@/assets/images/home/<USER>";
import backgroundImageUrl4 from "@/assets/images/home/<USER>";
import image5 from "@/assets/images/product/home.png";
import productList from "./components/productList.vue";
import userScene from "./components/userScene.vue";
import { getDetailList, zoneView } from "@/api/prefecture/home";
import { useRoute } from "vue-router";
import { getSceneList } from "@/api/scenario/home";

export default defineComponent({
  components: {
    productList,
    userScene,
  },
  name: "solveNew",
  setup() {
    const route = useRoute();
    const data = reactive({
    	zoneId: route.query.zoneId,
      formData: {},
      projectList: [
        {
          num: "",
          title: "用户场景上架数",
          backgroundImageUrl: backgroundImageUrl1,
          image: image5,
        },

        {
          num: "",
          title: "专区产品上架数",
          backgroundImageUrl: backgroundImageUrl2,
          image: image5,
        },
        {
          num: "",
          title: "用户场景构建数",
          backgroundImageUrl: backgroundImageUrl3,
          image: image5,
        },
        {
          num: "",
          title: "专区产品构建数",
          backgroundImageUrl: backgroundImageUrl4,
          image: image5,
        },
      ],
      sourceType: "2",
      totalItemCount: 0,
      currentPage: 1,
      pageItemSize: 10,
      cloudShow: true,
    });

    const tabChange = (type) => {
      data.sourceType = type;
    };

    watch(
      () => route.query.appMarket,
      (val) => {
        if (val) {
          let params;
          if (val == 4) {
            data.cloudShow = false;
            data.sourceType = "2";
          } else {
            data.cloudShow = true;
          }
          if (val == 5) {
            params = {
              applicationMarket: val,
              zoneId: route.query.zoneId,
            };
          } else {
            params = {
              applicationMarket: val,
            };
          }
          zoneView(params).then((res) => {
            data.projectList[0].num = res.data.sceneShelfCount;
            data.projectList[1].num = res.data.productShelfCount;
            data.projectList[2].num = res.data.sceneCustomCount;
            data.projectList[3].num = res.data.productCustomCount;
          });
          let pageParams = {
            pageNo: data.currentPage,
            pageSize: data.pageItemSize,
            applicationMarkets: val,
          };
          getSceneList(pageParams).then((res) => {
            data.totalItemCount = res.data.totalRows;
          });
        }
      },
      { immediate: true }
    );

    watch(
      () => route.query.zoneId,
      (val) => {
        if (val) {
        	data.zoneId = val;
          getDetailList(val).then((res) => {
            data.formData = res.data;
          });
        }
      },
      { immediate: true }
    );
    watch(
      () => route.query.activeNum,
      (val) => {
        if (val) {
          data.sourceType = val;
        } else {
          data.sourceType = "2";
        }
      },
      { immediate: true }
    );

    return {
      ...toRefs(data),
      isShowToolTip,
      tabChange,
    };
  },
});
</script>

<style lang="scss" scoped>
.topImage {
  width: 1200px;
  margin: 0 auto;
  /*height: 320px;*/
  background-image: url("@/assets/images/prefecture/levelBac.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;

  .topImage_content {
    padding-top: 63px;

    .topImage_title {
      font-weight: bold;
      font-size: 42px;
      color: #122c6c;
    }

    .topImage_details {
      width: 1020px;
      font-size: 16px;
      color: #2b3f66;
      line-height: 32px;
      margin-top: 16px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .cardBac:nth-child(1) {
    margin-left: -18px;
  }

  .cardBac:nth-child(4) {
    margin-right: -45px;
  }
  .cardBac {
    flex: 1;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: space-around;

    .card_num {
      display: flex;
      align-items: flex-start;
    }
    .card_dec {
      margin: 24px 0 24px 24px;
      .dec_num {
        font-size: 40px;
        font-weight: bold;
        font-family: DIN, DIN;
        color: #2e7fff;
        text-shadow: 0px 4px 6px rgba(46, 127, 255, 0.25);
      }

      .dec_box {
        background-color: #ff5b00;
        border-radius: 50%;
        width: 18px;
        height: 18px;
        font-weight: 500;
        font-size: 12px;
        color: #ffffff;
        text-align: center;
        line-height: 18px;
      }
    }
  }
}

.totaoText {
  font-weight: bold;
  font-size: 28px;
  color: rgba(0, 0, 0, 0.85);
  text-align: center;
  margin-top: 80px;
  padding-bottom: 32px;
  margin-left: 118px;

  span {
    cursor: pointer;
    margin-right: 96px;
    padding-bottom: 16px;
  }
  .activeBtn {
    color: #0c70eb;
    border-bottom: 3px solid #0c70eb;
  }
}
</style>
