<template>
    <div>
        <div class="text">
            您今日下载附件量已超过上限，如果继续下载需提交工单审核。
        </div>
        <div class="btns flex just-center margin_t_24">
            <a-button class="margin_r_20" @click="cancel()">
                取消
            </a-button>
            <a-button type="primary" @click="confirm()">
                确定
            </a-button>
        </div>
    </div>

</template>

<script>
import { defineComponent, reactive, toRefs, ref, computed } from "vue";
export default defineComponent({
    name: "promptBox",
    emits: ["downloadModalCancel", "downloadModalConfirm"],
    components: {
    },
    setup(props, { emit }) {
        const data = reactive({
        });
        // 取消
        const cancel = () => {
            console.log('取消')
            emit("downloadModalCancel")
        }
        // 确认
        const confirm = () => {
            console.log('确定')
            emit("downloadModalConfirm")
        }
        return {
            ...toRefs(data),
            cancel,
            confirm,
        };
    },
});
</script>

<style lang="scss" scoped>
.text {
    font-size: 16px;
}
</style>