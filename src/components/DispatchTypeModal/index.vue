<template>
  <a-modal
    :visible="modelValue"
    :mask-closable="false"
    :footer="null"
    :destroyOnClose="true"
    width="700px"
    @cancel="handleCancel"
    @close="handleCancel"
    class="dispatch-modal"
  >
    <template #title>
      <div class="custom-modal-title">
        <img
          src="../../assets/apply_icon.png"
          alt="调度类型"
          class="title-icon"
          style="width: 14px; margin-right: 10px"
        />
        <span style="color: #24456a">选择调度工单类型</span>
      </div>
    </template>
    <div class="dispatch-type-container">
      <div
        class="dispatch-type-item"
        :class="{ active: selectedType === '售前调度' }"
        @click="handleSelect('售前调度')"
      >
        <img
          :src="selectedType === '售前调度' ? selectedLeftIcon : selectLeftIcon"
          alt="售前调度"
        />
        <div class="dispatch-type-text">售前调度</div>
      </div>
      <div
        class="dispatch-type-item"
        :class="{ active: selectedType === '售中调度' }"
        @click="handleSelect('售中调度')"
      >
        <img
          :src="
            selectedType === '售中调度' ? selectedCenterIcon : selectCenterIcon
          "
          alt="售中调度"
        />
        <div class="dispatch-type-text">售中调度</div>
      </div>
      <div
        class="dispatch-type-item"
        :class="{ active: selectedType === '售后调度' }"
        @click="handleSelect('售后调度')"
      >
        <img
          :src="
            selectedType === '售后调度' ? selectedRightIcon : selectRightIcon
          "
          alt="售后调度"
        />
        <div class="dispatch-type-text">售后调度</div>
      </div>
    </div>
    <!-- <div class="dispatch-action">
      <a-button @click="handleCancel">取消</a-button>
      <a-button class="submit" @click="handleConfirm">确定</a-button>
    </div> -->
  </a-modal>
</template>

<script setup>
import { ref, watch } from "vue";
import { message } from "ant-design-vue";

// 添加图片导入
const selectLeftIcon = new URL(
  "../../assets/images/Coordination/select_left.png.png",
  import.meta.url
).href;
const selectedLeftIcon = new URL(
  "../../assets/images/Coordination/selected_left.png",
  import.meta.url
).href;
const selectCenterIcon = new URL(
  "../../assets/images/Coordination/select_center.png",
  import.meta.url
).href;
const selectedCenterIcon = new URL(
  "../../assets/images/Coordination/selected_center.png",
  import.meta.url
).href;
const selectRightIcon = new URL(
  "../../assets/images/Coordination/select_right.png",
  import.meta.url
).href;
const selectedRightIcon = new URL(
  "../../assets/images/Coordination/selected_right.png",
  import.meta.url
).href;

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
    default: false,
  },
});

const emit = defineEmits(["update:modelValue", "select"]);

const selectedType = ref("");

const handleSelect = (type) => {
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));

  if (
    type === "售中调度" &&
    !userInfo.roleKeyList.includes("deliveryManager")
  ) {
    message.warning("目前售中调度工单仅支持交付经理发起");
    return;
  }

  if (type === "售后调度") {
    message.warning("售后调度工单暂未开放");
    return;
  }

  selectedType.value = type;
  setTimeout(() => {
    handleConfirm();
  }, 100);
};

const handleConfirm = () => {
  if (!selectedType.value) {
    message.warning("请选择调度工单类型");
    return;
  }

  emit("select", selectedType.value);
  emit("update:modelValue", false);
};
const localVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});
const handleCancel = () => {
  selectedType.value = "";
  console.log("handleCancel");
  localVisible.value = false; // 使用 computed 的 setter
  // 如果仍不生效，尝试强制更新（备用方案）
  setTimeout(() => {
    localVisible.value = false;
  }, 100);
};

watch(
  () => props.modelValue,
  (newVal) => {
    if (!newVal) {
      selectedType.value = "";
    }
  }
);
</script>

<style lang="scss" scoped>
.dispatch-modal {
  :deep(.ant-modal-content) {
    border-radius: 8px;
    padding: 20px;
  }

  :deep(.ant-modal-header) {
    margin-bottom: 40px;
    padding: 0;
    border-bottom: none;

    .ant-modal-title {
      font-weight: 500;
      font-size: 16px;
      color: #000000;
      line-height: 24px;
    }
  }

  :deep(.ant-modal-close) {
    top: 20px;
    right: 20px;
  }

  .dispatch-type-container {
    display: flex;
    justify-content: space-between;
    padding: 0 40px;
    margin-bottom: 80px;

    .dispatch-type-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
      width: 140px;
      height: 140px;
      background: #ffffff;
      // border: 1px solid #e5e6eb;
      border-radius: 8px;
      padding: 20px;
      transition: all 0.3s;

      // &:hover {
        // border-color: #4086ff;
        // background: #f5f8ff;
      // }

      &.active {
        // border-color: #4086ff;
        // background: #f5f8ff;

        .dispatch-type-text {
          color: #4086ff;
        }
      }

      img {
        width: 120px;
        height: 120px;
        margin-bottom: 12px;
      }

      .dispatch-type-text {
        font-weight: 500;
        font-size: 22px;
        color: #24456a;
        text-align: center;
      }
    }
  }

  // .dispatch-action {
  //   display: flex;
  //   justify-content: center;
  //   gap: 12px;
  //   margin-top: 100px;
  //   .ant-btn {
  //     min-width: 80px;
  //     height: 32px;
  //     font-weight: 500;
  //     border: none;
  //     font-size: 16px;
  //     color: #0c70eb;
  //     line-height: 22px;
  //     border-radius: 4px;
  //     font-weight: 400;
  //     font-size: 14px;
  //     padding: 4px 16px;
  //     background: rgba(12, 112, 235, 0.08);
  //     border-radius: 4px 4px 4px 4px;
  //     &.ant-btn-primary {
  //       background: #4086ff;
  //       border-color: #4086ff;
  //       color: #ffffff;

  //       &:hover {
  //         background: #6aa1ff;
  //         border-color: #6aa1ff;
  //       }
  //     }

  //     &.ant-btn-default {
  //       color: #4e5969;
  //       // border-color: #e5e6eb;
  //       background: #ffffff;

  //       &:hover {
  //         color: #4086ff;
  //         border-color: #4086ff;
  //         background: #ffffff;
  //       }
  //     }
  //   }
  //   .submit {
  //     background: linear-gradient(270deg, #0142fd 0%, #2475f9 100%);
  //     border-radius: 4px 4px 4px 4px;
  //     font-weight: 500;
  //     font-size: 14px;
  //     color: #ffffff;
  //     line-height: 22px;
  //   }
  // }
}
</style>