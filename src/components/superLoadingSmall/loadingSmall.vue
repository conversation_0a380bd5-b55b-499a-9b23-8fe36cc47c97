<template>
  <div id="phrase_box1">
    <div>
      <div class="boxLoading">
        <span class="circle"></span>
        <span class="item">正在进行需求智能分析</span>
        <a-spin v-if="firstShow" :indicator="indicator"  size="small" />
        <img v-else src="../../assets/dui.svg" alt="" />
      </div>
      <div class="boxLoading" v-if="showSecond">
        <span class="circle"></span>
        <span class="item">正在进行数据智能匹配</span>
        <a-spin v-if="secondShow" :indicator="indicator"  size="small" />
        <img v-else src="../../assets/dui.svg" alt="" />
      </div>
      <div class="boxLoading" v-if="showLast">
        <span class="circle"></span>
        <span class="item">正在进行结果校验准备</span>
        <a-spin v-if="!lastShow" :indicator="indicator"  size="small" />
        <img v-else src="../../assets/dui.svg" alt="" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: ["showEnd"],
  data(props) {
    return {
      verticalSpacing: 30,
      phrases: [
        "正在进行需求智能分析",
        "正在进行数据智能匹配",
        "正在进行结果校验准备",
      ],
      firstShow: true,
      secondShow: true,
      thirdShow: false,
      showSecond: false,
      showLast: false,
      lastShow: false,
    };
  },
  mounted() {
    setTimeout(() => {
      this.firstShow = false;
      this.showSecond = true;
    }, 2000);

    setTimeout(() => {
      this.showLast = true;
      this.secondShow = false;
      this.checkSecondShow();
    }, 7000);
  },
  methods: {
    checkSecondShow() {
      if (!this.secondShow) {
        setTimeout(() => {
          this.lastShow = this.showEnd;
        }, 1000);
      }
    },
  },
};
</script>

<style>
.item {
  font-size: 14px !important;
  margin-right: 14px;
  line-height: 20px;
  margin-right: 10px;
  color: #595959;
  text-indent: 2em;
  margin-left: 8px;
}
.boxLoading {
  align-items: center;
  margin-bottom: 8px !important;
  height: 27px;
}
.boxLoading img {
  margin-bottom: 4px;
  width: 12px;
  height: 12px;
}
#phrase_box1 {
  padding-top: 15px;
  display: flex;
  flex-flow: column;
  /* height: 144px; */
  overflow: hidden;
  width: 1200px;
  background: #ffffff;
  margin-left: 8px;
  border-radius: 12px;
  padding-left: 24px;
  margin: 0 auto;
}

.close_load {
  animation: close 6s;
}

/* #phrases1 {
  -webkit-animation: slide-phrases-upward 60s;
  animation: close 3s;
} */
.circle {
	width: 3px;
	height: 3px;
	border-radius: 50%;
	background: #0C70EB;
	display: inline-block;
	margin-bottom: 4px;
}
@keyframes close {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}
</style>