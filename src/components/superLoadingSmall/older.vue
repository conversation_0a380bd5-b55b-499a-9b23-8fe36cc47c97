<template>
    <div id="phrase_box1">
      <div>
        <div class="boxLoading">
          <span class="circle"></span>
          <span class="item">正在进行需求智能分析</span>
          <a-spin v-if="firstShow" :indicator="indicator" size="small" />
          <img v-else src="../../assets/dui.svg" alt="" />
        </div>
        <div class="boxLoading" v-if="showSecond">
          <span class="circle"></span>
          <span class="item">{{ phrases[1] }}</span>
          <a-spin v-if="secondShow" :indicator="indicator" size="small" />
          <img v-else src="../../assets/dui.svg" alt="" />
        </div>
        <div class="boxLoading" v-if="showLast">
          <span class="circle"></span>
          <span class="item">{{ phrases[2] }}</span>
          <a-spin v-if="!lastShow" :indicator="indicator" size="small" />
          <img v-else src="../../assets/dui.svg" alt="" />
        </div>
      </div>
    </div>
  </template>
  
  <script>
  import { getStep } from "@/api/AI/ai.js";
  export default {
    props: ["showEnd"],
    data(props) {
      return {
        verticalSpacing: 30,
        phrases: ["正在进行需求智能分析", "", ""],
        firstShow: true,
        secondShow: true,
        thirdShow: false,
        showSecond: false,
        showLast: false,
        lastShow: false,
      };
    },
    mounted() {
      let str = localStorage.getItem("seekName");
      // setTimeout(() => {
      // this.firstShow = false;
      // this.showSecond = true;
      this.steWord(str);
      // }, 1000);
  
      // setTimeout(() => {
      //   this.showLast = true;
      //   this.secondShow = false;
      //   this.checkSecondShow();
      // }, 2000);
    },
    methods: {
      checkSecondShow() {
        if (!this.secondShow) {
          setTimeout(() => {
            this.lastShow = this.showEnd;
          }, 1000);
        }
      },
      steWord(step) {
        getStep({ question: step }).then((res) => {
          if (res.data.resultType == "0") {
            if (res.data.solution != null) {
              this.phrases[1] = "即将为您查询" + res.data.solution + "方案";
            }
            if (res.data.ability != null) {
              this.phrases[1] += "即将为您查询" + res.data.ability + "能力";
            }
            if (res.data.productBag != null) {
              this.phrases[1] += "即将为您查询" + res.data.productBag + "场景";
            }
          }
          if (res.data.resultType == "1") {
            this.phrases[1] = "正在为您查询" + res.data.question + "相关内容";
          }
          if (res.data.resultType == "4") {
            if (res.data.solution != null) {
              this.phrases[1] = "即将为你定制一个" + res.data.solution + "的方案";
              if (res.data.ability != null) {
                this.phrases[1] += "，包含" + res.data.ability + "的能力";
              }
              if (res.data.product != null) {
                this.phrases[1] += "，包含" + res.data.product + "的产品";
              }
            }
            // if (res.data.product != null) {
            //   this.phrases[1] = "即将为你定制一个" + res.data.product + "的方案";
            //   if (res.data.ability != null) {
            //     this.phrases[1] += "包含" + res.data.ability + "的能力";
            //   }
            // }
          }
          if (res.data.resultType == "7") {
            this.phrases[1] =
              "即将为你定制一个" + res.data.productBag + "的场景";
            if (res.data.product != null) {
              this.phrases[1] += "，包含" + res.data.product + "的产品";
            }
          }
          this.firstShow = false;
          this.showSecond = true;
          setTimeout(() => {
            if (res.data.resultType == "0") {
              if (res.data.solution != null) {
                this.phrases[2] = "正在为您匹配" + res.data.solution + "的方案";
              }
              if (res.data.ability != null) {
                this.phrases[2] = "正在为您匹配" + res.data.ability + "的能力";
              }
              if (res.data.productBag != null) {
                this.phrases[2] =
                  "正在为您匹配" + res.data.productBag + "的场景";
              }
            }
            if (res.data.resultType == "1") {
              this.phrases[2] = "正在为您匹配" + res.data.question + "相关内容";
            }
            if (res.data.resultType == "4") {
              if (res.data.solution != null) {
                this.phrases[2] = "正在为您匹配" + res.data.solution + "方案";
              }
            }
            if(res.data.resultType == '7'){
              if (res.data.productBag != null) {
                this.phrases[2] = "正在为您匹配" + res.data.productBag;
              }
            }
            this.showLast = true;
            this.secondShow = false;
            this.checkSecondShow();
          }, 500);
        });
      },
    },
  };
  </script>
  
  <style>
  .item {
    font-size: 14px !important;
    margin-right: 14px;
    line-height: 20px;
    margin-right: 10px;
    color: #595959;
    text-indent: 2em;
    margin-left: 8px;
  }
  .boxLoading {
    align-items: center;
    margin-bottom: 8px !important;
    height: 27px;
  }
  .boxLoading img {
    margin-bottom: 4px;
    width: 12px;
    height: 12px;
  }
  #phrase_box1 {
    padding-top: 15px;
    display: flex;
    flex-flow: column;
    /* height: 144px; */
    overflow: hidden;
    width: 1200px;
    background: #ffffff;
    margin-left: 8px;
    border-radius: 12px;
    padding-left: 24px;
    margin: 0 auto;
  }
  
  .close_load {
    animation: close 6s;
  }
  
  /* #phrases1 {
    -webkit-animation: slide-phrases-upward 60s;
    animation: close 3s;
  } */
  .circle {
    width: 3px;
    height: 3px;
    border-radius: 50%;
    background: #0c70eb;
    display: inline-block;
    margin-bottom: 4px;
  }
  @keyframes close {
    0% {
      opacity: 1;
    }
  
    50% {
      opacity: 1;
    }
  
    100% {
      opacity: 0;
    }
  }
  </style>